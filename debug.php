<?php
/**
 * Debug script to help diagnose routing issues
 * Upload this to your server and access it to see what's happening
 */

// Define the application root directory
define('APP_ROOT', __DIR__);

// Load configuration
require_once APP_ROOT . '/config/config.php';

echo "<h1>AI Doctor Debug Information</h1>";

echo "<h2>Environment Information</h2>";
echo "<strong>APP_URL:</strong> " . APP_URL . "<br>";
echo "<strong>Request URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "<br>";
echo "<strong>Request Method:</strong> " . ($_SERVER['REQUEST_METHOD'] ?? 'Not set') . "<br>";
echo "<strong>Server Name:</strong> " . ($_SERVER['SERVER_NAME'] ?? 'Not set') . "<br>";
echo "<strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Not set') . "<br>";
echo "<strong>Script Name:</strong> " . ($_SERVER['SCRIPT_NAME'] ?? 'Not set') . "<br>";

echo "<h2>File System Check</h2>";
echo "<strong>Current Directory:</strong> " . __DIR__ . "<br>";
echo "<strong>Index.php exists:</strong> " . (file_exists(__DIR__ . '/index.php') ? 'Yes' : 'No') . "<br>";
echo "<strong>Config exists:</strong> " . (file_exists(__DIR__ . '/config/config.php') ? 'Yes' : 'No') . "<br>";
echo "<strong>Routes exists:</strong> " . (file_exists(__DIR__ . '/config/routes.php') ? 'Yes' : 'No') . "<br>";
echo "<strong>Patients Controller exists:</strong> " . (file_exists(__DIR__ . '/App/Controllers/Patients.php') ? 'Yes' : 'No') . "<br>";
echo "<strong>Appointments Controller exists:</strong> " . (file_exists(__DIR__ . '/App/Controllers/Appointments.php') ? 'Yes' : 'No') . "<br>";

echo "<h2>Router Test</h2>";
try {
    // Load the autoloader / bootstrap
    require_once APP_ROOT . '/App/bootstrap.php';
    
    // Initialize the router
    $router = new \App\Core\Router();
    
    // Define routes
    require_once APP_ROOT . '/config/routes.php';
    
    echo "<strong>Router initialized successfully</strong><br>";
    
    // Test specific routes
    $testUrls = ['/patients', '/appointments', '/calendar', '/consultations'];
    
    foreach ($testUrls as $testUrl) {
        echo "<strong>Testing route '$testUrl':</strong> ";
        if ($router->match($testUrl)) {
            $params = $router->getParams();
            echo "MATCHED - Controller: " . ($params['controller'] ?? 'None') . ", Action: " . ($params['action'] ?? 'None') . "<br>";
        } else {
            echo "NOT MATCHED<br>";
        }
    }
    
} catch (Exception $e) {
    echo "<strong>Error:</strong> " . $e->getMessage() . "<br>";
    echo "<strong>Stack trace:</strong><br><pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>Class Loading Test</h2>";
try {
    if (class_exists('App\\Controllers\\Patients')) {
        echo "<strong>Patients Controller:</strong> Can be loaded<br>";
    } else {
        echo "<strong>Patients Controller:</strong> Cannot be loaded<br>";
    }
    
    if (class_exists('App\\Controllers\\Appointments')) {
        echo "<strong>Appointments Controller:</strong> Can be loaded<br>";
    } else {
        echo "<strong>Appointments Controller:</strong> Cannot be loaded<br>";
    }
} catch (Exception $e) {
    echo "<strong>Class loading error:</strong> " . $e->getMessage() . "<br>";
}

echo "<h2>URL Generation Test</h2>";
echo "<strong>base_url():</strong> " . base_url() . "<br>";
echo "<strong>base_url('patients'):</strong> " . base_url('patients') . "<br>";
echo "<strong>base_url('appointments'):</strong> " . base_url('appointments') . "<br>";

echo "<h2>.htaccess Check</h2>";
if (file_exists(__DIR__ . '/.htaccess')) {
    echo "<strong>.htaccess exists</strong><br>";
    echo "<pre>" . htmlspecialchars(file_get_contents(__DIR__ . '/.htaccess')) . "</pre>";
} else {
    echo "<strong>.htaccess does NOT exist - This is likely the problem!</strong><br>";
}

echo "<h2>PHP Info</h2>";
echo "<strong>PHP Version:</strong> " . PHP_VERSION . "<br>";
echo "<strong>Apache mod_rewrite:</strong> " . (function_exists('apache_get_modules') && in_array('mod_rewrite', apache_get_modules()) ? 'Enabled' : 'Unknown/Disabled') . "<br>";

?>
