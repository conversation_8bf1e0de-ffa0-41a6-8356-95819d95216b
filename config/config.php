<?php

/**
 * AI Doctor - Clinical Decision Support System
 * Configuration file
 */

// Define application root path if not already defined
if (!defined('APP_ROOT')) {
    define('APP_ROOT', dirname(__DIR__));
}

// Environment settings
define('DEBUG_MODE', true);
define('APP_NAME', 'AI Doctor (Vaccination & STD)');
define('APP_URL', 'https://vaccination-ai.allstackdev.tech');

// Database configuration - Your Hostinger DB
define('DB_HOST', '***************');
define('DB_NAME', 'u379753327_vaccinatoiinai');
define('DB_USER', 'u379753327_vaccinationadm');
define('DB_PASS', 'VaccinationAi@123');
define('DB_PORT', '3306');

// OpenAI API configuration
define('OPENAI_API_KEY', '********************************************************************************************************************************************************************'); // Add your OpenAI API key here

// Authentication settings
define('AUTH_SECRET', 'your_secret_key_here');

// File upload settings
define('UPLOAD_DIR', APP_ROOT . '/uploads');
define('MAX_UPLOAD_SIZE', 10 * 1024 * 1024); // 10MB

// Date and time settings
define('DEFAULT_TIMEZONE', 'Europe/Amsterdam');
date_default_timezone_set(DEFAULT_TIMEZONE);

// PHP execution settings
ini_set('max_execution_time', 120); // Increase timeout to 120 seconds for API calls
ini_set('display_errors', 1); // Show errors during development
