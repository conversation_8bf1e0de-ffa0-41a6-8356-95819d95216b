<?php
/**
 * AI Doctor - Clinical Decision Support System
 * Routes configuration
 */

// Home routes
$router->add('/', ['controller' => 'Home', 'action' => 'index']);
$router->add('/login', ['controller' => 'Auth', 'action' => 'login']);
$router->add('/logout', ['controller' => 'Auth', 'action' => 'logout']);

// Patient routes
$router->add('/patients', ['controller' => 'Patients', 'action' => 'index']);
$router->add('/patients/create', ['controller' => 'Patients', 'action' => 'create']);
$router->add('/patients/{id}', ['controller' => 'Patients', 'action' => 'show']);
$router->add('/patients/{id}/edit', ['controller' => 'Patients', 'action' => 'edit']);
$router->add('/patients/{id}/delete', ['controller' => 'Patients', 'action' => 'delete']);

// Appointment routes
$router->add('/appointments', ['controller' => 'Appointments', 'action' => 'index']);
$router->add('/appointments/create', ['controller' => 'Appointments', 'action' => 'create']);
$router->add('/appointments/update-status/{id}/{status}', ['controller' => 'Appointments', 'action' => 'updateStatus']);
$router->add('/appointments/get-appointments-by-date', ['controller' => 'Appointments', 'action' => 'getAppointmentsByDate']);
$router->add('/appointments/get-available-time-slots', ['controller' => 'Appointments', 'action' => 'getAvailableTimeSlots']);
$router->add('/appointments/{id}', ['controller' => 'Appointments', 'action' => 'show']);
$router->add('/appointments/{id}/edit', ['controller' => 'Appointments', 'action' => 'edit']);
$router->add('/appointments/{id}/delete', ['controller' => 'Appointments', 'action' => 'delete']);
$router->add('/calendar', ['controller' => 'Appointments', 'action' => 'calendar']);

// Consultation routes
$router->add('/consultations', ['controller' => 'Consultations', 'action' => 'index']);
$router->add('/consultations/create', ['controller' => 'Consultations', 'action' => 'create']);
$router->add('/consultations/{id}', ['controller' => 'Consultations', 'action' => 'viewConsultation']);
$router->add('/consultations/view/{id}', ['controller' => 'Consultations', 'action' => 'viewConsultation']);
$router->add('/consultations/{id}/edit', ['controller' => 'Consultations', 'action' => 'edit']);
$router->add('/consultations/{id}/delete', ['controller' => 'Consultations', 'action' => 'delete']);

// Protocol routes
$router->add('/travel-medicine', ['controller' => 'TravelMedicine', 'action' => 'index']);
$router->add('/travel-medicine/create', ['controller' => 'TravelMedicine', 'action' => 'create']);
$router->add('/travel-medicine/create/{step}', ['controller' => 'TravelMedicine', 'action' => 'create']);
$router->add('/travel-medicine/process-step', ['controller' => 'TravelMedicine', 'action' => 'processStep']);
$router->add('/travel-medicine/generate-ai-recommendations', ['controller' => 'TravelMedicine', 'action' => 'generateAIRecommendations']);
$router->add('/travel-medicine/previous-step/{currentStep}', ['controller' => 'TravelMedicine', 'action' => 'previousStep']);
$router->add('/travel-medicine/{id}', ['controller' => 'TravelMedicine', 'action' => 'show']);

$router->add('/std-testing', ['controller' => 'STDTesting', 'action' => 'index']);
$router->add('/std-testing/create', ['controller' => 'STDTesting', 'action' => 'create']);
$router->add('/std-testing/create/{step}', ['controller' => 'STDTesting', 'action' => 'create']);
$router->add('/std-testing/process-step', ['controller' => 'STDTesting', 'action' => 'processStep']);
$router->add('/std-testing/{id}', ['controller' => 'STDTesting', 'action' => 'show']);

$router->add('/clinical-qa', ['controller' => 'ClinicalQA', 'action' => 'index']);
$router->add('/clinical-qa/create', ['controller' => 'ClinicalQA', 'action' => 'create']);
$router->add('/clinical-qa/ask-question', ['controller' => 'ClinicalQA', 'action' => 'askQuestion']);
$router->add('/clinical-qa/ajax-ask', ['controller' => 'ClinicalQA', 'action' => 'ajaxAsk']);
$router->add('/clinical-qa/ajax-ask-concise', ['controller' => 'ClinicalQA', 'action' => 'ajaxAskConcise']);
$router->add('/clinical-qa/{id}', ['controller' => 'ClinicalQA', 'action' => 'show']);

// API routes
$router->add('/api/patients', ['controller' => 'Api\\Patients', 'action' => 'index']);
$router->add('/api/patients/create', ['controller' => 'Api\\Patients', 'action' => 'create']);
$router->add('/api/patients/{id}', ['controller' => 'Api\\Patients', 'action' => 'show']);
$router->add('/api/patients/{id}/update', ['controller' => 'Api\\Patients', 'action' => 'update']);
$router->add('/api/patients/{id}/delete', ['controller' => 'Api\\Patients', 'action' => 'delete']);

$router->add('/api/consultations', ['controller' => 'Api\\Consultations', 'action' => 'index']);
$router->add('/api/consultations/create', ['controller' => 'Api\\Consultations', 'action' => 'create']);
$router->add('/api/consultations/{id}', ['controller' => 'Api\\Consultations', 'action' => 'show']);
$router->add('/api/consultations/{id}/update', ['controller' => 'Api\\Consultations', 'action' => 'update']);

$router->add('/api/clinical-guidance', ['controller' => 'Api\\ClinicalGuidance', 'action' => 'index']);
$router->add('/api/health', ['controller' => 'Api\\Health', 'action' => 'index']);

// Set 404 handler
$router->setNotFoundHandler(function() {
    header('HTTP/1.1 404 Not Found');
    include APP_ROOT . '/App/views/errors/404.php';
});
