/* Medical UI Enhanced - Travel Protocol Design System */

/* ===== CSS Custom Properties (Design Tokens) ===== */
:root {
  /* Medical Color Palette */
  --medical-primary: #2c3e50;
  --medical-secondary: #34495e;
  --medical-accent: #3498db;
  
  /* Risk Level Colors */
  --risk-high: #dc3545;
  --risk-high-bg: #fff5f5;
  --risk-high-light: #f8d7da;
  
  --risk-moderate: #fd7e14;
  --risk-moderate-bg: #fff8f0;
  --risk-moderate-light: #ffeaa7;
  
  --risk-low: #ffc107;
  --risk-low-bg: #fffbf0;
  --risk-low-light: #fff3cd;
  
  /* Vaccination Priority Colors */
  --vaccine-required: #dc3545;
  --vaccine-required-bg: #fff5f5;
  
  --vaccine-recommended: #fd7e14;
  --vaccine-recommended-bg: #fff8f0;
  
  --vaccine-optional: #0d6efd;
  --vaccine-optional-bg: #f0f8ff;
  
  --vaccine-preventive: #198754;
  --vaccine-preventive-bg: #f0fff4;
  
  /* Typography Scale */
  --font-size-h1: 1.75rem;
  --font-size-h2: 1.5rem;
  --font-size-h3: 1.25rem;
  --font-size-body: 1rem;
  --font-size-caption: 0.875rem;
  --font-size-small: 0.75rem;
  
  /* Font Weights */
  --font-weight-bold: 600;
  --font-weight-medium: 500;
  --font-weight-normal: 400;
  
  /* Spacing System */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-xxl: 2.5rem;
  --spacing-xxxl: 3rem;
  
  /* Border Radius */
  --border-radius-sm: 6px;
  --border-radius-md: 12px;
  --border-radius-lg: 16px;
  --border-radius-full: 50%;
  
  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
  --shadow-md: 0 4px 12px rgba(0,0,0,0.1);
  --shadow-lg: 0 8px 24px rgba(0,0,0,0.15);
  
  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.2s ease;
  --transition-slow: 0.3s ease;
}

/* ===== Typography System ===== */
.medical-heading-1 {
  font-size: var(--font-size-h1);
  font-weight: var(--font-weight-bold);
  color: var(--medical-primary);
  line-height: 1.3;
  margin-bottom: var(--spacing-lg);
}

.medical-heading-2 {
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-bold);
  color: var(--medical-secondary);
  line-height: 1.4;
  margin-bottom: var(--spacing-md);
}

.medical-heading-3 {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-medium);
  color: var(--medical-secondary);
  line-height: 1.4;
  margin-bottom: var(--spacing-sm);
}

.medical-body {
  font-size: var(--font-size-body);
  line-height: 1.6;
  color: var(--medical-primary);
  margin-bottom: var(--spacing-md);
}

.medical-caption {
  font-size: var(--font-size-caption);
  color: #6c757d;
  line-height: 1.5;
}

.medical-small {
  font-size: var(--font-size-small);
  color: #6c757d;
  line-height: 1.4;
}

/* ===== Base Card Component ===== */
.medical-card {
  background: #ffffff;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  transition: all var(--transition-normal);
  border: 1px solid #e9ecef;
  position: relative;
  overflow: hidden;
}

.medical-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.medical-card-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid #f8f9fa;
}

.medical-card-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
  font-size: 1.25rem;
  color: white;
}

.medical-card-title {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-bold);
  color: var(--medical-primary);
  margin: 0;
  flex: 1;
}

.medical-card-body {
  color: var(--medical-secondary);
  line-height: 1.6;
}

/* ===== Risk Category Cards ===== */
.risk-category-card {
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
  margin-bottom: var(--spacing-lg);
  transition: all var(--transition-normal);
  overflow: hidden;
  position: relative;
}

.risk-category-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.risk-high {
  border-left: 6px solid var(--risk-high);
  background: linear-gradient(135deg, var(--risk-high-bg) 0%, #ffffff 100%);
}

.risk-high .medical-card-icon {
  background: var(--risk-high);
}

.risk-moderate {
  border-left: 6px solid var(--risk-moderate);
  background: linear-gradient(135deg, var(--risk-moderate-bg) 0%, #ffffff 100%);
}

.risk-moderate .medical-card-icon {
  background: var(--risk-moderate);
}

.risk-low {
  border-left: 6px solid var(--risk-low);
  background: linear-gradient(135deg, var(--risk-low-bg) 0%, #ffffff 100%);
}

.risk-low .medical-card-icon {
  background: var(--risk-low);
}

/* ===== Severity Badges ===== */
.severity-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-left: auto;
}

.severity-high {
  background: var(--risk-high);
  color: white;
}

.severity-moderate {
  background: var(--risk-moderate);
  color: white;
}

.severity-low {
  background: var(--risk-low);
  color: var(--medical-primary);
}

/* ===== Vaccination Cards ===== */
.vaccination-card {
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
  border: 2px solid transparent;
}

.vaccination-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.vaccination-required {
  background: linear-gradient(135deg, var(--vaccine-required-bg) 0%, #ffffff 100%);
  border-color: var(--vaccine-required);
}

.vaccination-recommended {
  background: linear-gradient(135deg, var(--vaccine-recommended-bg) 0%, #ffffff 100%);
  border-color: var(--vaccine-recommended);
}

.vaccination-optional {
  background: linear-gradient(135deg, var(--vaccine-optional-bg) 0%, #ffffff 100%);
  border-color: var(--vaccine-optional);
}

.vaccination-preventive {
  background: linear-gradient(135deg, var(--vaccine-preventive-bg) 0%, #ffffff 100%);
  border-color: var(--vaccine-preventive);
}

/* ===== Priority Badges ===== */
.priority-badge {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.priority-required {
  background: var(--vaccine-required);
  color: white;
}

.priority-recommended {
  background: var(--vaccine-recommended);
  color: white;
}

.priority-optional {
  background: var(--vaccine-optional);
  color: white;
}

.priority-preventive {
  background: var(--vaccine-preventive);
  color: white;
}

/* ===== Grid Layouts ===== */
.medical-grid {
  display: grid;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xxl);
}

.medical-grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.medical-grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

/* ===== Responsive Grid System ===== */
@media (max-width: 992px) {
  .medical-grid-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .medical-grid-2,
  .medical-grid-3 {
    grid-template-columns: 1fr;
  }
  
  .medical-card {
    padding: var(--spacing-md);
  }
  
  .vaccination-card {
    padding: var(--spacing-md);
  }
}

/* ===== Utility Classes ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--spacing-xs) !important; }
.mb-2 { margin-bottom: var(--spacing-sm) !important; }
.mb-3 { margin-bottom: var(--spacing-md) !important; }
.mb-4 { margin-bottom: var(--spacing-lg) !important; }
.mb-5 { margin-bottom: var(--spacing-xl) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--spacing-xs) !important; }
.mt-2 { margin-top: var(--spacing-sm) !important; }
.mt-3 { margin-top: var(--spacing-md) !important; }
.mt-4 { margin-top: var(--spacing-lg) !important; }
.mt-5 { margin-top: var(--spacing-xl) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-xs) !important; }
.p-2 { padding: var(--spacing-sm) !important; }
.p-3 { padding: var(--spacing-md) !important; }
.p-4 { padding: var(--spacing-lg) !important; }
.p-5 { padding: var(--spacing-xl) !important; }

/* ===== Animation Classes ===== */
.fade-in {
  animation: fadeIn var(--transition-slow) ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-up {
  animation: slideUp var(--transition-normal) ease-out;
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* ===== Focus and Accessibility ===== */
.medical-card:focus,
.vaccination-card:focus {
  outline: 3px solid var(--medical-accent);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --shadow-md: 0 4px 12px rgba(0,0,0,0.3);
    --shadow-lg: 0 8px 24px rgba(0,0,0,0.4);
  }
  
  .medical-card,
  .vaccination-card {
    border-width: 2px;
    border-color: var(--medical-primary);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
/* AI Reco
mmendations Styles */
.ai-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    font-size: 0.8rem;
    color: white;
    font-weight: 500;
}

.ai-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.ai-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.ai-section-title {
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.ai-content {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.25rem;
    line-height: 1.6;
    color: #495057;
    border-left: 4px solid #6f42c1;
}

.ai-sources {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 2px solid #e9ecef;
}

.sources-title {
    color: #2c3e50;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.sources-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.source-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    background: linear-gradient(135deg, #198754 0%, #157347 100%);
    color: white;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

/* Travel Medicine Vaccination Styles */
.vaccination-section {
    margin-bottom: 2rem;
}

.vaccination-section-title {
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.vaccination-content-box {
    border-radius: 12px;
    padding: 1.5rem;
    line-height: 1.6;
    color: #495057;
    position: relative;
    overflow: hidden;
}

.vaccination-content-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
}

.vaccination-content-box.required {
    background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
    border: 1px solid #fecaca;
}

.vaccination-content-box.required::before {
    background: #dc2626;
}

.vaccination-content-box.recommended {
    background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
    border: 1px solid #fed7aa;
}

.vaccination-content-box.recommended::before {
    background: #f59e0b;
}

.vaccination-content-box.malaria {
    background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
    border: 1px solid #fca5a5;
}

.vaccination-content-box.malaria::before {
    background: #ef4444;
}

.vaccination-content-box.special {
    background: linear-gradient(135deg, #eff6ff 0%, #ffffff 100%);
    border: 1px solid #93c5fd;
}

.vaccination-content-box.special::before {
    background: #3b82f6;
}

/* STD Testing AI Recommendations */
.ai-recommendations {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 2rem;
    border: 2px solid #e9ecef;
}

.recommendations-title {
    color: #2c3e50;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.recommendations-list {
    margin-bottom: 1.5rem;
}

.recommendation-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    border-left: 4px solid #6f42c1;
}

.recommendation-icon {
    color: #198754;
    font-size: 1.1rem;
    margin-right: 1rem;
    margin-top: 0.25rem;
    flex-shrink: 0;
}

.recommendation-text {
    color: #495057;
    line-height: 1.5;
    flex: 1;
}

.recommendations-note {
    background: rgba(111, 66, 193, 0.1);
    border: 1px solid rgba(111, 66, 193, 0.2);
    border-radius: 8px;
    padding: 1rem;
    color: #5a32a3;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .ai-content {
        padding: 1rem;
    }
    
    .vaccination-content-box {
        padding: 1.25rem;
    }
    
    .sources-list {
        flex-direction: column;
    }
    
    .source-badge {
        align-self: flex-start;
    }
    
    .ai-recommendations {
        padding: 1.5rem;
    }
    
    .recommendation-item {
        flex-direction: column;
        text-align: center;
    }
    
    .recommendation-icon {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }
}