/* Vaccination Plan Styling */

/* Card Styling */
.vaccination-card {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    border: none;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.vaccination-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

/* Selected Vaccinations Section */
.selected-vaccinations {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.selected-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.selected-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-right: 1rem;
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

.selected-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    color: #2e7d32;
}

.selected-count {
    margin-left: auto;
    background: #4caf50;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Vaccination Item Cards */
.vaccination-item {
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
    border: none;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.vaccination-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.vaccination-item.required {
    border-left: 4px solid #dc3545;
}

.vaccination-item.recommended {
    border-left: 4px solid #ffc107;
}

.vaccination-item-header {
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.vaccination-item-header.required {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.vaccination-item-header.recommended {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
}

.vaccination-item-title {
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
}

.vaccination-item-title i {
    margin-right: 0.5rem;
}

.vaccination-item-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.vaccination-item-badge.required {
    background-color: white;
    color: #dc3545;
}

.vaccination-item-badge.recommended {
    background-color: white;
    color: #e0a800;
}

.vaccination-item-body {
    padding: 1.25rem;
    background-color: white;
}

.vaccination-item-description {
    color: #495057;
    margin-bottom: 0;
    line-height: 1.6;
}

/* AI Recommendation Section */
.ai-recommendation-card {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    border: none;
}

.ai-recommendation-header {
    background: linear-gradient(135deg, #198754 0%, #157347 100%);
    color: white;
    padding: 1.5rem;
    display: flex;
    align-items: center;
}

.ai-recommendation-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-right: 1rem;
}

.ai-recommendation-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    flex-grow: 1;
}

.ai-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    backdrop-filter: blur(5px);
}

.ai-badge i {
    font-size: 12px;
    margin-right: 0.5rem;
}

/* Risk Section Styling */
.risk-section {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    margin-bottom: 24px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.risk-section:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.risk-section-header {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    background: linear-gradient(135deg, #4a6bdf 0%, #2a4bdf 100%);
    color: white;
}

.risk-section-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    font-size: 18px;
}

.risk-section-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.risk-item {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #eaeaea;
    transition: background-color 0.2s ease;
}

.risk-item:last-child {
    border-bottom: none;
}

.risk-item:hover {
    background-color: #f9f9f9;
}

.risk-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    font-size: 16px;
    color: white;
}

.severity-high {
    background: linear-gradient(135deg, #ff4d4d 0%, #d90000 100%);
}

.severity-medium {
    background: linear-gradient(135deg, #ffaa33 0%, #ff8800 100%);
}

.severity-low {
    background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
}

.risk-content {
    flex: 1;
}

.risk-title {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 500;
}

.risk-details {
    font-size: 14px;
    color: #666;
    margin-top: 4px;
}

.severity-badge {
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    color: white;
    text-transform: uppercase;
}

/* AI Sources Styling */
.ai-sources {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-top: 24px;
}

.sources-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #333;
}

.sources-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.source-badge {
    background: #e9ecef;
    color: #495057;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 13px;
    display: inline-block;
    transition: all 0.2s ease;
}

.source-badge:hover {
    background: #dee2e6;
    transform: translateY(-1px);
}

/* Disclaimer Styling */
.disclaimer {
    display: flex;
    align-items: flex-start;
    background: linear-gradient(135deg, #e9f2ff 0%, #d4e6ff 100%);
    border-left: 4px solid #4a6bdf;
    border-radius: 8px;
    padding: 16px;
    margin-top: 24px;
    font-size: 14px;
    color: #333;
    line-height: 1.5;
}

.disclaimer i {
    color: #4a6bdf;
    font-size: 18px;
    margin-right: 12px;
    margin-top: 2px;
}

/* Loading Spinner Styling */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(0, 123, 255, 0.1);
    border-radius: 50%;
    border-top: 4px solid #007bff;
    animation: spin 1s linear infinite;
}

.loading-text {
    margin-top: 16px;
    font-size: 16px;
    color: #6c757d;
    font-weight: 500;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Animation effects */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.fade-in-delay-1 {
    animation: fadeIn 0.5s ease-in-out 0.1s both;
}

.fade-in-delay-2 {
    animation: fadeIn 0.5s ease-in-out 0.2s both;
}

.fade-in-delay-3 {
    animation: fadeIn 0.5s ease-in-out 0.3s both;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .risk-section-header,
    .selected-header,
    .ai-recommendation-header {
        padding: 12px 16px;
    }
    
    .risk-section-icon,
    .selected-icon,
    .ai-recommendation-icon {
        width: 32px;
        height: 32px;
        font-size: 16px;
    }
    
    .risk-section-title,
    .selected-title,
    .ai-recommendation-title {
        font-size: 16px;
    }
    
    .risk-item {
        padding: 12px 16px;
    }
    
    .risk-icon {
        width: 30px;
        height: 30px;
        font-size: 14px;
    }
    
    .risk-title {
        font-size: 15px;
    }
    
    .severity-badge,
    .vaccination-item-badge {
        padding: 3px 8px;
        font-size: 11px;
    }
    
    .vaccination-item-title {
        font-size: 14px;
    }
    
    .vaccination-item-description {
        font-size: 13px;
    }
    
    .disclaimer {
        padding: 12px;
        font-size: 13px;
    }
    
    .disclaimer i {
        font-size: 16px;
    }
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 5px solid rgba(0, 123, 255, 0.1);
    border-radius: 50%;
    border-top-color: #007bff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.loading-text {
    margin-top: 1rem;
    color: #6c757d;
    font-weight: 500;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .selected-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .selected-icon {
        margin-bottom: 1rem;
    }
    
    .selected-count {
        margin-left: 0;
        margin-top: 1rem;
    }
    
    .risk-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .risk-icon {
        margin-bottom: 1rem;
    }
    
    .severity-badge {
        margin-left: 0;
        margin-top: 1rem;
    }
}
