/* Calendar Styles */
.calendar-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    overflow: hidden;
    margin: 20px 0;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 32px;
    border-bottom: 1px solid #e5e7eb;
    background: #fafafa;
}

.calendar-nav {
    display: flex;
    align-items: center;
    gap: 20px;
}

.nav-btn {
    background: none;
    border: none;
    padding: 10px;
    border-radius: 8px;
    cursor: pointer;
    color: #6b7280;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-btn:hover {
    background: #e5e7eb;
    color: #374151;
}

.calendar-title h2 {
    margin: 0;
    font-size: 28px;
    font-weight: 600;
    color: #111827;
}

.calendar-controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

.view-controls {
    display: flex;
    background: #f3f4f6;
    border-radius: 8px;
    padding: 4px;
}

.view-btn {
    background: none;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #6b7280;
    transition: all 0.2s ease;
}

.view-btn.active {
    background: white;
    color: #111827;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    border: none;
}

.btn-primary {
    background: #3b82f6;
    color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
    background: #2563eb;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.calendar-grid {
    padding: 0;
}

.calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
}

.weekday {
    padding: 16px 12px;
    text-align: center;
    font-size: 13px;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
}

.calendar-day {
    min-height: 140px;
    border-right: 1px solid #e5e7eb;
    border-bottom: 1px solid #e5e7eb;
    padding: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    background: white;
}

.calendar-day:hover {
    background: #f8fafc;
}

.calendar-day.selected {
    background: #eff6ff;
    border-color: #3b82f6;
}

.calendar-day.today {
    background: #fef3c7;
    border-color: #f59e0b;
}

.calendar-day.other-month {
    background: #f9fafb;
    color: #9ca3af;
}

.calendar-day.other-month:hover {
    background: #f3f4f6;
}

.day-number {
    font-size: 16px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.calendar-day.today .day-number {
    background: #f59e0b;
    color: white;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 700;
}

.calendar-day.other-month .day-number {
    color: #9ca3af;
}

.day-appointments {
    display: flex;
    flex-direction: column;
    gap: 3px;
    margin-top: 4px;
}

.appointment-block {
    background: #dbeafe;
    border-left: 4px solid #3b82f6;
    padding: 6px 8px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.appointment-block:hover {
    background: #bfdbfe;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Appointment status colors matching the screenshot */
.appointment-scheduled {
    background: #dbeafe;
    border-left-color: #3b82f6;
}

.appointment-completed {
    background: #dcfce7;
    border-left-color: #22c55e;
}

.appointment-cancelled {
    background: #fee2e2;
    border-left-color: #ef4444;
}

.appointment-no_show {
    background: #fef3c7;
    border-left-color: #f59e0b;
}

/* Additional color variations for visual variety */
.appointment-block:nth-child(2) {
    background: #fef3c7;
    border-left-color: #f59e0b;
}

.appointment-block:nth-child(3) {
    background: #f3e8ff;
    border-left-color: #8b5cf6;
}

.appointment-block:nth-child(4) {
    background: #ecfdf5;
    border-left-color: #10b981;
}

.appointment-time {
    font-weight: 700;
    color: #374151;
    font-size: 11px;
    margin-bottom: 2px;
}

.appointment-patient {
    color: #6b7280;
    font-size: 11px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
}

.appointment-more {
    background: #f3f4f6;
    color: #6b7280;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    text-align: center;
    cursor: pointer;
    font-weight: 600;
    border: 1px solid #e5e7eb;
}

.appointment-more:hover {
    background: #e5e7eb;
}

.appointments-panel {
    border-top: 1px solid #e5e7eb;
    background: #f9fafb;
}

.panel-header {
    padding: 20px 32px;
    border-bottom: 1px solid #e5e7eb;
    background: white;
}

.panel-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #111827;
}

.panel-content {
    padding: 32px;
}

.appointments-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.appointment-item {
    background: white;
    border-radius: 8px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 4px solid #3b82f6;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.appointment-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.appointment-item .appointment-time {
    font-size: 16px;
    font-weight: 700;
    color: #111827;
    min-width: 60px;
}

.appointment-details {
    flex: 1;
}

.appointment-item .appointment-patient {
    font-size: 16px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 4px;
}

.appointment-reason {
    font-size: 14px;
    color: #6b7280;
}

.appointment-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: capitalize;
    background: #f3f4f6;
    color: #6b7280;
}

.appointment-item.appointment-scheduled .appointment-status {
    background: #dbeafe;
    color: #1d4ed8;
}

.appointment-item.appointment-completed .appointment-status {
    background: #dcfce7;
    color: #166534;
}

.appointment-item.appointment-cancelled .appointment-status {
    background: #fee2e2;
    color: #dc2626;
}

.appointment-item.appointment-no_show .appointment-status {
    background: #fef3c7;
    color: #d97706;
}

.no-appointments {
    text-align: center;
    color: #6b7280;
    padding: 40px 20px;
}

.no-appointments p {
    margin: 0 0 20px 0;
    font-size: 16px;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 1000;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(4px);
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 32px;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
}

.modal-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #111827;
}

.modal-close {
    background: none;
    border: none;
    font-size: 28px;
    cursor: pointer;
    color: #6b7280;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #e5e7eb;
    color: #374151;
}

.modal-body {
    padding: 32px;
}

.form-group {
    margin-bottom: 24px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.2s ease;
    background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-actions {
    display: flex;
    gap: 16px;
    justify-content: flex-end;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e5e7eb;
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

.btn-outline {
    background: white;
    color: #6b7280;
    border: 1px solid #d1d5db;
}

.btn-outline:hover {
    background: #f9fafb;
    color: #374151;
    border-color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .calendar-header {
        padding: 20px 24px;
    }
    
    .calendar-title h2 {
        font-size: 24px;
    }
}

@media (max-width: 768px) {
    .calendar-header {
        flex-direction: column;
        gap: 20px;
        align-items: stretch;
        padding: 16px 20px;
    }
    
    .calendar-controls {
        justify-content: space-between;
    }
    
    .calendar-day {
        min-height: 100px;
        padding: 8px;
    }
    
    .appointment-block {
        font-size: 11px;
        padding: 4px 6px;
    }
    
    .appointment-time {
        font-size: 10px;
    }
    
    .appointment-patient {
        font-size: 10px;
    }
    
    .panel-content {
        padding: 20px;
    }
    
    .modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .modal-header,
    .modal-body {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .calendar-day {
        min-height: 80px;
        padding: 6px;
    }
    
    .day-number {
        font-size: 14px;
    }
    
    .appointment-block {
        padding: 3px 4px;
        font-size: 10px;
    }
    
    .weekday {
        padding: 12px 8px;
        font-size: 12px;
    }
}
