/* Risk Cards Styling for Travel Medicine Protocol */

/* Risk Category Sections */
.risk-category-section {
    margin-bottom: 2rem;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Section Headers */
.risk-section-header {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    position: relative;
}

.disease-risk .risk-section-header {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5253 100%);
    color: white;
}

.environmental-risk .risk-section-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.vaccination-risk .risk-section-header {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;
}

.risk-section-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.25rem;
}

.risk-section-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    flex-grow: 1;
}

.risk-section-count {
    background-color: rgba(255, 255, 255, 0.3);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Risk Items */
.risk-item {
    display: flex;
    padding: 1rem 1.5rem;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
    background-color: #fff;
    margin-bottom: 0.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.risk-item:hover {
    background-color: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.risk-item.expanded {
    background-color: #f0f4f8;
}

.risk-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1rem;
    flex-shrink: 0;
}

.disease-risk .risk-icon {
    background-color: #ffe8e8;
    color: #e74c3c;
}

.environmental-risk .risk-icon {
    background-color: #e3f2fd;
    color: #2196f3;
}

.vaccination-risk .risk-icon {
    background-color: #e8f5e9;
    color: #4caf50;
}

.risk-content {
    flex-grow: 1;
}

.risk-title {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: #2d3748;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.risk-description {
    color: #4a5568;
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

.risk-recommendations {
    color: #718096;
    font-style: italic;
    padding-top: 0.5rem;
    border-top: 1px dashed #e2e8f0;
    font-size: 0.95rem;
}

/* Severity Badges */
.severity-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-left: 0.5rem;
    transition: transform 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.severity-high {
    background-color: #f56565;
    color: white;
}

.severity-moderate {
    background-color: #ed8936;
    color: white;
}

.severity-low {
    background-color: #38b2ac;
    color: white;
}

/* AI Sources Section */
.ai-sources {
    margin-top: 2rem;
    padding: 1rem;
    background-color: #f7fafc;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
}

.sources-title {
    font-size: 1rem;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.75rem;
}

.sources-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.source-badge {
    background-color: #ebf4ff;
    color: #4c51bf;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Animations */
.fade-in {
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.5s ease, transform 0.5s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .risk-section-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .risk-section-icon {
        margin-bottom: 0.5rem;
    }
    
    .risk-section-count {
        margin-top: 0.5rem;
    }
    
    .risk-item {
        flex-direction: column;
    }
    
    .risk-icon {
        margin-bottom: 0.5rem;
    }
}
