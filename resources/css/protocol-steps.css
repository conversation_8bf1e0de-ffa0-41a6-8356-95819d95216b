/* Travel Protocol Steps Styling */

/* Layout */
.protocol-layout {
  display: flex;
  min-height: calc(100vh - 60px);
}

.protocol-sidebar {
  width: 250px;
  background-color: #f8f9fa;
  padding: 1rem;
  border-right: 1px solid #e9ecef;
  position: fixed;
  height: calc(100vh - 60px);
  overflow-y: auto;
}

.protocol-content {
  flex: 1;
  padding: 1.5rem 1.5rem 1.5rem calc(250px + 1.5rem);
}

/* Step navigation styling */
.protocol-steps .list-group-item {
  border-radius: 0.5rem;
  margin-bottom: 0.5rem;
  border: 1px solid #e9ecef;
  padding: 0.5rem;
  transition: all 0.3s ease;
  background-color: white;
}

.protocol-steps .list-group-item.active {
  background-color: #f8f9fa;
  border-left: 4px solid #0d6efd;
  font-weight: 500;
}

.protocol-steps .list-group-item.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.protocol-steps .step-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.9rem;
}

.protocol-steps .active .step-number {
  background-color: #0d6efd;
  color: white;
}

.protocol-steps .disabled .step-number {
  background-color: #6c757d;
  color: white;
}

.protocol-steps .completed .step-number {
  background-color: #198754;
  color: white;
}

.protocol-steps h6 {
  margin-bottom: 0.15rem;
  font-weight: 600;
  font-size: 0.95rem;
}

.protocol-steps small {
  color: #6c757d;
  font-size: 0.8rem;
  line-height: 1.2;
  display: block;
}

/* Progress bar */
.protocol-progress {
  height: 6px;
  margin-bottom: 1.25rem;
}

/* Step content */
.step-content {
  padding: 1.5rem;
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Navigation buttons */
.step-navigation {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
}

/* ===== Risk Analysis Enhanced Components ===== */

/* Risk Category Cards */
.risk-analysis-container {
  display: grid;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.risk-category-section {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.risk-category-section:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 24px rgba(0,0,0,0.15);
}

/* Disease Risk Cards */
.disease-risk-high {
  border-left: 6px solid #dc3545;
  background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
}

.disease-risk-moderate {
  border-left: 6px solid #fd7e14;
  background: linear-gradient(135deg, #fff8f0 0%, #ffffff 100%);
}

.disease-risk-low {
  border-left: 6px solid #ffc107;
  background: linear-gradient(135deg, #fffbf0 0%, #ffffff 100%);
}

/* Environmental Risk Cards */
.environmental-risk {
  border-left: 6px solid #17a2b8;
  background: linear-gradient(135deg, #f0fdff 0%, #ffffff 100%);
}

/* Safety Risk Cards */
.safety-risk {
  border-left: 6px solid #6f42c1;
  background: linear-gradient(135deg, #f8f5ff 0%, #ffffff 100%);
}

/* Patient-Specific Risk Cards */
.patient-specific-risk {
  border-left: 6px solid #e83e8c;
  background: linear-gradient(135deg, #fff0f6 0%, #ffffff 100%);
  border: 2px solid #e83e8c;
}

/* Risk Item Components */
.risk-item {
  display: flex;
  align-items: flex-start;
  padding: 1rem;
  margin-bottom: 0.75rem;
  background: rgba(255,255,255,0.7);
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.risk-item:hover {
  background: rgba(255,255,255,0.9);
  transform: translateX(5px);
}

.risk-item:last-child {
  margin-bottom: 0;
}

.risk-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  font-size: 1.25rem;
  color: white;
  flex-shrink: 0;
}

.risk-high .risk-icon {
  background: #dc3545;
}

.risk-moderate .risk-icon {
  background: #fd7e14;
}

.risk-low .risk-icon {
  background: #ffc107;
  color: #2c3e50;
}

.environmental-risk .risk-icon {
  background: #17a2b8;
}

.safety-risk .risk-icon {
  background: #6f42c1;
}

.patient-specific-risk .risk-icon {
  background: #e83e8c;
}

.risk-content {
  flex: 1;
}

.risk-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.risk-description {
  color: #34495e;
  line-height: 1.5;
  margin-bottom: 0.5rem;
}

.risk-recommendations {
  font-size: 0.9rem;
  color: #6c757d;
  font-style: italic;
}

/* Severity Badges */
.severity-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-left: 0.5rem;
}

.severity-high {
  background: #dc3545;
  color: white;
}

.severity-moderate {
  background: #fd7e14;
  color: white;
}

.severity-low {
  background: #ffc107;
  color: #2c3e50;
}

/* Section Headers */
.risk-section-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.25rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #f8f9fa;
}

.risk-section-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  font-size: 1.5rem;
  color: white;
}

.disease-risk-high .risk-section-icon {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.disease-risk-moderate .risk-section-icon {
  background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);
}

.disease-risk-low .risk-section-icon {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
}

.environmental-risk .risk-section-icon {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.safety-risk .risk-section-icon {
  background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
}

.patient-specific-risk .risk-section-icon {
  background: linear-gradient(135deg, #e83e8c 0%, #d91a72 100%);
}

.risk-section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  flex: 1;
}

.risk-section-count {
  background: rgba(0,0,0,0.1);
  color: #2c3e50;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Expandable Content */
.risk-expandable {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.risk-expandable.expanded {
  max-height: 500px;
}

.risk-expand-toggle {
  background: none;
  border: none;
  color: #007bff;
  font-size: 0.875rem;
  cursor: pointer;
  padding: 0;
  text-decoration: underline;
  margin-top: 0.5rem;
}

.risk-expand-toggle:hover {
  color: #0056b3;
}

/* Responsive Design for Risk Analysis */
@media (max-width: 768px) {
  .risk-category-section {
    padding: 1rem;
    margin-bottom: 1rem;
  }
  
  .risk-item {
    padding: 0.75rem;
    flex-direction: column;
    text-align: center;
  }
  
  .risk-icon {
    margin-right: 0;
    margin-bottom: 0.5rem;
  }
  
  .risk-title {
    justify-content: center;
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .severity-badge {
    margin-left: 0;
  }
}

/* ===== Vaccination Plan Enhanced Components ===== */

/* Vaccination Card System */
.vaccination-plan-container {
  display: grid;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.vaccination-category-section {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.vaccination-category-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0,0,0,0.15);
}

/* Priority-based Vaccination Cards */
.vaccination-required {
  border-left: 6px solid #dc3545;
  background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
  border: 2px solid #dc3545;
}

.vaccination-recommended {
  border-left: 6px solid #fd7e14;
  background: linear-gradient(135deg, #fff8f0 0%, #ffffff 100%);
  border: 2px solid #fd7e14;
}

.vaccination-optional {
  border-left: 6px solid #0d6efd;
  background: linear-gradient(135deg, #f0f8ff 0%, #ffffff 100%);
  border: 2px solid #0d6efd;
}

.vaccination-preventive {
  border-left: 6px solid #198754;
  background: linear-gradient(135deg, #f0fff4 0%, #ffffff 100%);
  border: 2px solid #198754;
}

/* Vaccination Item Cards */
.vaccination-item {
  display: flex;
  align-items: flex-start;
  padding: 1.25rem;
  margin-bottom: 1rem;
  background: rgba(255,255,255,0.8);
  border-radius: 10px;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

.vaccination-item:hover {
  background: rgba(255,255,255,0.95);
  transform: translateX(5px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.vaccination-item:last-child {
  margin-bottom: 0;
}

.vaccination-icon {
  width: 48px;
  height: 48px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1.25rem;
  font-size: 1.5rem;
  color: white;
  flex-shrink: 0;
}

.vaccination-required .vaccination-icon {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.vaccination-recommended .vaccination-icon {
  background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);
}

.vaccination-optional .vaccination-icon {
  background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
}

.vaccination-preventive .vaccination-icon {
  background: linear-gradient(135deg, #198754 0%, #157347 100%);
}

.vaccination-content {
  flex: 1;
  padding-right: 1rem;
}

.vaccination-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.vaccination-description {
  color: #34495e;
  line-height: 1.6;
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
}

.vaccination-timing {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.timing-icon {
  color: #6c757d;
  font-size: 0.9rem;
}

.timing-text {
  color: #6c757d;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Priority Badges for Vaccinations */
.priority-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  gap: 0.25rem;
  position: absolute;
  top: 1rem;
  right: 1rem;
}

.priority-required {
  background: #dc3545;
  color: white;
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

.priority-recommended {
  background: #fd7e14;
  color: white;
  box-shadow: 0 2px 4px rgba(253, 126, 20, 0.3);
}

.priority-optional {
  background: #0d6efd;
  color: white;
  box-shadow: 0 2px 4px rgba(13, 110, 253, 0.3);
}

.priority-preventive {
  background: #198754;
  color: white;
  box-shadow: 0 2px 4px rgba(25, 135, 84, 0.3);
}

/* Vaccination Section Headers */
.vaccination-section-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 3px solid #f8f9fa;
}

.vaccination-section-icon {
  width: 56px;
  height: 56px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1.25rem;
  font-size: 1.75rem;
  color: white;
}

.vaccination-required .vaccination-section-icon {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.vaccination-recommended .vaccination-section-icon {
  background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);
}

.vaccination-optional .vaccination-section-icon {
  background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
}

.vaccination-preventive .vaccination-section-icon {
  background: linear-gradient(135deg, #198754 0%, #157347 100%);
}

.vaccination-section-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  flex: 1;
}

.vaccination-section-count {
  background: rgba(0,0,0,0.1);
  color: #2c3e50;
  padding: 0.375rem 0.75rem;
  border-radius: 15px;
  font-size: 0.875rem;
  font-weight: 600;
}

/* Vaccination Timeline Component */
.vaccination-timeline {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-top: 0.75rem;
  padding: 0.75rem;
  background: rgba(0,0,0,0.03);
  border-radius: 8px;
}

.timeline-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.timeline-step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 15px;
  right: -50%;
  width: 100%;
  height: 2px;
  background: #dee2e6;
  z-index: 1;
}

.timeline-dot {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  color: #6c757d;
  z-index: 2;
  position: relative;
}

.timeline-dot.active {
  background: #198754;
  color: white;
}

.timeline-dot.completed {
  background: #28a745;
  color: white;
}

.timeline-label {
  font-size: 0.75rem;
  color: #6c757d;
  text-align: center;
  margin-top: 0.25rem;
  line-height: 1.2;
}

/* Expandable Vaccination Details */
.vaccination-details {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  margin-top: 0.75rem;
}

.vaccination-details.expanded {
  max-height: 300px;
}

.vaccination-details-content {
  padding: 1rem;
  background: rgba(0,0,0,0.02);
  border-radius: 8px;
  border-left: 3px solid #dee2e6;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f8f9fa;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 500;
  color: #495057;
  font-size: 0.9rem;
}

.detail-value {
  color: #6c757d;
  font-size: 0.9rem;
  text-align: right;
  flex: 1;
  margin-left: 1rem;
}

/* Responsive Design for Vaccination Plan */
@media (max-width: 768px) {
  .vaccination-category-section {
    padding: 1rem;
    margin-bottom: 1rem;
  }
  
  .vaccination-item {
    padding: 1rem;
    flex-direction: column;
    text-align: center;
  }
  
  .vaccination-icon {
    margin-right: 0;
    margin-bottom: 0.75rem;
  }
  
  .vaccination-content {
    padding-right: 0;
  }
  
  .priority-badge {
    position: static;
    margin-top: 0.5rem;
  }
  
  .vaccination-timeline {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .timeline-step::after {
    display: none;
  }
}

/* Print Styles for Vaccination Plan */
@media print {
  .vaccination-category-section {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #dee2e6;
  }
  
  .vaccination-item {
    break-inside: avoid;
  }
  
  .priority-badge {
    background: transparent !important;
    color: #000 !important;
    border: 1px solid #000;
  }
}/* ==
=== Enhanced Responsive Design ===== */

/* Large screens (desktops) */
@media (min-width: 1200px) {
  .protocol-content {
    padding-left: calc(280px + 2rem);
  }
  
  .protocol-sidebar {
    width: 280px;
  }
  
  .medical-grid-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .risk-category-section,
  .vaccination-category-section {
    padding: 2rem;
  }
}

/* Medium screens (tablets) */
@media (max-width: 992px) {
  .protocol-layout {
    flex-direction: column;
  }
  
  .protocol-sidebar {
    position: relative;
    width: 100%;
    height: auto;
    padding: 1rem;
    border-right: none;
    border-bottom: 1px solid #e9ecef;
  }
  
  .protocol-content {
    padding: 1rem;
  }
  
  .medical-grid-3 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .risk-category-section,
  .vaccination-category-section {
    padding: 1.25rem;
  }
  
  .risk-item,
  .vaccination-item {
    padding: 1rem;
  }
  
  .medical-card {
    padding: 1.25rem;
  }
}

/* Small screens (mobile phones) */
@media (max-width: 768px) {
  .protocol-sidebar {
    padding: 0.75rem;
  }
  
  .protocol-content {
    padding: 0.75rem;
  }
  
  .medical-grid-2,
  .medical-grid-3 {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .risk-category-section,
  .vaccination-category-section {
    padding: 1rem;
    margin-bottom: 1rem;
  }
  
  .risk-item,
  .vaccination-item {
    padding: 0.75rem;
    flex-direction: column;
    text-align: center;
  }
  
  .risk-icon,
  .vaccination-icon {
    margin-right: 0;
    margin-bottom: 0.75rem;
    width: 36px;
    height: 36px;
    font-size: 1.1rem;
  }
  
  .risk-content,
  .vaccination-content {
    padding-right: 0;
  }
  
  .priority-badge {
    position: static;
    margin-top: 0.5rem;
    align-self: center;
  }
  
  .vaccination-timeline {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .timeline-step::after {
    display: none;
  }
  
  .medical-card {
    padding: 1rem;
  }
  
  .medical-card-header {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }
  
  .medical-card-icon {
    margin-right: 0;
    margin-bottom: 0.5rem;
  }
  
  .risk-section-header,
  .vaccination-section-header {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }
  
  .risk-section-icon,
  .vaccination-section-icon {
    margin-right: 0;
    margin-bottom: 0.5rem;
  }
  
  .btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }
  
  .form-control-lg,
  .form-select-lg {
    padding: 0.75rem 1rem;
    font-size: 1rem;
  }
}

/* Extra small screens */
@media (max-width: 576px) {
  .protocol-content {
    padding: 0.5rem;
  }
  
  .card {
    border-radius: 8px;
  }
  
  .medical-card,
  .risk-category-section,
  .vaccination-category-section {
    border-radius: 8px;
    padding: 0.75rem;
  }
  
  .risk-item,
  .vaccination-item {
    padding: 0.5rem;
  }
  
  .medical-card-header {
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
  }
  
  .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
  }
  
  .d-flex.justify-content-between .btn {
    width: 100%;
  }
  
  .alert {
    padding: 0.75rem;
  }
  
  .alert .fa-2x {
    font-size: 1.5rem !important;
  }
}

/* Landscape orientation adjustments */
@media (max-width: 768px) and (orientation: landscape) {
  .protocol-sidebar {
    display: none;
  }
  
  .protocol-content {
    padding: 0.5rem;
  }
  
  .medical-card {
    padding: 0.75rem;
  }
  
  .risk-item,
  .vaccination-item {
    flex-direction: row;
    text-align: left;
  }
  
  .risk-icon,
  .vaccination-icon {
    margin-right: 0.75rem;
    margin-bottom: 0;
  }
}

/* High DPI screens */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .medical-card,
  .risk-category-section,
  .vaccination-category-section {
    box-shadow: 0 2px 8px rgba(0,0,0,0.12);
  }
  
  .medical-card:hover,
  .risk-category-section:hover,
  .vaccination-category-section:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.18);
  }
}/* ==
=== Accessibility Enhancements ===== */

/* Focus indicators */
.risk-item:focus,
.vaccination-item:focus,
.medical-card:focus {
  outline: 3px solid #0d6efd;
  outline-offset: 2px;
  box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
}

/* Keyboard navigation */
.risk-item[tabindex],
.vaccination-item[tabindex] {
  cursor: pointer;
}

.risk-item:focus-visible,
.vaccination-item:focus-visible {
  outline: 3px solid #0d6efd;
  outline-offset: 2px;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .medical-card,
  .risk-category-section,
  .vaccination-category-section {
    border: 2px solid #000;
    background: #fff;
  }
  
  .risk-high {
    border-left-color: #000;
    background: #fff;
  }
  
  .risk-moderate {
    border-left-color: #000;
    background: #fff;
  }
  
  .risk-low {
    border-left-color: #000;
    background: #fff;
  }
  
  .severity-badge,
  .priority-badge {
    border: 2px solid #000;
    background: #fff;
    color: #000;
  }
  
  .medical-card-icon,
  .risk-icon,
  .vaccination-icon {
    background: #000 !important;
    color: #fff !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .medical-card:hover,
  .risk-category-section:hover,
  .vaccination-category-section:hover {
    transform: none;
  }
  
  .risk-item:hover,
  .vaccination-item:hover {
    transform: none;
  }
}

/* Color blind friendly patterns */
.severity-high::before {
  content: "⚠️";
  margin-right: 0.25rem;
}

.severity-moderate::before {
  content: "⚡";
  margin-right: 0.25rem;
}

.severity-low::before {
  content: "ℹ️";
  margin-right: 0.25rem;
}

.priority-required::before {
  content: "🔴";
  margin-right: 0.25rem;
}

.priority-recommended::before {
  content: "🟡";
  margin-right: 0.25rem;
}

.priority-optional::before {
  content: "🔵";
  margin-right: 0.25rem;
}

.priority-preventive::before {
  content: "🟢";
  margin-right: 0.25rem;
}

/* ARIA live regions */
.live-region {
  position: absolute;
  left: -10000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/* Skip links */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}

/* Enhanced form labels */
.form-label {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.form-label.required::after {
  content: " *";
  color: #dc3545;
}

/* Error states */
.form-control:invalid,
.form-select:invalid {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #dc3545;
}

/* Loading states accessibility */
.spinner-border {
  width: 2rem;
  height: 2rem;
}

.loading-text {
  margin-left: 0.75rem;
  font-weight: 500;
}

/* Button accessibility */
.btn:focus {
  outline: 3px solid #0d6efd;
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}

/* Table accessibility (for any remaining tables) */
.table th {
  background-color: #f8f9fa;
  font-weight: 600;
  border-bottom: 2px solid #dee2e6;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.025);
}

/* Print accessibility */
@media print {
  .medical-card,
  .risk-category-section,
  .vaccination-category-section {
    break-inside: avoid;
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }
  
  .severity-badge,
  .priority-badge {
    background: transparent !important;
    color: #000 !important;
    border: 1px solid #000 !important;
  }
  
  .medical-card-icon,
  .risk-icon,
  .vaccination-icon {
    background: #000 !important;
    color: #fff !important;
  }
  
  .btn,
  .protocol-sidebar,
  .skip-link {
    display: none !important;
  }
  
  .protocol-content {
    padding: 0 !important;
  }
  
  .alert {
    border: 1px solid #000 !important;
    background: #fff !important;
    color: #000 !important;
  }
}