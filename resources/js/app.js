/**
 * AI Doctor PHP - Main JavaScript File
 */

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize any components that need JavaScript functionality
    initializeFormValidation();
    initializeDropdowns();
    initializeAlerts();
});

/**
 * Initialize form validation
 */
function initializeFormValidation() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('border-red-500');
                    
                    // Add error message if it doesn't exist
                    let errorMessage = field.parentNode.querySelector('.error-message');
                    if (!errorMessage) {
                        errorMessage = document.createElement('p');
                        errorMessage.className = 'text-red-500 text-xs mt-1 error-message';
                        errorMessage.textContent = 'This field is required';
                        field.parentNode.appendChild(errorMessage);
                    }
                } else {
                    field.classList.remove('border-red-500');
                    const errorMessage = field.parentNode.querySelector('.error-message');
                    if (errorMessage) {
                        errorMessage.remove();
                    }
                }
            });
            
            if (!isValid) {
                event.preventDefault();
            }
        });
    });
}

/**
 * Initialize dropdown functionality
 */
function initializeDropdowns() {
    // Add any custom dropdown functionality here
    console.log('Dropdowns initialized');
}

/**
 * Initialize alert dismissal
 */
function initializeAlerts() {
    const alerts = document.querySelectorAll('.alert');
    
    alerts.forEach(alert => {
        const dismissButton = alert.querySelector('.dismiss-alert');
        if (dismissButton) {
            dismissButton.addEventListener('click', function() {
                alert.remove();
            });
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                alert.classList.add('opacity-0');
                setTimeout(() => {
                    alert.remove();
                }, 300);
            }, 5000);
        }
    });
}

/**
 * Multi-step form navigation
 */
function goToStep(currentStep, targetStep) {
    // This function can be used for client-side step navigation if needed
    console.log(`Navigating from step ${currentStep} to step ${targetStep}`);
    
    // For now, we're using server-side navigation, so this is just a placeholder
    const form = document.querySelector('form');
    if (form) {
        const stepInput = document.createElement('input');
        stepInput.type = 'hidden';
        stepInput.name = 'target_step';
        stepInput.value = targetStep;
        form.appendChild(stepInput);
        form.submit();
    }
}
