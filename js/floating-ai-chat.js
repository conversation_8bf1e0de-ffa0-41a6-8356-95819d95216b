/**
 * Floating AI Chat
 * JavaScript functionality for the floating AI chat component
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const aiChatButton = document.getElementById('aiChatButton');
    const aiChatPanel = document.getElementById('aiChatPanel');
    const aiChatClose = document.getElementById('aiChatClose');
    const aiChatForm = document.getElementById('aiChatForm');
    const aiQuestionInput = document.getElementById('aiQuestionInput');
    const aiSendBtn = document.getElementById('aiSendBtn');
    const aiChatMessages = document.getElementById('aiChatMessages');
    
    // Toggle chat panel
    aiChatButton.addEventListener('click', function() {
        aiChatButton.classList.toggle('active');
        aiChatPanel.classList.toggle('hidden');
        
        // Focus on input when opening
        if (!aiChatPanel.classList.contains('hidden')) {
            aiQuestionInput.focus();
        }
    });
    
    // Close chat panel
    aiChatClose.addEventListener('click', function() {
        aiChatPanel.classList.add('hidden');
        aiChatButton.classList.remove('active');
    });
    
    // Handle form submission
    aiChatForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const question = aiQuestionInput.value.trim();
        if (!question) return;
        
        // Add user message
        addMessage(question, 'user');
        
        // Clear input and disable send button
        aiQuestionInput.value = '';
        aiSendBtn.disabled = true;
        aiSendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        
        // Show typing indicator
        showTypingIndicator();
        
        // Make API call to backend - use concise mode for floating chat
        fetch(siteUrl + 'clinical-qa/ajax-ask-concise', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                question: question
            })
        })
        .then(response => response.json())
        .then(data => {
            hideTypingIndicator();
            
            if (data.success && data.response) {
                addMessage(data.response.answer, 'assistant');
            } else if (data.fallback) {
                addMessage(data.fallback, 'assistant');
            } else {
                addMessage('I apologize, but I encountered an error processing your question. Please try again.', 'assistant');
            }
            
            // Re-enable send button
            aiSendBtn.disabled = false;
            aiSendBtn.innerHTML = '<i class="fas fa-paper-plane"></i>';
            aiQuestionInput.focus();
        })
        .catch(error => {
            console.error('Error:', error);
            hideTypingIndicator();
            
            // Show error message
            addMessage('Sorry, I encountered a technical issue. Please try again later.', 'assistant');
            
            // Re-enable send button
            aiSendBtn.disabled = false;
            aiSendBtn.innerHTML = '<i class="fas fa-paper-plane"></i>';
            aiQuestionInput.focus();
        });
    });
    
    // Handle Enter key (Shift+Enter for new line)
    aiQuestionInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            aiChatForm.dispatchEvent(new Event('submit'));
        }
    });
    
    // Auto-resize textarea as user types
    aiQuestionInput.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
    
    // Add message to chat
    function addMessage(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;
        
        const currentTime = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        
        messageDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-${sender === 'user' ? 'user' : 'robot'}"></i>
            </div>
            <div class="message-content">
                <div class="message-text">${text}</div>
                <div class="message-time">${currentTime}</div>
            </div>
        `;
        
        aiChatMessages.appendChild(messageDiv);
        aiChatMessages.scrollTop = aiChatMessages.scrollHeight;
    }
    
    // Show typing indicator
    function showTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message assistant-message typing-message';
        typingDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="message-content">
                <div class="typing-indicator">
                    <span>AI is thinking</span>
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            </div>
        `;
        
        aiChatMessages.appendChild(typingDiv);
        aiChatMessages.scrollTop = aiChatMessages.scrollHeight;
    }
    
    // Hide typing indicator
    function hideTypingIndicator() {
        const typingMessage = aiChatMessages.querySelector('.typing-message');
        if (typingMessage) {
            typingMessage.remove();
        }
    }
    
    // Save chat state in session storage
    function saveChatState() {
        const messages = Array.from(aiChatMessages.querySelectorAll('.message:not(.typing-message)'))
            .map(msg => {
                const isUser = msg.classList.contains('user-message');
                const text = msg.querySelector('.message-text').innerText;
                const time = msg.querySelector('.message-time').innerText;
                return { sender: isUser ? 'user' : 'assistant', text, time };
            });
        
        sessionStorage.setItem('aiChatMessages', JSON.stringify(messages));
    }
    
    // Load chat state from session storage
    function loadChatState() {
        const savedMessages = sessionStorage.getItem('aiChatMessages');
        if (savedMessages) {
            try {
                const messages = JSON.parse(savedMessages);
                
                // Clear default welcome message
                aiChatMessages.innerHTML = '';
                
                // Add saved messages
                messages.forEach(msg => {
                    const messageDiv = document.createElement('div');
                    messageDiv.className = `message ${msg.sender}-message`;
                    
                    messageDiv.innerHTML = `
                        <div class="message-avatar">
                            <i class="fas fa-${msg.sender === 'user' ? 'user' : 'robot'}"></i>
                        </div>
                        <div class="message-content">
                            <div class="message-text">${msg.text}</div>
                            <div class="message-time">${msg.time}</div>
                        </div>
                    `;
                    
                    aiChatMessages.appendChild(messageDiv);
                });
                
                aiChatMessages.scrollTop = aiChatMessages.scrollHeight;
            } catch (e) {
                console.error('Error loading chat state:', e);
            }
        }
    }
    
    // Save chat state when messages are added or when page is unloaded
    window.addEventListener('beforeunload', saveChatState);
    const originalAddMessage = addMessage;
    addMessage = function(text, sender) {
        originalAddMessage(text, sender);
        saveChatState();
    };
    
    // Load chat state on initialization
    loadChatState();
});
