<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <h1 class="text-2xl font-semibold text-gray-900">AI Doctor Dashboard</h1>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">Clinical Decision Support System</p>
    </div>
    
    <!-- Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 px-4 py-5 sm:p-6">
        <div class="bg-blue-50 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-blue-500 rounded-md p-3">
                        <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Patients</dt>
                            <dd class="text-3xl font-semibold text-gray-900"><?= $patientCount ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-blue-100 px-4 py-4 sm:px-6">
                <div class="text-sm">
                    <a href="<?= base_url('patients') ?>" class="font-medium text-blue-700 hover:text-blue-900">View all patients</a>
                </div>
            </div>
        </div>
        
        <div class="bg-green-50 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-green-500 rounded-md p-3">
                        <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Upcoming Appointments</dt>
                            <dd class="text-3xl font-semibold text-gray-900"><?= $appointmentCount ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-green-100 px-4 py-4 sm:px-6">
                <div class="text-sm">
                    <a href="<?= base_url('appointments') ?>" class="font-medium text-green-700 hover:text-green-900">View all appointments</a>
                </div>
            </div>
        </div>
        
        <div class="bg-purple-50 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-purple-500 rounded-md p-3">
                        <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Clinical Protocols</dt>
                            <dd class="text-3xl font-semibold text-gray-900">3</dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-purple-100 px-4 py-4 sm:px-6">
                <div class="text-sm">
                    <a href="<?= base_url('consultations') ?>" class="font-medium text-purple-700 hover:text-purple-900">View all protocols</a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Access -->
    <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">Quick Access</h2>
        <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="<?= base_url('travel-medicine/create') ?>" class="bg-white overflow-hidden shadow rounded-lg border border-blue-200 hover:border-blue-500 transition-colors">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 bg-blue-100 rounded-md p-3">
                            <svg class="h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <h3 class="text-lg font-medium text-gray-900">Travel Medicine</h3>
                            <p class="mt-1 text-sm text-gray-500">Vaccination guidance and travel health recommendations</p>
                        </div>
                    </div>
                </div>
            </a>
            
            <a href="<?= base_url('std-testing/create') ?>" class="bg-white overflow-hidden shadow rounded-lg border border-green-200 hover:border-green-500 transition-colors">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 bg-green-100 rounded-md p-3">
                            <svg class="h-6 w-6 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <h3 class="text-lg font-medium text-gray-900">STD Testing</h3>
                            <p class="mt-1 text-sm text-gray-500">Sexual health screening protocols</p>
                        </div>
                    </div>
                </div>
            </a>
            
            <a href="<?= base_url('clinical-qa/create') ?>" class="bg-white overflow-hidden shadow rounded-lg border border-purple-200 hover:border-purple-500 transition-colors">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 bg-purple-100 rounded-md p-3">
                            <svg class="h-6 w-6 text-purple-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <h3 class="text-lg font-medium text-gray-900">Clinical Q&A</h3>
                            <p class="mt-1 text-sm text-gray-500">AI-powered medical guideline assistance</p>
                        </div>
                    </div>
                </div>
            </a>
        </div>
    </div>
    
    <!-- Today's Appointments -->
    <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">Today's Appointments</h2>
        <?php if (empty($todayAppointments)): ?>
            <p class="mt-2 text-sm text-gray-500">No appointments scheduled for today.</p>
        <?php else: ?>
            <div class="mt-4 flex flex-col">
                <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                    <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                        <div class="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reason</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th scope="col" class="relative px-6 py-3">
                                            <span class="sr-only">Actions</span>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php foreach ($todayAppointments as $appointment): ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="flex items-center">
                                                    <div class="flex-shrink-0 h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                                        <span class="text-gray-500 font-medium"><?= substr($appointment['first_name'], 0, 1) . substr($appointment['last_name'], 0, 1) ?></span>
                                                    </div>
                                                    <div class="ml-4">
                                                        <div class="text-sm font-medium text-gray-900"><?= e($appointment['first_name'] . ' ' . $appointment['last_name']) ?></div>
                                                        <div class="text-sm text-gray-500"><?= e($appointment['email']) ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900"><?= date('H:i', strtotime($appointment['appointment_date'])) ?></div>
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="text-sm text-gray-900"><?= e($appointment['reason_for_visit']) ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php
                                                $statusClass = 'bg-gray-100 text-gray-800';
                                                if ($appointment['status'] === 'scheduled') $statusClass = 'bg-green-100 text-green-800';
                                                if ($appointment['status'] === 'completed') $statusClass = 'bg-blue-100 text-blue-800';
                                                if ($appointment['status'] === 'cancelled') $statusClass = 'bg-red-100 text-red-800';
                                                if ($appointment['status'] === 'no_show') $statusClass = 'bg-yellow-100 text-yellow-800';
                                                ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?= $statusClass ?>">
                                                    <?= ucfirst($appointment['status']) ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <a href="<?= base_url('appointments/' . $appointment['id']) ?>" class="text-blue-600 hover:text-blue-900">View</a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Recent Consultations -->
    <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">Recent Consultations</h2>
        <?php if (empty($recentConsultations)): ?>
            <p class="mt-2 text-sm text-gray-500">No recent consultations.</p>
        <?php else: ?>
            <div class="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2">
                <?php foreach ($recentConsultations as $consultation): ?>
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg border border-gray-200">
                        <div class="px-4 py-4 sm:px-6">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg leading-6 font-medium text-gray-900">
                                    <?php
                                    $typeLabel = 'Consultation';
                                    $typeColor = 'text-gray-800';
                                    if ($consultation['consultation_type'] === 'travel_medicine') {
                                        $typeLabel = 'Travel Medicine';
                                        $typeColor = 'text-blue-800';
                                    } elseif ($consultation['consultation_type'] === 'std_testing') {
                                        $typeLabel = 'STD Testing';
                                        $typeColor = 'text-green-800';
                                    } elseif ($consultation['consultation_type'] === 'general_qa') {
                                        $typeLabel = 'Clinical Q&A';
                                        $typeColor = 'text-purple-800';
                                    }
                                    ?>
                                    <span class="<?= $typeColor ?>"><?= $typeLabel ?></span>
                                </h3>
                                <p class="text-sm text-gray-500">
                                    <?= format_date($consultation['created_at'], 'd M Y') ?>
                                </p>
                            </div>
                            <p class="mt-1 max-w-2xl text-sm text-gray-500">
                                <?= e($consultation['patient_name']) ?>
                            </p>
                        </div>
                        <div class="border-t border-gray-200 px-4 py-4 sm:px-6">
                            <div class="flex justify-between">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $consultation['status'] === 'completed' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800' ?>">
                                    <?= ucfirst($consultation['status']) ?>
                                </span>
                                <a href="<?= base_url('consultations/' . $consultation['id']) ?>" class="text-sm font-medium text-blue-600 hover:text-blue-500">
                                    View details
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>
