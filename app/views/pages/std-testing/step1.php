<div class="form-card">
    <!-- CSS for enhanced medical UI -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="<?= site_url('resources/css/protocol-steps.css') ?>">
    <link rel="stylesheet" href="<?= site_url('resources/css/medical-ui-enhanced.css') ?>">
    
    <div class="px-4 py-5 sm:px-6">
        <h1 class="text-2xl font-semibold text-gray-900">STD Testing Consultation</h1>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">Step 1 of 4: Patient Information</p>
    </div>
    
    <!-- Enhanced Step Progress Bar -->
    <div class="px-4 py-5 sm:px-6">
        <div class="step-progress">
            <?php foreach ($steps as $stepNumber => $stepInfo): ?>
                <div class="flex flex-col items-center">
                    <div class="step-circle <?= $stepNumber === $currentStep ? 'step-circle-active' : ($stepNumber < $currentStep ? 'step-circle-completed' : 'step-circle-inactive') ?>">
                        <?php if ($stepNumber < $currentStep): ?>
                            <i class="fas fa-check"></i>
                        <?php else: ?>
                            <?= $stepNumber ?>
                        <?php endif; ?>
                    </div>
                    <span class="step-title <?= $stepNumber === $currentStep ? 'step-title-active' : ($stepNumber < $currentStep ? 'step-title-completed' : 'step-title-inactive') ?>">
                        <?= $stepInfo['title'] ?>
                    </span>
                </div>
                
                <?php if ($stepNumber < count($steps)): ?>
                    <div class="step-line <?= $stepNumber < $currentStep ? 'step-line-completed' : 'step-line-inactive' ?>"></div>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
    </div>
    
    <div class="form-card-body">
        <form method="POST" action="<?= base_url('std-testing/processStep') ?>" class="space-y-8">
            <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
            <input type="hidden" name="current_step" value="<?= $currentStep ?>">
            <input type="hidden" name="next_step" value="<?= $currentStep + 1 ?>">
            
            <!-- Enhanced Patient Information -->
            <div class="medical-card">
                <div class="medical-card-header">
                    <div class="medical-card-icon" style="background: linear-gradient(135deg, #198754 0%, #157347 100%);">
                        <i class="fas fa-user"></i>
                    </div>
                    <h3 class="medical-card-title">Patient Selection</h3>
                </div>
                <div class="medical-card-body">
                    <div class="patient-selection-grid">
                        <div class="patient-selection-item">
                            <label for="patient_id" class="form-label medical-heading-3">Select Patient</label>
                            <select id="patient_id" name="patient_id" class="form-select form-control-lg" required>
                                <option value="">Choose a patient from your records</option>
                                <?php foreach ($patients as $patient): ?>
                                    <option value="<?= e($patient['id']) ?>" <?= $formData['patient_id'] === $patient['id'] ? 'selected' : '' ?>>
                                        👤 <?= e($patient['first_name'] . ' ' . $patient['last_name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <small class="form-text text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Select the patient who requires STD testing
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Demographics -->
            <div class="medical-card">
                <div class="medical-card-header">
                    <div class="medical-card-icon" style="background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);">
                        <i class="fas fa-id-card"></i>
                    </div>
                    <h3 class="medical-card-title">Demographics</h3>
                </div>
                <div class="medical-card-body">
                    <div class="demographics-grid">
                        <div class="demographic-item">
                            <label for="patient_age" class="form-label medical-heading-3">Age</label>
                            <input type="number" id="patient_age" name="patient_age" 
                                   value="<?= e($formData['patient_age']) ?>" 
                                   class="form-input form-control-lg" 
                                   required min="1" max="120"
                                   placeholder="Enter age in years">
                            <small class="form-text text-muted">
                                <i class="fas fa-birthday-cake me-1"></i>
                                Age affects testing recommendations and protocols
                            </small>
                        </div>
                        
                        <div class="demographic-item">
                            <label for="patient_gender" class="form-label medical-heading-3">Gender</label>
                            <select id="patient_gender" name="patient_gender" class="form-select form-control-lg" required>
                                <option value="">Select gender</option>
                                <option value="male" <?= $formData['patient_gender'] === 'male' ? 'selected' : '' ?>>
                                    ♂️ Male
                                </option>
                                <option value="female" <?= $formData['patient_gender'] === 'female' ? 'selected' : '' ?>>
                                    ♀️ Female
                                </option>
                                <option value="other" <?= $formData['patient_gender'] === 'other' ? 'selected' : '' ?>>
                                    ⚧️ Other
                                </option>
                            </select>
                            <small class="form-text text-muted">
                                <i class="fas fa-venus-mars me-1"></i>
                                Gender-specific testing considerations apply
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Testing History -->
            <div class="medical-card">
                <div class="medical-card-header">
                    <div class="medical-card-icon" style="background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);">
                        <i class="fas fa-history"></i>
                    </div>
                    <h3 class="medical-card-title">Testing History</h3>
                </div>
                <div class="medical-card-body">
                    <div class="testing-history-section">
                        <label for="last_testing" class="form-label medical-heading-3">Last STD Testing</label>
                        <select id="last_testing" name="last_testing" class="form-select form-control-lg" required>
                            <option value="">Select last testing timeframe</option>
                            <option value="never" <?= $formData['last_testing'] === 'never' ? 'selected' : '' ?>>
                                🚫 Never tested before
                            </option>
                            <option value="less_than_3_months" <?= $formData['last_testing'] === 'less_than_3_months' ? 'selected' : '' ?>>
                                🟢 Less than 3 months ago
                            </option>
                            <option value="3_to_6_months" <?= $formData['last_testing'] === '3_to_6_months' ? 'selected' : '' ?>>
                                🟡 3-6 months ago
                            </option>
                            <option value="6_to_12_months" <?= $formData['last_testing'] === '6_to_12_months' ? 'selected' : '' ?>>
                                🟠 6-12 months ago
                            </option>
                            <option value="more_than_12_months" <?= $formData['last_testing'] === 'more_than_12_months' ? 'selected' : '' ?>>
                                🔴 More than 12 months ago
                            </option>
                        </select>
                        <small class="form-text text-muted">
                            <i class="fas fa-clock me-1"></i>
                            Testing history helps determine appropriate screening intervals
                        </small>
                    </div>
                </div>
            </div>

            <div class="form-card-footer">
                <div class="flex justify-end">
                    <a href="<?= base_url('std-testing') ?>" class="btn btn-outline-secondary btn-lg mr-2">
                        <i class="fas fa-times me-2"></i>Cancel
                    </a>
                    <button type="submit" class="btn btn-primary btn-lg">
                        Next Step<i class="fas fa-arrow-right ms-2"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<style>
/* Enhanced STD Testing Step 1 Styles */
.patient-selection-grid {
    margin-top: 1rem;
}

.patient-selection-item {
    background: rgba(0,0,0,0.02);
    padding: 1.25rem;
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.2s ease;
}

.patient-selection-item:hover {
    border-color: #198754;
    box-shadow: 0 4px 12px rgba(25, 135, 84, 0.1);
}

.demographics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.demographic-item {
    background: rgba(0,0,0,0.02);
    padding: 1.25rem;
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.2s ease;
}

.demographic-item:hover {
    border-color: #0d6efd;
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.1);
}

.testing-history-section {
    background: rgba(0,0,0,0.02);
    padding: 1.25rem;
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.2s ease;
}

.testing-history-section:hover {
    border-color: #fd7e14;
    box-shadow: 0 4px 12px rgba(253, 126, 20, 0.1);
}

/* Step progress enhancements */
.step-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.step-circle-active {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.3);
}

.step-circle-completed {
    background: linear-gradient(135deg, #198754 0%, #157347 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(25, 135, 84, 0.3);
}

.step-circle-inactive {
    background: #e9ecef;
    color: #6c757d;
    border: 2px solid #dee2e6;
}

@media (max-width: 768px) {
    .demographics-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-populate age and gender if patient is selected
    const patientSelect = document.getElementById('patient_id');
    const ageInput = document.getElementById('patient_age');
    const genderSelect = document.getElementById('patient_gender');
    
    patientSelect.addEventListener('change', function() {
        // In a real implementation, you would fetch patient data via AJAX
        // For now, we'll just clear the fields if patient changes
        if (this.value) {
            // This would be replaced with actual patient data
            // ageInput.value = patientData.age;
            // genderSelect.value = patientData.gender;
        }
    });
    
    // Add visual feedback for form validation
    const requiredFields = document.querySelectorAll('[required]');
    requiredFields.forEach(field => {
        field.addEventListener('blur', function() {
            if (this.value.trim() === '') {
                this.style.borderColor = '#dc3545';
            } else {
                this.style.borderColor = '#198754';
            }
        });
    });
});
</script>