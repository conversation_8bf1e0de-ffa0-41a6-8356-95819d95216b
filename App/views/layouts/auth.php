<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? $title . ' - ' . APP_NAME : APP_NAME ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?= asset('css/styles.css') ?>">
    
    <!-- Favicon -->
    <link rel="icon" href="<?= asset('images/favicon.ico') ?>" type="image/x-icon">
</head>
<body class="bg-gray-50 min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <div class="text-center">
            <h2 class="text-3xl font-extrabold text-blue-600"><?= APP_NAME ?></h2>
            <p class="mt-2 text-sm text-gray-600">
                Clinical Decision Support System
            </p>
        </div>
        
        <!-- Flash Messages -->
        <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
            <?php $flashes = get_flashes(); ?>
            <?php if (!empty($flashes)): ?>
                <?php foreach ($flashes as $type => $message): ?>
                    <?php
                    $alertClass = 'bg-gray-100 border-gray-500';
                    if ($type === 'success') $alertClass = 'bg-green-100 border-green-500';
                    if ($type === 'error') $alertClass = 'bg-red-100 border-red-500';
                    if ($type === 'info') $alertClass = 'bg-blue-100 border-blue-500';
                    if ($type === 'warning') $alertClass = 'bg-yellow-100 border-yellow-500';
                    ?>
                    <div class="mb-4 border-l-4 p-4 <?= $alertClass ?>">
                        <div class="flex">
                            <div class="flex-1">
                                <p class="text-sm"><?= e($message) ?></p>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
        <!-- Page Content -->
        <div class="mt-2 sm:mx-auto sm:w-full sm:max-w-md">
            <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                <?= $content ?? '' ?>
            </div>
        </div>
    </div>
    
    <!-- Footer -->
    <footer class="mt-8">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <p class="text-center text-sm text-gray-500">
                &copy; <?= date('Y') ?> <?= APP_NAME ?>. All rights reserved.
            </p>
        </div>
    </footer>
    
    <!-- Scripts -->
    <script src="<?= asset('js/app.js') ?>"></script>
    
    <?php if (isset($scripts)): ?>
        <?= $scripts ?>
    <?php endif; ?>
</body>
</html>
