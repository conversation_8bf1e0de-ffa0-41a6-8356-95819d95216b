<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? $title . ' - ' . APP_NAME : APP_NAME ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js -->
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Alpine.js styles -->
    <style>
        [x-cloak] { display: none !important; }
    </style>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="https://vaccination-ai.allstackdev.tech/resources/css/styles.css">
    <link rel="stylesheet" href="https://vaccination-ai.allstackdev.tech/resources/css/calendar.css">
    
    <!-- Favicon -->
    <link rel="icon" href="<?= asset('images/favicon.ico') ?>" type="image/x-icon">
</head>
<body class="bg-gray-50 min-h-screen flex flex-col">
    <!-- Header -->
    <header class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <a href="<?= base_url() ?>" class="text-xl font-bold text-blue-600">
                            <?= APP_NAME ?>
                        </a>
                    </div>
                    
                    <?php if (is_logged_in()): ?>
                    <nav class="ml-6 flex space-x-8">
                        <a href="<?= base_url() ?>" class="<?= is_active('') ? 'border-blue-500 text-gray-900' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700' ?> inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Dashboard
                        </a>
                        <a href="<?= base_url('patients') ?>" class="<?= is_active('patients') ? 'border-blue-500 text-gray-900' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700' ?> inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Patients
                        </a>
                        <a href="<?= base_url('appointments') ?>" class="<?= is_active('appointments') ? 'border-blue-500 text-gray-900' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700' ?> inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Appointments
                        </a>
                        <a href="<?= base_url('calendar') ?>" class="<?= is_active('calendar') ? 'border-blue-500 text-gray-900' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700' ?> inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Calendar
                        </a>
                        <a href="<?= base_url('consultations') ?>" class="<?= is_active('consultations') ? 'border-blue-500 text-gray-900' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700' ?> inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Consultations
                        </a>
                    </nav>
                    <?php endif; ?>
                </div>
                
                <div class="flex items-center">
                    <?php if (is_logged_in()): ?>
                    <div class="ml-3 relative">
                        <div>
                            <button type="button" class="max-w-xs bg-white flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" id="user-menu-button" aria-expanded="false" aria-haspopup="true">
                                <span class="sr-only">Open user menu</span>
                                <span class="inline-block h-8 w-8 rounded-full overflow-hidden bg-gray-100">
                                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                                    </svg>
                                </span>
                            </button>
                        </div>
                        <div class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none hidden" role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button" tabindex="-1" id="user-menu">
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem" tabindex="-1">Your Profile</a>
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem" tabindex="-1">Settings</a>
                            <a href="<?= base_url('logout') ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem" tabindex="-1">Sign out</a>
                        </div>
                    </div>
                    <?php else: ?>
                    <a href="<?= base_url('login') ?>" class="text-sm font-medium text-blue-600 hover:text-blue-500">
                        Log in
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </header>
    
    <!-- Main Content -->
    <main class="flex-grow">
        <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <!-- Flash Messages -->
            <?php $flashes = get_flashes(); ?>
            <?php if (!empty($flashes)): ?>
                <?php foreach ($flashes as $type => $message): ?>
                    <?php
                    $alertClass = 'bg-gray-100 border-gray-500';
                    if ($type === 'success') $alertClass = 'bg-green-100 border-green-500';
                    if ($type === 'error') $alertClass = 'bg-red-100 border-red-500';
                    if ($type === 'info') $alertClass = 'bg-blue-100 border-blue-500';
                    if ($type === 'warning') $alertClass = 'bg-yellow-100 border-yellow-500';
                    ?>
                    <div class="mb-4 border-l-4 p-4 <?= $alertClass ?>">
                        <div class="flex">
                            <div class="flex-1">
                                <p class="text-sm"><?= e($message) ?></p>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
            
            <!-- Page Content -->
            <?= $content ?? '' ?>
        </div>
    </main>
    
    <!-- Footer -->
    <footer class="bg-white">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <p class="text-center text-sm text-gray-500">
                &copy; <?= date('Y') ?> <?= APP_NAME ?>. All rights reserved.
            </p>
        </div>
    </footer>
    
    <!-- Floating AI Chat Component -->
    <?php if (is_logged_in()): ?>
        <?php include_once APP_ROOT . '/app/views/components/floating-ai-chat.php'; ?>
    <?php endif; ?>
    
    <!-- Scripts -->
    <script src="<?= asset('js/app.js') ?>"></script>
    <script>
        // Site URL for JavaScript
        const siteUrl = '<?= site_url() ?>';
        
        // Toggle user menu
        document.getElementById('user-menu-button')?.addEventListener('click', function() {
            document.getElementById('user-menu').classList.toggle('hidden');
        });
        
        // Close user menu when clicking outside
        document.addEventListener('click', function(event) {
            const userMenu = document.getElementById('user-menu');
            const userMenuButton = document.getElementById('user-menu-button');
            
            if (userMenu && !userMenu.contains(event.target) && !userMenuButton.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });
    </script>
    
    <!-- Floating AI Chat Script -->
    <?php if (is_logged_in()): ?>
        <!-- Using the siteUrl already defined above for AJAX requests -->
        <script src="<?= base_url('js/floating-ai-chat.js') ?>"></script>
    <?php endif; ?>
    
    <?php if (isset($scripts)): ?>
        <?= $scripts ?>
    <?php endif; ?>
</body>
</html>
