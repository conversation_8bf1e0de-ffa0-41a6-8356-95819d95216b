<?php
/**
 * Travel Protocol - Step Navigation Component
 */
?>

<div class="protocol-steps">
    <h5 class="mb-3">Protocol Steps</h5>
    
    <!-- Progress bar -->
    <div class="progress protocol-progress">
        <div class="progress-bar" role="progressbar" style="width: <?= ($step / 5) * 100 ?>%" 
             aria-valuenow="<?= $step ?>" aria-valuemin="0" aria-valuemax="5"></div>
    </div>
    
    <div class="list-group">
        <div class="list-group-item <?= $step >= 1 ? ($step > 1 ? 'completed' : 'active') : '' ?>">
            <div class="d-flex align-items-center">
                <div class="step-number <?= $step > 1 ? 'bg-success' : ($step == 1 ? 'bg-primary' : 'bg-secondary') ?>">1</div>
                <div class="ms-2">
                    <h6>Travel Information</h6>
                    <small>Collect destination and travel details</small>
                </div>
            </div>
        </div>
        <div class="list-group-item <?= $step >= 2 ? ($step > 2 ? 'completed' : 'active') : 'disabled' ?>">
            <div class="d-flex align-items-center">
                <div class="step-number <?= $step > 2 ? 'bg-success' : ($step == 2 ? 'bg-primary' : 'bg-secondary') ?>">2</div>
                <div class="ms-2">
                    <h6>Patient Assessment</h6>
                    <small>Medical history and contraindications</small>
                </div>
            </div>
        </div>
        <div class="list-group-item <?= $step >= 3 ? ($step > 3 ? 'completed' : 'active') : 'disabled' ?>">
            <div class="d-flex align-items-center">
                <div class="step-number <?= $step > 3 ? 'bg-success' : ($step == 3 ? 'bg-primary' : 'bg-secondary') ?>">3</div>
                <div class="ms-2">
                    <h6>Risk Analysis</h6>
                    <small>Evaluate destination-specific risks</small>
                </div>
            </div>
        </div>
        <div class="list-group-item <?= $step >= 4 ? ($step > 4 ? 'completed' : 'active') : 'disabled' ?>">
            <div class="d-flex align-items-center">
                <div class="step-number <?= $step > 4 ? 'bg-success' : ($step == 4 ? 'bg-primary' : 'bg-secondary') ?>">4</div>
                <div class="ms-2">
                    <h6>Vaccination Plan</h6>
                    <small>Determine required and recommended vaccines</small>
                </div>
            </div>
        </div>
        <div class="list-group-item <?= $step >= 5 ? 'active' : 'disabled' ?>">
            <div class="d-flex align-items-center">
                <div class="step-number <?= $step == 5 ? 'bg-primary' : 'bg-secondary' ?>">5</div>
                <div class="ms-2">
                    <h6>Documentation</h6>
                    <small>Generate patient information and records</small>
                </div>
            </div>
        </div>
    </div>
</div>
