<?php
/**
 * Travel Protocol - Step 4: Vaccination Plan
 */
?>

<!-- CSS for protocol steps styling -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="<?= site_url('resources/css/protocol-steps.css') ?>">
<link rel="stylesheet" href="<?= site_url('resources/css/medical-ui-enhanced.css') ?>">
<link rel="stylesheet" href="<?= site_url('resources/css/risk-cards.css') ?>">
<link rel="stylesheet" href="<?= site_url('resources/css/vaccination-plan.css') ?>">

<style>
    /* Vaccination item styling */
    .risk-item.vaccination-item {
        position: relative;
        padding-left: 3rem;
        transition: all 0.3s ease;
        border-radius: 12px;
        margin-bottom: 1.25rem;
        background: none;
        border: none;
        box-shadow: none;
    }
    
    .risk-item.vaccination-item:hover .vaccination-item-content-wrapper {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.12);
    }
    
    .vaccination-item-icon {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        color: white;
        flex-shrink: 0;
    }
    
    .vaccination-item-icon.required {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        box-shadow: 0 2px 5px rgba(220, 53, 69, 0.3);
    }
    
    .vaccination-item-icon.recommended {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        box-shadow: 0 2px 5px rgba(255, 193, 7, 0.3);
    }
    
    .vaccination-item-header {
        display: flex;
        align-items: center;
        padding: 12px 15px 0;
        background: none;
    }
    
    .vaccination-item-content-wrapper {
        flex: 1;
        display: flex;
        flex-direction: column;
        background-color: #fff;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
    }
    
    .vaccination-item-content {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
    }
    
    .vaccination-item-title {
        font-weight: 600;
        margin: 0;
        font-size: 1rem;
    }
    
    .vaccination-badge {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 0.65rem;
        font-weight: 700;
        letter-spacing: 0.5px;
        margin-bottom: 5px;
        text-transform: uppercase;
    }
    
    .vaccination-badge.required {
        background-color: rgba(220, 53, 69, 0.15);
        color: #c82333;
    }
    
    .vaccination-badge.recommended {
        background-color: rgba(255, 193, 7, 0.15);
        color: #e0a800;
    }
    
    .vaccination-item-body {
        padding: 12px 15px 15px;
        background-color: #fff;
    }
    
    .risk-description {
        color: #555;
        font-size: 0.9rem;
        line-height: 1.5;
    }
    
    /* Selected vaccinations section styling */
    .selected-vaccinations {
        background-color: #f8f9fa;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 3px 10px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
    }
    
    .selected-header {
        display: flex;
        align-items: center;
        margin-bottom: 1.25rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }
    
    .selected-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        box-shadow: 0 2px 5px rgba(78, 115, 223, 0.3);
        flex-shrink: 0;
    }
    
    .selected-title {
        font-weight: 600;
        margin: 0;
        flex-grow: 1;
    }
    
    .selected-count {
        background-color: rgba(78, 115, 223, 0.15);
        color: #224abe;
        padding: 4px 10px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
    }
</style>

<div class="protocol-layout">
    <!-- Left sidebar with steps -->
    <div class="protocol-sidebar">
        <?php include __DIR__ . '/step-nav.php'; ?>
    </div>
    
    <!-- Main content area -->
    <div class="protocol-content">
        <div class="card shadow-sm">
            <div class="card-header bg-white">
                <h4 class="mb-0">Vaccination Plan</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-info mb-4">
                    <div class="d-flex">
                        <div class="me-3">
                            <i class="fas fa-syringe fa-2x text-info"></i>
                        </div>
                        <div>
                            <h5 class="alert-heading">Destination: <?= htmlspecialchars($protocol_data['destination'] ?? 'Unknown') ?></h5>
                            <p class="mb-0">Based on your destination, travel details, health profile, and selected vaccinations, we've created your personalized vaccination plan.</p>
                        </div>
                    </div>
                </div>
                
                <?php if (!empty($selected_vaccinations)): ?>
                <!-- Selected Vaccinations Section -->
                <div class="selected-vaccinations mb-4">
                    <div class="selected-header">
                        <div class="selected-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h3 class="selected-title">Selected Vaccinations</h3>
                        <div class="selected-count"><?= count($selected_vaccinations) ?> vaccines</div>
                    </div>
                    
                    <div class="alert alert-light border mb-4">
                        <i class="fas fa-info-circle me-2"></i>
                        These are the vaccinations you selected in the previous step. The doctor will review these selections and provide a final recommendation.
                    </div>
                    
                    <div class="row">
                        <?php foreach ($selected_vaccinations as $index => $vaccine): 
                            $isRequired = isset($vaccine['required']) && $vaccine['required'];
                            $animationDelay = ($index % 3) + 1;
                        ?>
                        <div class="col-md-6 mb-3">
                            <div class="vaccination-item <?= $isRequired ? 'required' : 'recommended' ?> fade-in-delay-<?= $animationDelay ?>">
                                <div class="vaccination-item-content-wrapper">
                                    <div class="vaccination-item-header">
                                        <div class="vaccination-item-icon <?= $isRequired ? 'required' : 'recommended' ?>">
                                            <i class="fas fa-<?= $isRequired ? 'syringe' : 'shield-virus' ?>"></i>
                                        </div>
                                        <div class="vaccination-item-content">
                                            <div class="vaccination-badge <?= $isRequired ? 'required' : 'recommended' ?>">
                                                <?= $isRequired ? 'REQUIRED' : 'RECOMMENDED' ?>
                                            </div>
                                            <h5 class="vaccination-item-title"><?= htmlspecialchars($vaccine['title']) ?></h5>
                                        </div>
                                    </div>
                                    <div class="vaccination-item-body">
                                        <div class="risk-description"><?= htmlspecialchars($vaccine['description'] ?? '') ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <form action="<?= site_url('travel-medicine/process-step') ?>" method="post">
                    <?= csrf_field() ?>
                    <input type="hidden" name="step" value="4">
                    <input type="hidden" id="vaccination-plan-input" name="vaccination_plan" value="">
                    
                    <!-- Loading spinner (shown initially only if AI recommendations are expected) -->
                    <div id="vaccination-loading" class="loading-container" style="<?= isset($ai_recommendations) && !empty($ai_recommendations) ? '' : 'display: none;' ?>">
                        <div class="loading-spinner"></div>
                        <p class="loading-text">Generating vaccination recommendations for <?= htmlspecialchars($protocol_data['destination'] ?? 'your destination') ?>...</p>
                    </div>
                    
                    <!-- Debug info (hidden in production) -->
                    <?php if (isset($ai_recommendations)): ?>
                    <div class="alert alert-info" style="display: none;">
                        <strong>Debug:</strong> AI recommendations are available in the view.
                        <pre style="display: none;"><?php print_r($ai_recommendations); ?></pre>
                    </div>
                    <?php else: ?>
                    <div class="alert alert-warning" style="display: none;">
                        <strong>Debug:</strong> No AI recommendations available in the view.
                    </div>
                    <?php endif; ?>
                    

                    
                    <!-- AI-Generated Vaccination Plan -->
                    <div id="vaccination-content" class="mt-4 fade-in" style="display: none;">
                        <!-- Content will be populated by JavaScript -->
                    </div>
                    
                    <?php if (isset($ai_recommendations)): ?>
                    <!-- Static AI Vaccination Recommendations (fallback) -->
                    <div class="medical-card" id="static-vaccination-content">
                            <div class="medical-card-header">
                                <div class="medical-card-icon" style="background: linear-gradient(135deg, #198754 0%, #157347 100%);">
                                    <i class="fas fa-syringe"></i>
                                </div>
                                <h3 class="medical-card-title">AI Vaccination Plan</h3>
                                <div class="ai-badge">
                                    <i class="fas fa-microchip me-1"></i>
                                    <?= htmlspecialchars($ai_recommendations['model_used'] ?? 'AI Generated') ?>
                                </div>
                            </div>
                            <div class="medical-card-body">
                                <!-- Check if we have parsed sections or use full response -->
                                <?php 
                                $hasParsedSections = (
                                    (!empty($ai_recommendations['required_vaccinations']) && $ai_recommendations['required_vaccinations'] !== 'Information not available in AI response') ||
                                    (!empty($ai_recommendations['recommended_vaccinations']) && $ai_recommendations['recommended_vaccinations'] !== 'Information not available in AI response') ||
                                    (!empty($ai_recommendations['malaria_prevention']) && $ai_recommendations['malaria_prevention'] !== 'Information not available in AI response')
                                );
                                ?>
                                
                                <?php if ($hasParsedSections): ?>
                                    <!-- Required Vaccinations -->
                                    <?php if (!empty($ai_recommendations['required_vaccinations']) && $ai_recommendations['required_vaccinations'] !== 'Information not available in AI response'): ?>
                                    <div class="vaccination-section">
                                        <h4 class="vaccination-section-title">
                                            <i class="fas fa-exclamation-circle text-danger me-2"></i>
                                            Required Vaccinations
                                        </h4>
                                        <div class="vaccination-content-box required">
                                            <?= nl2br(htmlspecialchars($ai_recommendations['required_vaccinations'])) ?>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <!-- Recommended Vaccinations -->
                                    <?php if (!empty($ai_recommendations['recommended_vaccinations']) && $ai_recommendations['recommended_vaccinations'] !== 'Information not available in AI response'): ?>
                                    <div class="vaccination-section">
                                        <h4 class="vaccination-section-title">
                                            <i class="fas fa-shield-alt text-warning me-2"></i>
                                            Recommended Vaccinations
                                        </h4>
                                        <div class="vaccination-content-box recommended">
                                            <?= nl2br(htmlspecialchars($ai_recommendations['recommended_vaccinations'])) ?>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <!-- Malaria Prevention (if applicable) -->
                                    <?php if (!empty($ai_recommendations['malaria_prevention']) && $ai_recommendations['malaria_prevention'] !== 'Information not available in AI response'): ?>
                                    <div class="vaccination-section">
                                        <h4 class="vaccination-section-title">
                                            <i class="fas fa-bug text-danger me-2"></i>
                                            Malaria Prevention
                                        </h4>
                                        <div class="vaccination-content-box malaria">
                                            <?= nl2br(htmlspecialchars($ai_recommendations['malaria_prevention'])) ?>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <!-- Special Considerations -->
                                    <?php if (!empty($ai_recommendations['special_considerations']) && $ai_recommendations['special_considerations'] !== 'Information not available in AI response'): ?>
                                    <div class="vaccination-section">
                                        <h4 class="vaccination-section-title">
                                            <i class="fas fa-user-md text-info me-2"></i>
                                            Special Considerations
                                        </h4>
                                        <div class="vaccination-content-box special">
                                            <?= nl2br(htmlspecialchars($ai_recommendations['special_considerations'])) ?>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <!-- Show full AI response if parsing failed -->
                                    <div class="vaccination-section">
                                        <h4 class="vaccination-section-title">
                                            <i class="fas fa-syringe text-success me-2"></i>
                                            AI Vaccination Recommendations
                                        </h4>
                                        <div class="vaccination-content-box recommended">
                                            <?= nl2br(htmlspecialchars($ai_recommendations['full_response'])) ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Guidelines Sources -->
                                <?php if (!empty($ai_recommendations['sources'])): ?>
                                <div class="ai-sources">
                                    <h5 class="sources-title">
                                        <i class="fas fa-book me-2"></i>
                                        Guidelines Referenced
                                    </h5>
                                    <div class="sources-list">
                                        <?php foreach ($ai_recommendations['sources'] as $source): ?>
                                            <span class="source-badge"><?= htmlspecialchars($source) ?></span>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- Store vaccination plan data -->
                        <input type="hidden" name="vaccination_plan" value="<?= htmlspecialchars(json_encode($ai_recommendations)) ?>">
                    <?php else: ?>
                    <div id="vaccination-content" class="mt-4 fade-in" style="display: none;">
                        <!-- Dynamic content will be inserted here by JavaScript -->
                    </div>
                        
                        <div class="alert alert-info mt-4">
                            <div class="d-flex">
                                <div class="me-3">
                                    <i class="fas fa-info-circle fa-2x text-info"></i>
                                </div>
                                <div>
                                    <h6 class="alert-heading">Important Note</h6>
                                    <p class="mb-0">This plan is based on current CDC and WHO recommendations for <?= htmlspecialchars($protocol_data['destination'] ?? 'this region') ?>. Individual recommendations may vary based on patient-specific factors, medical history, and current health status.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <div class="d-flex justify-content-between mt-4">
                        <a href="<?= site_url('travel-medicine/create/3') ?>" class="btn btn-outline-secondary btn-lg">
                            <i class="fas fa-arrow-left me-2"></i>Previous
                        </a>
                        <button type="submit" class="btn btn-primary btn-lg">
                            Next<i class="fas fa-arrow-right ms-2"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    // Immediately execute on page load without waiting for DOMContentLoaded
    (function() {
        // Hide any loading elements immediately
        const loadingElement = document.getElementById('vaccination-loading');
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
        
        // Use existing AI recommendations from PHP if available
        <?php if (isset($ai_recommendations) && !empty($ai_recommendations)): ?>
            console.log('Using existing AI recommendations from session');
            const recommendations = <?= json_encode($ai_recommendations) ?>;
            // Small timeout to ensure DOM is ready
            setTimeout(() => displayVaccinationPlan(recommendations), 10);
        <?php else: ?>
            console.log('No AI recommendations found in session, showing fallback content');
            // Store fallback data in hidden input
            const inputElement = document.getElementById('vaccination-plan-input');
            if (inputElement) {
                const fallbackData = {
                    required_vaccinations: 'Standard travel vaccinations based on destination risk profile',
                    recommended_vaccinations: 'Consult current guidelines for recommended vaccinations',
                    malaria_prevention: 'Follow local malaria prevention guidelines if applicable',
                    special_considerations: 'Consider patient-specific factors and medical history'
                };
                inputElement.value = JSON.stringify(fallbackData);
            }
            // Small timeout to ensure DOM is ready
            setTimeout(showFallbackContent, 10);
        <?php endif; ?>
    })();
    
    function displayVaccinationPlan(recommendations) {
        // Hide loading spinner
        const loadingElement = document.getElementById('vaccination-loading');
        const contentElement = document.getElementById('vaccination-content');
        const inputElement = document.getElementById('vaccination-plan-input');
        
        // Get selected vaccinations from PHP
        const selectedVaccinations = <?= isset($selected_vaccinations) && !empty($selected_vaccinations) ? json_encode($selected_vaccinations) : '[]' ?>;
        
        // Hide debug info
        const debugElements = document.querySelectorAll('.alert-info, .alert-warning');
        debugElements.forEach(el => el.style.display = 'none');
        
        // Safety check for required elements
        if (!loadingElement || !contentElement || !inputElement) {
            console.error('Required DOM elements not found');
            return;
        }
        
        // Always hide loading spinner
        loadingElement.style.display = 'none';
        contentElement.style.display = 'block';
        
        // Add selected vaccinations to recommendations
        if (selectedVaccinations && selectedVaccinations.length > 0) {
            recommendations.selected_vaccinations = selectedVaccinations;
        }
        
        // Store recommendations in hidden input for form submission
        inputElement.value = JSON.stringify(recommendations);
        
        // Clear previous content
        contentElement.innerHTML = '';
        
        // Check if we have valid recommendations
        if (!recommendations || typeof recommendations !== 'object') {
            console.error('Invalid recommendations format');
            showFallbackContent();
            return;
        }
        
        // Create AI recommendation card container
        const aiRecommendationCard = document.createElement('div');
        aiRecommendationCard.className = 'ai-recommendation-card';
        
        // Add AI recommendation card header
        const cardHeader = document.createElement('div');
        cardHeader.className = 'ai-recommendation-header';
        cardHeader.innerHTML = `
            <div class="ai-recommendation-icon">
                <i class="fas fa-syringe"></i>
            </div>
            <h3 class="ai-recommendation-title">AI Vaccination Plan</h3>
            <div class="ai-badge">
                <i class="fas fa-microchip"></i>
                ${recommendations.model_used || 'AI Generated'}
            </div>
        `;
        
        // Create card body
        const cardBody = document.createElement('div');
        cardBody.className = 'card-body p-4';
        
        // Add header and body to card
        aiRecommendationCard.appendChild(cardHeader);
        aiRecommendationCard.appendChild(cardBody);
        
        // Add card to content element
        contentElement.appendChild(aiRecommendationCard);
        
        // Use cardBody as the container for sections instead of contentElement
        contentElement = cardBody;
            
        // Check if we have valid sections
        let hasValidSections = false;
        
        // Parse and display required vaccinations
        if (recommendations.required_vaccinations && 
            recommendations.required_vaccinations !== 'Information not available in AI response') {
            const requiredItems = parseVaccinationItems(recommendations.required_vaccinations);
            if (requiredItems && requiredItems.length > 0) {
                createVaccinationSection('Required Vaccinations', requiredItems, 'high', 'fas fa-shield-virus', contentElement);
                hasValidSections = true;
            }
        }
        
        // Parse and display recommended vaccinations
        if (recommendations.recommended_vaccinations && 
            recommendations.recommended_vaccinations !== 'Information not available in AI response') {
            const recommendedItems = parseVaccinationItems(recommendations.recommended_vaccinations);
            if (recommendedItems && recommendedItems.length > 0) {
                createVaccinationSection('Recommended Vaccinations', recommendedItems, 'medium', 'fas fa-syringe', contentElement);
                hasValidSections = true;
            }
        }
        
        // Parse and display malaria prevention
        if (recommendations.malaria_prevention && 
            recommendations.malaria_prevention !== 'Information not available in AI response') {
            const malariaItems = parseVaccinationItems(recommendations.malaria_prevention);
            if (malariaItems && malariaItems.length > 0) {
                createVaccinationSection('Malaria Prevention', malariaItems, 'medium', 'fas fa-mosquito', contentElement);
                hasValidSections = true;
            }
        }
        
        // Parse and display special considerations
        if (recommendations.special_considerations && 
            recommendations.special_considerations !== 'Information not available in AI response') {
            const specialItems = parseVaccinationItems(recommendations.special_considerations);
            if (specialItems && specialItems.length > 0) {
                createVaccinationSection('Special Considerations', specialItems, 'low', 'fas fa-notes-medical', contentElement);
                hasValidSections = true;
            }
        }
        
        // If no valid sections were found, try to parse the full response
        if (!hasValidSections && recommendations.full_response) {
            console.log('No valid sections found, using full response');
            const fullResponseItems = parseVaccinationItems(recommendations.full_response);
            if (fullResponseItems && fullResponseItems.length > 0) {
                createVaccinationSection('Vaccination Recommendations', fullResponseItems, 'medium', 'fas fa-syringe', contentElement);
            } else {
                // If still no valid content, show fallback
                showFallbackContent();
                return;
            }
        }
        
        // Add sources if available
        if (recommendations.sources && Array.isArray(recommendations.sources) && recommendations.sources.length > 0) {
            const sourcesDiv = document.createElement('div');
            sourcesDiv.className = 'ai-sources';
            sourcesDiv.innerHTML = `
                <h5 class="sources-title">
                    <i class="fas fa-book me-2"></i>
                    Guidelines Referenced
                </h5>
                <div class="sources-list">
                    ${recommendations.sources.map(source => `<span class="source-badge">${source}</span>`).join('')}
                </div>
            `;
            contentElement.appendChild(sourcesDiv);
        }
        
        // Add disclaimer
        const disclaimer = document.createElement('div');
        disclaimer.className = 'disclaimer';
        disclaimer.innerHTML = `
            <i class="fas fa-info-circle"></i>
            <div>
                <strong>Medical Disclaimer:</strong> The information provided is based on current guidelines and may change over time. 
                Always consult a travel health clinic or specialist for the most up-to-date advice tailored to your specific situation.
            </div>
        `;
        contentElement.appendChild(disclaimer);
    }
    
    function parseVaccinationItems(text) {
        if (!text || typeof text !== 'string') {
            return ['No information available. Please consult current guidelines.'];
        }
        
        // Split by newlines and filter empty lines
        const lines = text.split(/\n|\r/).filter(line => line.trim());
        
        // If no lines found, return default message
        if (lines.length === 0) {
            return ['No specific information available. Please consult current guidelines.'];
        }
        
        // Clean up lines (remove dashes, etc.)
        return lines.map(line => {
            // Remove leading dashes, asterisks, or other list markers
            return line.trim().replace(/^[-\u2013\u2022*]\s*/, '');
        });
    }
    
    function createVaccinationSection(title, items, severity, icon, container) {
        const sectionDiv = document.createElement('div');
        sectionDiv.className = 'risk-section mb-4 fade-in';
        
        // Create section header
        const headerDiv = document.createElement('div');
        headerDiv.className = 'risk-section-header';
        
        let bgGradient = 'linear-gradient(135deg, #3182ce 0%, #2c5282 100%)';
        if (title.includes('Required')) {
            bgGradient = 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)';
        } else if (title.includes('Recommended')) {
            bgGradient = 'linear-gradient(135deg, #ffc107 0%, #e0a800 100%)';
        } else if (title.includes('Malaria')) {
            bgGradient = 'linear-gradient(135deg, #805ad5 0%, #6b46c1 100%)';
        } else if (title.includes('Special')) {
            bgGradient = 'linear-gradient(135deg, #17a2b8 0%, #138496 100%)';
        }
        
        headerDiv.style.background = bgGradient;
        
        headerDiv.innerHTML = `
            <div class="risk-section-icon">
                <i class="${icon}"></i>
            </div>
            <h3 class="risk-section-title">${title}</h3>
        `;
        
        sectionDiv.appendChild(headerDiv);
        
        // Process items and create risk items
        items.forEach((item, index) => {
            const itemDiv = document.createElement('div');
            itemDiv.className = `risk-item fade-in-delay-${index % 3 + 1}`;
            
            // Determine severity level based on keywords
            let itemSeverity = severity;
            let itemIcon = icon.replace('fas fa-', '');
            
            if (typeof item === 'string') {
                // Handle simple string items
                if (item.toLowerCase().includes('required') || item.toLowerCase().includes('high risk')) {
                    itemSeverity = 'high';
                } else if (item.toLowerCase().includes('recommended') || item.toLowerCase().includes('moderate risk')) {
                    itemSeverity = 'medium';
                } else {
                    itemSeverity = 'low';
                }
                
                // Customize icon based on content
                if (item.toLowerCase().includes('yellow fever')) {
                    itemIcon = 'virus';
                } else if (item.toLowerCase().includes('hepatitis')) {
                    itemIcon = 'liver';
                } else if (item.toLowerCase().includes('typhoid')) {
                    itemIcon = 'utensils';
                } else if (item.toLowerCase().includes('rabies')) {
                    itemIcon = 'dog';
                } else if (item.toLowerCase().includes('malaria')) {
                    itemIcon = 'mosquito';
                } else if (item.toLowerCase().includes('japanese encephalitis')) {
                    itemIcon = 'brain';
                } else if (item.toLowerCase().includes('dtp') || item.toLowerCase().includes('tetanus')) {
                    itemIcon = 'syringe';
                } else if (item.toLowerCase().includes('influenza')) {
                    itemIcon = 'virus';
                } else if (item.toLowerCase().includes('covid')) {
                    itemIcon = 'virus-covid';
                } else if (item.toLowerCase().includes('polio')) {
                    itemIcon = 'child';
                } else if (item.toLowerCase().includes('measles') || item.toLowerCase().includes('mmr')) {
                    itemIcon = 'head-side-cough';
                }
                
                // Create item HTML with enhanced styling
                itemDiv.innerHTML = `
                    <div class="risk-icon severity-${itemSeverity}">
                        <i class="fas fa-${itemIcon}"></i>
                    </div>
                    <div class="risk-content">
                        <h4 class="risk-title">${item}</h4>
                    </div>
                    <div class="severity-badge severity-${itemSeverity}">
                        ${itemSeverity === 'high' ? 'REQUIRED' : itemSeverity === 'medium' ? 'RECOMMENDED' : 'OPTIONAL'}
                    </div>
                `;
            } else if (typeof item === 'object' && item.name) {
                // Handle object items with name/details
                if (item.name.toLowerCase().includes('required') || item.name.toLowerCase().includes('high risk')) {
                    itemSeverity = 'high';
                } else if (item.name.toLowerCase().includes('recommended') || item.name.toLowerCase().includes('moderate risk')) {
                    itemSeverity = 'medium';
                } else {
                    itemSeverity = 'low';
                }
                
                // Customize icon based on content
                if (item.name.toLowerCase().includes('yellow fever')) {
                    itemIcon = 'virus';
                } else if (item.name.toLowerCase().includes('hepatitis')) {
                    itemIcon = 'liver';
                } else if (item.name.toLowerCase().includes('typhoid')) {
                    itemIcon = 'utensils';
                } else if (item.name.toLowerCase().includes('rabies')) {
                    itemIcon = 'dog';
                } else if (item.name.toLowerCase().includes('malaria')) {
                    itemIcon = 'mosquito';
                } else if (item.name.toLowerCase().includes('japanese encephalitis')) {
                    itemIcon = 'brain';
                } else if (item.name.toLowerCase().includes('dtp') || item.name.toLowerCase().includes('tetanus')) {
                    itemIcon = 'syringe';
                } else if (item.name.toLowerCase().includes('influenza')) {
                    itemIcon = 'virus';
                } else if (item.name.toLowerCase().includes('covid')) {
                    itemIcon = 'virus-covid';
                } else if (item.name.toLowerCase().includes('polio')) {
                    itemIcon = 'child';
                } else if (item.name.toLowerCase().includes('measles') || item.name.toLowerCase().includes('mmr')) {
                    itemIcon = 'head-side-cough';
                }
                
                // Create item HTML with details and enhanced styling
                itemDiv.innerHTML = `
                    <div class="risk-icon severity-${itemSeverity}">
                        <i class="fas fa-${itemIcon}"></i>
                    </div>
                    <div class="risk-content">
                        <h4 class="risk-title">${item.name}</h4>
                        ${item.details ? `<div class="risk-details">${item.details}</div>` : ''}
                    </div>
                    <div class="severity-badge severity-${itemSeverity}">
                        ${itemSeverity === 'high' ? 'REQUIRED' : itemSeverity === 'medium' ? 'RECOMMENDED' : 'OPTIONAL'}
                    </div>
                `;
            }
            
            sectionDiv.appendChild(itemDiv);
        });
        
        container.appendChild(sectionDiv);
        return sectionDiv;
    }
    
    function displayError(message) {
        const loadingElement = document.getElementById('vaccination-loading');
        const contentElement = document.getElementById('vaccination-content');
        const inputElement = document.getElementById('vaccination-plan-input');
        
        // Safety check for required elements
        if (!loadingElement) {
            console.error('Loading element not found');
            return;
        }
        
        // Always hide loading spinner
        loadingElement.style.display = 'none';
        
        // Create error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger';
        errorDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Error:</strong> ${message}
        `;
        
        // Replace loading with error
        loadingElement.parentNode.insertBefore(errorDiv, loadingElement.nextSibling);
        
        // Show fallback content
        showFallbackContent();
        
        // Store fallback data in hidden input
        if (inputElement) {
            const fallbackData = {
                required_vaccinations: 'Standard travel vaccinations based on destination risk profile',
                recommended_vaccinations: 'Consult current guidelines for recommended vaccinations',
                malaria_prevention: 'Follow local malaria prevention guidelines if applicable',
                special_considerations: 'Consider patient-specific factors and medical history'
            };
            inputElement.value = JSON.stringify(fallbackData);
        }
    }
    
    function showFallbackContent() {
        const contentElement = document.getElementById('vaccination-content');
        if (!contentElement) {
            console.error('Content element not found');
            return;
        }
        
        // Show content element
        contentElement.style.display = 'block';
        
        // Create AI recommendation card container
        const aiRecommendationCard = document.createElement('div');
        aiRecommendationCard.className = 'ai-recommendation-card';
        
        // Add AI recommendation card header
        const cardHeader = document.createElement('div');
        cardHeader.className = 'ai-recommendation-header';
        cardHeader.innerHTML = `
            <div class="ai-recommendation-icon">
                <i class="fas fa-syringe"></i>
            </div>
            <h3 class="ai-recommendation-title">Vaccination Plan</h3>
            <div class="ai-badge">
                <i class="fas fa-info-circle"></i>
                Fallback Content
            </div>
        `;
        
        // Create card body
        const cardBody = document.createElement('div');
        cardBody.className = 'card-body p-4';
        
        // Add header and body to card
        aiRecommendationCard.appendChild(cardHeader);
        aiRecommendationCard.appendChild(cardBody);
        
        // Add card to content element
        contentElement.appendChild(aiRecommendationCard);
        
        // Add info note
        const infoNote = document.createElement('div');
        infoNote.className = 'alert alert-info mb-4';
        infoNote.innerHTML = `
            <div class="d-flex">
                <div class="me-3">
                    <i class="fas fa-info-circle fa-2x text-info"></i>
                </div>
                <div>
                    <h6 class="alert-heading">Important Note</h6>
                    <p class="mb-0">Individual recommendations may vary based on patient-specific factors, medical history, and current health status.</p>
                </div>
            </div>
        `;
        cardBody.appendChild(infoNote);
                    <p class="mb-0">Individual recommendations may vary based on patient-specific factors, medical history, and current health status.</p>
                </div>
            </div>
        `;
        cardBody.appendChild(infoNote);
            
            // Create fallback vaccination sections
            createVaccinationSection(
                'Required Vaccinations', 
                [
                    { name: 'Standard Travel Vaccines', details: 'Please check current guidelines for required vaccinations for this destination.' },
                    { name: 'Yellow Fever', details: 'May be required for entry to certain countries or when traveling from endemic areas.' }
                ],
                'high',
                'fas fa-shield-virus',
                cardBody
            );
            
            createVaccinationSection(
                'Recommended Vaccinations', 
                [
                    { name: 'Mosquito Avoidance', details: 'Use insect repellent, wear long sleeves and pants, and sleep under mosquito nets.' }
                ],
                'medium',
                'fas fa-mosquito',
                cardBody
            );
            
            createVaccinationSection(
                'Special Considerations', 
                [
                    { name: 'Patient-Specific Factors', details: 'Consider age, pregnancy status, chronic conditions, and medication interactions.' },
                    { name: 'Travel Duration', details: 'Longer stays may require additional preventive measures.' }
                ],
                'low',
                'fas fa-notes-medical',
                cardBody
            );
            
            // Add disclaimer
            const disclaimer = document.createElement('div');
            disclaimer.className = 'alert alert-info mt-4';
            disclaimer.innerHTML = '<i class="fas fa-info-circle me-2"></i> This is a fallback vaccination plan. For accurate recommendations, please consult with a travel health specialist.';
            contentElement.appendChild(disclaimer);
        }
    }
    
    // Function to update the vaccination summary section
    function updateVaccinationSummary() {
        const selectedCheckboxes = document.querySelectorAll('.vaccination-checkbox:checked');
        const summarySection = document.getElementById('vaccination-summary');
        const selectedList = document.getElementById('selected-vaccinations-list');
        const selectedCount = document.querySelector('.selected-count');
        const selectedVaccinationsInput = document.getElementById('selected-vaccinations-input');
        
        // Array to store selected vaccination data
        const selectedVaccinations = [];
        
        // Check if summary section exists
        if (!summarySection || !selectedList) {
            console.error('Vaccination summary elements not found');
            return;
        }
        
        if (selectedCheckboxes.length > 0) {
            summarySection.style.display = 'block';
            
            // Update count if element exists
            if (selectedCount) {
                selectedCount.textContent = `${selectedCheckboxes.length} vaccines`;
            }
            
            // Clear previous content
            selectedList.innerHTML = '';
            
            // Create a row for selected vaccinations
            const row = document.createElement('div');
            row.className = 'row';
            
            selectedCheckboxes.forEach((checkbox, index) => {
                try {
                    const vaccineData = JSON.parse(decodeURIComponent(checkbox.value));
                    selectedVaccinations.push(vaccineData);
                    
                    const isRequired = vaccineData.required;
                    const animationDelay = (index % 3) + 1;
                    
                    // Create column
                    const col = document.createElement('div');
                    col.className = 'col-md-6 mb-3';
                    
                    // Create vaccination item
                    const item = document.createElement('div');
                    item.className = `vaccination-item ${isRequired ? 'required' : 'recommended'} fade-in-delay-${animationDelay}`;
                    
                    // Create header
                    const header = document.createElement('div');
                    header.className = `vaccination-item-header ${isRequired ? 'required' : 'recommended'}`;
                    header.innerHTML = `
                        <span class="vaccination-item-title"><i class="fas fa-syringe me-2"></i> ${vaccineData.title}</span>
                        <span class="vaccination-item-badge ${isRequired ? 'required' : 'recommended'}">${isRequired ? 'Required' : 'Recommended'}</span>
                    `;
                    
                    // Create body
                    const body = document.createElement('div');
                    body.className = 'vaccination-item-body';
                    body.innerHTML = `
                        <p class="vaccination-item-description">${vaccineData.description || 'No additional details available.'}</p>
                    `;
                    
                    // Assemble the item
                    item.appendChild(header);
                    item.appendChild(body);
                    col.appendChild(item);
                    row.appendChild(col);
                    
                } catch (e) {
                    console.error('Error parsing vaccination data:', e);
                }
            });
            
            selectedList.appendChild(row);
        } else {
            summarySection.style.display = 'none';
        }
        
        // Store selected vaccinations in hidden input field if the element exists
        if (selectedVaccinationsInput) {
            selectedVaccinationsInput.value = JSON.stringify(selectedVaccinations);
        } else {
            console.warn('Selected vaccinations input not found');
        }
    }
    
    // Add click functionality to risk items
    document.addEventListener('click', function(e) {
        const riskItem = e.target.closest('.risk-item');
        if (riskItem) {
            // Toggle expanded class
            const isExpanded = riskItem.classList.contains('expanded');
            
            // Close all other expanded items
            document.querySelectorAll('.risk-item.expanded').forEach(item => {
                if (item !== riskItem) {
                    item.classList.remove('expanded');
                }
            });
            
            if (isExpanded) {
                riskItem.classList.remove('expanded');
            } else {
                riskItem.classList.add('expanded');
            }
        }
    });
</script>