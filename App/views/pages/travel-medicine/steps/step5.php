<?php
/**
 * Travel Protocol - Step 5: Documentation
 */
?>

<!-- CSS for protocol steps styling -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="<?= site_url('resources/css/protocol-steps.css') ?>">
<link rel="stylesheet" href="<?= site_url('resources/css/medical-ui-enhanced.css') ?>">

<div class="protocol-layout">
    <!-- Left sidebar with steps -->
    <div class="protocol-sidebar">
        <?php include __DIR__ . '/step-nav.php'; ?>
    </div>

    <!-- Main content area -->
    <div class="protocol-content">
        <div class="card shadow-sm">
            <div class="card-header bg-white">
                <h4 class="mb-0">Documentation</h4>
            </div>
            <div class="card-body">
                    <div class="alert alert-success mb-4">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-clipboard-check fa-2x text-success"></i>
                            </div>
                            <div>
                                <h5 class="alert-heading">Protocol Complete!</h5>
                                <p class="mb-0">Please review the travel protocol details and add any final notes before completing the documentation.</p>
                            </div>
                        </div>
                    </div>

                    <form action="<?= site_url('travel-medicine/process-step') ?>" method="post">
                        <?= csrf_field() ?>
                        <input type="hidden" name="step" value="5">

                        <!-- Protocol Summary Card -->
                        <div class="medical-card mb-4">
                            <div class="medical-card-header">
                                <div class="medical-card-icon" style="background: linear-gradient(135deg, #198754 0%, #157347 100%);">
                                    <i class="fas fa-file-medical"></i>
                                </div>
                                <h3 class="medical-card-title">Protocol Summary</h3>
                            </div>
                            <div class="medical-card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="summary-item mb-3">
                                            <div class="summary-label">
                                                <i class="fas fa-map-marker-alt text-info me-2"></i>
                                                <strong>Destination:</strong>
                                            </div>
                                            <div class="summary-value"><?= htmlspecialchars($protocol_data['destination'] ?? 'Not specified') ?></div>
                                        </div>
                                        <div class="summary-item mb-3">
                                            <div class="summary-label">
                                                <i class="fas fa-suitcase text-primary me-2"></i>
                                                <strong>Travel Type:</strong>
                                            </div>
                                            <div class="summary-value"><?= htmlspecialchars(ucfirst(str_replace('_', ' ', $protocol_data['travel_type'] ?? 'Not specified'))) ?></div>
                                        </div>
                                        <div class="summary-item mb-3">
                                            <div class="summary-label">
                                                <i class="fas fa-clock text-warning me-2"></i>
                                                <strong>Duration:</strong>
                                            </div>
                                            <div class="summary-value"><?= htmlspecialchars($protocol_data['duration'] ?? 'Not specified') ?></div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="summary-item mb-3">
                                            <div class="summary-label">
                                                <i class="fas fa-calendar-alt text-success me-2"></i>
                                                <strong>Departure Date:</strong>
                                            </div>
                                            <div class="summary-value">
                                                <?php if (!empty($protocol_data['departure_date'])): ?>
                                                    <?= date('F j, Y', strtotime($protocol_data['departure_date'])) ?>
                                                <?php else: ?>
                                                    Not specified
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="summary-item mb-3">
                                            <div class="summary-label">
                                                <i class="fas fa-user text-secondary me-2"></i>
                                                <strong>Patient:</strong>
                                            </div>
                                            <div class="summary-value">
                                                <?php if (isset($patient) && !empty($patient)): ?>
                                                    <?= htmlspecialchars($patient['first_name'] . ' ' . $patient['last_name']) ?>
                                                <?php else: ?>
                                                    Patient #<?= htmlspecialchars($protocol_data['patient_id'] ?? 'Not selected') ?>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="summary-item mb-3">
                                            <div class="summary-label">
                                                <i class="fas fa-calendar-plus text-info me-2"></i>
                                                <strong>Protocol Date:</strong>
                                            </div>
                                            <div class="summary-value"><?= date('F j, Y') ?></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Doctor Assignment Card -->
                        <div class="medical-card mb-4">
                            <div class="medical-card-header">
                                <div class="medical-card-icon" style="background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);">
                                    <i class="fas fa-user-md"></i>
                                </div>
                                <h3 class="medical-card-title">Medical Professional</h3>
                            </div>
                            <div class="medical-card-body">
                                <div class="mb-3">
                                    <label for="doctor_id" class="form-label medical-heading-3">Attending Doctor</label>
                                    <select class="form-select form-select-lg" id="doctor_id" name="doctor_id" required>
                                        <option value="">Select the attending doctor</option>
                                        <?php foreach ($doctors as $doctor): ?>
                                            <option value="<?= $doctor['id'] ?>" <?= isset($protocol_data['doctor_id']) && $protocol_data['doctor_id'] === $doctor['id'] ? 'selected' : '' ?>>
                                                Dr. <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <small class="form-text text-muted">Select the doctor responsible for this travel protocol</small>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Notes Card -->
                        <div class="medical-card mb-4">
                            <div class="medical-card-header">
                                <div class="medical-card-icon" style="background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);">
                                    <i class="fas fa-sticky-note"></i>
                                </div>
                                <h3 class="medical-card-title">Additional Notes</h3>
                            </div>
                            <div class="medical-card-body">
                                <div class="mb-3">
                                    <label for="notes" class="form-label medical-heading-3">Clinical Notes</label>
                                    <textarea class="form-control form-control-lg" id="notes" name="notes" rows="5" 
                                              placeholder="Add any additional clinical notes, special considerations, or follow-up instructions..."><?= $protocol_data['notes'] ?? '' ?></textarea>
                                    <small class="form-text text-muted">Include any special instructions, contraindications, or follow-up requirements</small>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <a href="<?= site_url('travel-medicine/create/4') ?>" class="btn btn-outline-secondary btn-lg">
                                <i class="fas fa-arrow-left me-2"></i>Previous
                            </a>
                            <div class="d-flex gap-3">
                                <button type="button" class="btn btn-outline-primary btn-lg" onclick="printProtocol()">
                                    <i class="fas fa-print me-2"></i>Print Protocol
                                </button>
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-check-circle me-2"></i>Complete Protocol
                                </button>
                            </div>
                        </div>
                    </form>

                    <script>
                        function printProtocol() {
                            window.print();
                        }
                        
                        // Add some styling for the summary items
                        document.addEventListener('DOMContentLoaded', function() {
                            const style = document.createElement('style');
                            style.textContent = `
                                .summary-item {
                                    padding: 0.75rem;
                                    background: rgba(0,0,0,0.02);
                                    border-radius: 8px;
                                    border-left: 4px solid #dee2e6;
                                }
                                .summary-label {
                                    font-weight: 600;
                                    color: #495057;
                                    margin-bottom: 0.25rem;
                                    display: flex;
                                    align-items: center;
                                }
                                .summary-value {
                                    color: #2c3e50;
                                    font-size: 1.1rem;
                                    margin-left: 1.5rem;
                                }
                                @media print {
                                    .btn, .protocol-sidebar {
                                        display: none !important;
                                    }
                                    .medical-card {
                                        break-inside: avoid;
                                        box-shadow: none !important;
                                        border: 1px solid #dee2e6 !important;
                                    }
                                }
                            `;
                            document.head.appendChild(style);
                        });
                    </script>
                </div>
            </div>
        </div>
    </div>
