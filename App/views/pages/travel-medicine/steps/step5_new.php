<?php
/**
 * Travel Protocol - Step 5: Documentation
 */
?>

<!-- CSS for protocol steps styling -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="<?= site_url('resources/css/protocol-steps.css') ?>">

<div class="protocol-layout">
    <!-- Left sidebar with steps -->
    <div class="protocol-sidebar">
        <?php include __DIR__ . '/step-nav.php'; ?>
    </div>
    
    <!-- Main content area -->
    <div class="protocol-content">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h4 class="mb-0">Documentation</h4>
                </div>
                <div class="card-body">
                    <form action="<?= site_url('travel-medicine/process-step') ?>" method="post">
                        <?= csrf_field() ?>
                        <input type="hidden" name="step" value="5">
                        <!-- Hidden doctor_id field required by the controller -->
                        <input type="hidden" name="doctor_id" value="<?= $_SESSION['user_id'] ?? '' ?>">
                        
                        <div class="mb-4">
                            <div class="alert alert-success">
                                <p><i class="fas fa-check-circle me-2"></i> Your travel protocol is almost complete! Please review and add any final notes.</p>
                            </div>
                            
                            <div class="card mb-4">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">Protocol Summary</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>Destination:</strong> <?= htmlspecialchars($protocol_data['destination'] ?? 'Not specified') ?></p>
                                            <p><strong>Travel Dates:</strong> <?= htmlspecialchars($protocol_data['departure_date'] ?? 'Not specified') ?> to <?= htmlspecialchars($protocol_data['return_date'] ?? 'Not specified') ?></p>
                                            <p><strong>Duration:</strong> <?= htmlspecialchars($protocol_data['duration'] ?? 'Not specified') ?> days</p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>Purpose:</strong> <?= htmlspecialchars($protocol_data['purpose'] ?? 'Not specified') ?></p>
                                            <p><strong>Accommodation:</strong> <?= htmlspecialchars($protocol_data['accommodation'] ?? 'Not specified') ?></p>
                                            <p><strong>Activities:</strong> <?= htmlspecialchars($protocol_data['activities'] ?? 'Not specified') ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="notes" class="form-label">Doctor's Notes</label>
                                <textarea class="form-control" id="notes" name="notes" rows="4" placeholder="Add any additional notes, recommendations, or follow-up instructions..."><?= htmlspecialchars($protocol_data['notes'] ?? '') ?></textarea>
                            </div>
                            
                            <div class="mb-4">
                                <label class="form-label">Documents to Provide to Patient</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="doc_vaccination_cert" name="documents[]" value="vaccination_certificate" checked>
                                    <label class="form-check-label" for="doc_vaccination_cert">
                                        Vaccination Certificate
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="doc_prescription" name="documents[]" value="prescription" checked>
                                    <label class="form-check-label" for="doc_prescription">
                                        Prescription for Medications
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="doc_travel_health" name="documents[]" value="travel_health_guide" checked>
                                    <label class="form-check-label" for="doc_travel_health">
                                        Travel Health Guide
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="doc_emergency" name="documents[]" value="emergency_contact" checked>
                                    <label class="form-check-label" for="doc_emergency">
                                        Emergency Contact Information
                                    </label>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="follow_up" class="form-label">Schedule Follow-up</label>
                                <div class="input-group">
                                    <input type="date" class="form-control" id="follow_up" name="follow_up" value="<?= htmlspecialchars($protocol_data['follow_up'] ?? '') ?>">
                                    <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                </div>
                                <div class="form-text">Optional: Schedule a follow-up appointment after the patient returns</div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <a href="<?= site_url('travel-medicine/create/4') ?>" class="btn btn-outline-secondary">Previous</a>
                            <button type="submit" class="btn btn-success">Complete Protocol</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
