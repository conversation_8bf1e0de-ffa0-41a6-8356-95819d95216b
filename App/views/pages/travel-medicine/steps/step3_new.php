<?php
/**
 * Travel Protocol - Step 3: Risk Analysis
 */
?>

<!-- CSS for protocol steps styling -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="<?= site_url('resources/css/protocol-steps.css') ?>">
<link rel="stylesheet" href="<?= site_url('resources/css/medical-ui-enhanced.css') ?>">
<link rel="stylesheet" href="<?= site_url('resources/css/risk-cards.css') ?>">
<link rel="stylesheet" href="<?= site_url('resources/css/vaccination-plan.css') ?>">
<style>
    /* Vaccination selection styling */
    .risk-item.vaccination-item {
        position: relative;
        padding-left: 3rem;
        transition: all 0.3s ease;
        border-radius: 12px;
        margin-bottom: 1.25rem;
        background: none;
        border: none;
        box-shadow: none;
    }
    
    .risk-item.vaccination-item:hover .vaccination-item-content-wrapper {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.12);
    }
    
    .risk-item.vaccination-item .form-check {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        z-index: 2;
    }
    
    .risk-item.vaccination-item .form-check-input {
        width: 1.2rem;
        height: 1.2rem;
        cursor: pointer;
    }
    
    .risk-item.vaccination-item .form-check-input:checked {
        background-color: #4caf50;
        border-color: #4caf50;
    }
    
    .risk-item.vaccination-item.required {
        border-left: 4px solid #f44336;
    }
    
    .risk-item.vaccination-item.recommended {
        border-left: 4px solid #ff9800;
    }
    
    .risk-item.vaccination-item .risk-title {
        font-weight: 600;
        color: #333;
        margin-bottom: 0.25rem;
    }
    
    .risk-item.vaccination-item .risk-description {
        font-size: 0.9rem;
        color: #666;
    }
    
    /* Vaccination item icon styling */
    .vaccination-item-icon {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        color: white;
        flex-shrink: 0;
    }
    
    .vaccination-item-icon.required {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        box-shadow: 0 2px 5px rgba(220, 53, 69, 0.3);
    }
    
    .vaccination-item-icon.recommended {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        box-shadow: 0 2px 5px rgba(255, 193, 7, 0.3);
    }
    
    .vaccination-item-header {
        display: flex;
        align-items: center;
        padding: 12px 15px 0;
        background: none;
    }
    
    .vaccination-item-content-wrapper {
        flex: 1;
        display: flex;
        flex-direction: column;
        background-color: #fff;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
    }
    
    .vaccination-item-content {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
    }
    
    .vaccination-item-title {
        font-weight: 600;
        margin: 0;
        font-size: 1rem;
    }
    
    .vaccination-badge {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 0.65rem;
        font-weight: 700;
        letter-spacing: 0.5px;
        margin-bottom: 5px;
        text-transform: uppercase;
    }
    
    .vaccination-badge.required {
        background-color: rgba(220, 53, 69, 0.15);
        color: #c82333;
    }
    
    .vaccination-badge.recommended {
        background-color: rgba(255, 193, 7, 0.15);
        color: #e0a800;
    }
    
    .vaccination-item-body {
        padding: 12px 15px 15px;
        background-color: #fff;
    }
    
    .risk-description {
        color: #555;
        font-size: 0.9rem;
        line-height: 1.5;
    }
    
    .risk-recommendations {
        margin-top: 8px;
        font-size: 0.85rem;
        color: #666;
        font-style: italic;
    }
    
    /* Vaccination summary section styling */
    .vaccination-summary-section {
        background-color: #f8f9fa;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 3px 10px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
    }
    
    .vaccination-summary-header {
        display: flex;
        align-items: center;
        margin-bottom: 1.25rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }
    
    .vaccination-summary-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        box-shadow: 0 2px 5px rgba(78, 115, 223, 0.3);
        flex-shrink: 0;
    }
    
    .vaccination-summary-title {
        font-weight: 600;
        margin: 0;
        flex-grow: 1;
    }
    
    .vaccination-count-badge {
        background-color: rgba(78, 115, 223, 0.15);
        color: #224abe;
        padding: 4px 10px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    /* Risk Assessment Card Enhancements */
    .medical-card {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 6px 16px rgba(0,0,0,0.12);
        transition: all 0.3s ease;
        margin-bottom: 1.5rem;
        background: linear-gradient(to right, #f9f9f9, #ffffff);
        transform: translateY(0);
    }
    
    .medical-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 24px rgba(0,0,0,0.15);
    }
    
    .medical-card-header {
        padding: 1.25rem;
        display: flex;
        align-items: center;
        background: linear-gradient(to right, #f8f9fa, #ffffff);
        border-bottom: 1px solid rgba(0,0,0,0.05);
        position: relative;
    }
    
    .medical-card-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        color: white;
        font-size: 1.25rem;
    }
    
    .medical-card-title {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: #333;
        flex-grow: 1;
    }
    
    .medical-card-body {
        padding: 1.5rem;
    }
    
    .ai-badge {
        background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
        color: white;
        padding: 0.35rem 0.75rem;
        border-radius: 2rem;
        font-size: 0.75rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(111, 66, 193, 0.4); }
        70% { box-shadow: 0 0 0 6px rgba(111, 66, 193, 0); }
        100% { box-shadow: 0 0 0 0 rgba(111, 66, 193, 0); }
    }
    
    .ai-section {
        margin-bottom: 1.5rem;
        animation: fadeIn 0.5s ease-in-out both;
    }
    
    .ai-section-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        display: flex;
        align-items: center;
    }
    
    .ai-content {
        line-height: 1.6;
        color: #555;
    }
    
    .ai-sources {
        background-color: rgba(0,0,0,0.02);
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
    }
    
    .sources-title {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
    }
    
    .sources-list {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .source-badge {
        background-color: #e9ecef;
        color: #495057;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
        transition: all 0.2s ease;
    }
    
    .source-badge:hover {
        background-color: #dee2e6;
        transform: translateY(-1px);
    }
    
    /* Severity badges */
    .severity-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 1rem;
        display: inline-block;
        margin-left: 0.5rem;
        font-weight: 600;
        transition: all 0.2s ease;
    }
    
    .severity-high {
        background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
        color: white;
    }
    
    .severity-medium {
        background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
        color: white;
    }
    
    .severity-low {
        background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
        color: white;
    }
    
    /* Animation classes */
    .fade-in-delay-1 {
        animation: fadeIn 0.5s ease-in-out 0.1s both;
    }
    
    .fade-in-delay-2 {
        animation: fadeIn 0.5s ease-in-out 0.2s both;
    }
    
    .fade-in-delay-3 {
        animation: fadeIn 0.5s ease-in-out 0.3s both;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    /* Risk loading spinner enhancements */
    #risk-loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 2rem;
        background: rgba(255,255,255,0.8);
        border-radius: 10px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    }
    
    .loading-spinner {
        width: 3rem;
        height: 3rem;
        border: 3px solid rgba(0,0,0,0.1);
        border-radius: 50%;
        border-top-color: #6f42c1;
        animation: spin 1s ease-in-out infinite;
        margin-bottom: 1rem;
    }
    
    @keyframes spin {
        to { transform: rotate(360deg); }
    }
</style>

<div class="protocol-layout">
    <!-- Left sidebar with steps -->
    <div class="protocol-sidebar">
        <?php include __DIR__ . '/step-nav.php'; ?>
    </div>
    
    <!-- Main content area -->
    <div class="protocol-content">
        <div class="card shadow-sm">
            <div class="card-header bg-white">
                <h4 class="mb-0">Risk Analysis</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-info mb-4">
                    <div class="d-flex">
                        <div class="me-3">
                            <i class="fas fa-info-circle fa-2x text-info"></i>
                        </div>
                        <div>
                            <h5 class="alert-heading">Destination: <?= htmlspecialchars($protocol_data['destination'] ?? 'Unknown') ?></h5>
                            <p class="mb-0">Based on your destination and patient information, we're analyzing potential health risks.</p>
                        </div>
                    </div>
                </div>
                
                <!-- Patient Information Card -->
                <div class="medical-card mb-4">
                    <div class="medical-card-header">
                        <div class="medical-card-icon" style="background: linear-gradient(135deg, #198754 0%, #157347 100%);">
                            <i class="fas fa-user"></i>
                        </div>
                        <h3 class="medical-card-title">Patient Information</h3>
                    </div>
                    <div class="medical-card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Name:</strong> <?= isset($patient) ? htmlspecialchars($patient['first_name'] . ' ' . $patient['last_name']) : 'Not selected' ?></p>
                                <p><strong>Age:</strong> <?= htmlspecialchars($protocol_data['age'] ?? 'Not specified') ?></p>
                                <p><strong>Gender:</strong> <?= htmlspecialchars(ucfirst($protocol_data['gender'] ?? 'Not specified')) ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Pregnancy Status:</strong> <?= htmlspecialchars(ucfirst(str_replace('_', ' ', $protocol_data['pregnancy_status'] ?? 'Not specified'))) ?></p>
                                <p><strong>Allergies:</strong> <?= htmlspecialchars($protocol_data['allergies'] ?? 'None reported') ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <form action="<?= site_url('travel-medicine/process-step') ?>" method="post">
                    <?= csrf_field() ?>
                    <input type="hidden" name="step" value="3">
                    
                    <!-- Loading spinner (shown initially) -->
                    <div id="risk-loading" class="my-5">
                        <div class="loading-spinner"></div>
                        <div class="loading-text">
                            <h5 class="mb-2">Analyzing Travel Health Risks</h5>
                            <p class="text-muted">Our AI is evaluating destination-specific health risks and recommended vaccinations...</p>
                        </div>
                    </div>
                    

                    
                    <!-- AI-Generated Risk Analysis -->
                    <?php if (isset($ai_recommendations)): ?>
                    <div id="risk-content" class="mt-4">
                        <!-- AI Risk Assessment -->
                        <div class="medical-card fade-in-delay-1">
                            <div class="medical-card-header">
                                <div class="medical-card-icon" style="background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);">
                                    <i class="fas fa-robot"></i>
                                </div>
                                <h3 class="medical-card-title">AI Risk Assessment</h3>
                                <div class="ai-badge">
                                    <i class="fas fa-microchip me-1"></i>
                                    <?= htmlspecialchars($ai_recommendations['model_used'] ?? 'AI Generated') ?>
                                </div>
                            </div>
                            <div class="medical-card-body">
                                <div class="ai-section fade-in-delay-1">
                                    <h4 class="ai-section-title">
                                        <i class="fas fa-chart-bar text-primary me-2"></i>
                                        Risk Assessment for <?= htmlspecialchars($protocol_data['destination'] ?? 'Destination') ?>
                                    </h4>
                                    <div class="ai-content">
                                        <?php if (!empty($ai_recommendations['risk_assessment']) && $ai_recommendations['risk_assessment'] !== 'Information not available in AI response'): ?>
                                            <?= nl2br(htmlspecialchars($ai_recommendations['risk_assessment'])) ?>
                                        <?php else: ?>
                                            <?= nl2br(htmlspecialchars($ai_recommendations['full_response'])) ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <?php if (!empty($ai_recommendations['malaria_prevention']) && $ai_recommendations['malaria_prevention'] !== 'Information not available in AI response'): ?>
                                <div class="ai-section fade-in-delay-2">
                                    <h4 class="ai-section-title">
                                        <i class="fas fa-bug text-danger me-2"></i>
                                        Malaria Prevention
                                    </h4>
                                    <div class="ai-content">
                                        <?= nl2br(htmlspecialchars($ai_recommendations['malaria_prevention'])) ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($ai_recommendations['disease_risks'])): ?>
                                <div class="ai-section fade-in-delay-1">
                                    <h4 class="ai-section-title">
                                        <i class="fas fa-virus text-danger me-2"></i>
                                        Disease Risks
                                    </h4>
                                    <div class="ai-content">
                                        <?= nl2br(htmlspecialchars($ai_recommendations['disease_risks'])) ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                                
                                <div class="ai-section fade-in-delay-2">
                                    <h4 class="ai-section-title">
                                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                        Travel Health Risks
                                    </h4>
                                    <div class="ai-content">
                                        <?= nl2br(htmlspecialchars($ai_recommendations['full_response'] ?? 'No risk assessment available')) ?>
                                    </div>
                                </div>
                                
                                <?php if (!empty($ai_recommendations['special_considerations']) && $ai_recommendations['special_considerations'] !== 'Information not available in AI response'): ?>
                                <div class="ai-section fade-in-delay-3">
                                    <h4 class="ai-section-title">
                                        <i class="fas fa-user-md text-success me-2"></i>
                                        Special Considerations
                                    </h4>
                                    <div class="ai-content">
                                        <?= nl2br(htmlspecialchars($ai_recommendations['special_considerations'])) ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($ai_recommendations['sources'])): ?>
                                <div class="ai-sources fade-in-delay-3">
                                    <div class="sources-title">
                                        <i class="fas fa-book-medical me-2"></i>
                                        Sources
                                    </div>
                                    <div class="sources-list">
                                        <?php foreach ($ai_recommendations['sources'] as $source): ?>
                                            <span class="source-badge"><?= htmlspecialchars($source) ?></span>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- Vaccination Section -->
                        <div class="risk-category-section vaccination-risk">
                            <div class="risk-section-header">
                                <div class="risk-section-icon">
                                    <i class="fas fa-syringe"></i>
                                </div>
                                <h3 class="risk-section-title">Vaccinations</h3>
                                <span class="risk-section-count"><?= count($ai_recommendations['vaccinations']) ?> vaccines</span>
                            </div>
                            <div class="p-3 bg-light">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Please select the vaccinations you want to include in the vaccination plan. The doctor will review your selections.
                                </div>
                            </div>
                            <?php foreach ($ai_recommendations['vaccinations'] as $index => $vaccine): ?>
                            <div class="risk-item vaccination-item">
                                <div class="form-check form-check-inline me-3">
                                    <input class="form-check-input vaccination-checkbox" type="checkbox" 
                                        id="vaccine_<?= $index ?>_<?= $vaccine['title'] ?>" name="selected_vaccinations[]" 
                                        value="<?= htmlspecialchars(json_encode($vaccine)) ?>">
                                    <label class="form-check-label" for="vaccine_<?= $index ?>_<?= $vaccine['title'] ?>"></label>
                                </div>
                                <div class="risk-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="risk-content">
                                    <div class="risk-title">
                                        <?= $vaccine['title'] ?>
                                        <span class="severity-badge severity-<?= $vaccine['severity'] ?>"><?= $vaccine['severity_text'] ?></span>
                                    </div>
                                    <div class="risk-description"><?= $vaccine['description'] ?></div>
                                    <div class="risk-recommendations"><?= $vaccine['recommendations'] ?></div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <!-- Store AI recommendations for next step -->
                        <input type="hidden" name="ai_recommendations" value="<?= htmlspecialchars(json_encode($ai_recommendations)) ?>">
                    <?php else: ?>
                    <div id="risk-content" class="mt-4 fade-in" style="display: none;">
                        <!-- Fallback static content if AI is not available -->
                                <div class="risk-content">
                                    <div class="risk-title">
                                        Typhoid
                                        <span class="severity-badge severity-moderate">Moderate</span>
                                    </div>
                                    <div class="risk-description">Moderate risk through contaminated food and water</div>
                                    <div class="risk-recommendations">Vaccination recommended, practice food and water safety</div>
                                </div>
                            </div>
                            <!-- <div class="risk-item">
                                <div class="risk-icon">
                                    <i class="fas fa-prescription-bottle"></i>
                                </div>
                                <div class="risk-content">
                                    <div class="risk-title">
                                        Hepatitis A
                                        <span class="severity-badge severity-moderate">Moderate</span>
                                    </div>
                                    <div class="risk-description">Present throughout the region</div>
                                    <div class="risk-recommendations">Vaccination strongly recommended for all travelers</div>
                                </div>
                            </div> -->
                        </div>

                        <!-- Environmental Risks -->
                        <!-- <div class="risk-category-section environmental-risk">
                            <div class="risk-section-header">
                                <div class="risk-section-icon">
                                    <i class="fas fa-cloud-sun"></i>
                                </div>
                                <h3 class="risk-section-title">Environmental Risks</h3>
                                <span class="risk-section-count">3 risks</span>
                            </div>
                            <div class="risk-item">
                                <div class="risk-icon">
                                    <i class="fas fa-thermometer-full"></i>
                                </div>
                                <div class="risk-content">
                                    <div class="risk-title">Altitude Sickness</div>
                                    <div class="risk-description">Some regions have high elevation, risk of altitude sickness</div>
                                    <div class="risk-recommendations">Gradual ascent, stay hydrated, consider medication</div>
                                </div>
                            </div>
                            <div class="risk-item">
                                <div class="risk-icon">
                                    <i class="fas fa-thermometer-full"></i>
                                </div>
                                <div class="risk-content">
                                    <div class="risk-title">Heat-related Illness</div>
                                    <div class="risk-description">Risk of heat exhaustion and dehydration</div>
                                    <div class="risk-recommendations">Stay hydrated, avoid midday sun, wear light clothing</div>
                                </div>
                            </div>
                            <div class="risk-item">
                                <div class="risk-icon">
                                    <i class="fas fa-tint"></i>
                                </div>
                                <div class="risk-content">
                                    <div class="risk-title">Water Safety</div>
                                    <div class="risk-description">Risk from contaminated water sources</div>
                                    <div class="risk-recommendations">Drink only bottled or boiled water, avoid ice</div>
                                </div>
                            </div>
                        </div> -->
                    </div>
                    <?php endif; ?>
                    
                    <!-- Vaccination Summary Section -->
                    <div id="vaccination-summary" class="vaccination-summary-section mt-4 mb-4" style="display: none;">
                        <div class="vaccination-summary-header">
                            <div class="vaccination-summary-icon">
                                <i class="fas fa-clipboard-check"></i>
                            </div>
                            <h4 class="vaccination-summary-title">Selected Vaccinations</h4>
                            <div class="vaccination-count-badge">
                                <span class="selected-count">0 vaccines</span>
                            </div>
                        </div>
                        <div class="vaccination-summary-body">
                            <div id="selected-vaccinations-list" class="mt-3">
                                <p class="text-muted">No vaccinations selected</p>
                            </div>
                        </div>
                        <input type="hidden" id="selected-vaccinations-input" name="selected_vaccinations" value="[]">
                    </div>
                    
                    <div class="d-flex justify-content-between mt-4">
                        <a href="<?= site_url('travel-medicine/create/2') ?>" class="btn btn-outline-secondary btn-lg">
                            <i class="fas fa-arrow-left me-2"></i>Previous
                        </a>
                        <button type="submit" class="btn btn-primary btn-lg" onclick="updateVaccinationSummary();">
                            Next<i class="fas fa-arrow-right ms-2"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    // Load AI recommendations asynchronously
    document.addEventListener('DOMContentLoaded', function() {
        // Start loading AI recommendations immediately
        loadAIRecommendations();
    });
    
    function parseAIResponse(recommendations) {
        const parsedData = {
            diseaseRisks: [],
            environmentalRisks: [],
            vaccinations: []
        };
        
        // Get the full response text
        const fullResponse = recommendations.full_response || '';
        
        // Parse disease risks from various sections
        const diseaseKeywords = ['hepatitis', 'typhoid', 'yellow fever', 'dengue', 'malaria', 'cholera', 'meningitis', 'japanese encephalitis', 'tick-borne encephalitis'];
        const environmentalKeywords = ['altitude', 'heat', 'water', 'sun', 'air pollution', 'insect', 'animal'];
        const vaccinationKeywords = ['vaccination', 'vaccine', 'immunization', 'shot'];
        
        // Extract sections from structured response if available
        if (recommendations.risk_assessment && recommendations.risk_assessment !== 'Information not available in AI response') {
            parsedData.diseaseRisks.push(...extractRisksFromText(recommendations.risk_assessment, 'disease'));
        }
        
        if (recommendations.recommended_vaccinations && recommendations.recommended_vaccinations !== 'Information not available in AI response') {
            parsedData.vaccinations.push(...extractVaccinationsFromText(recommendations.recommended_vaccinations));
        }
        
        if (recommendations.required_vaccinations && recommendations.required_vaccinations !== 'Information not available in AI response') {
            parsedData.vaccinations.push(...extractVaccinationsFromText(recommendations.required_vaccinations, true));
        }
        
        // Parse from full response if structured data not available
        if (parsedData.diseaseRisks.length === 0 && parsedData.vaccinations.length === 0) {
            const sections = splitIntoSections(fullResponse);
            
            sections.forEach(section => {
                const lowerSection = section.toLowerCase();
                
                // Check for disease risks
                diseaseKeywords.forEach(keyword => {
                    if (lowerSection.includes(keyword)) {
                        const risk = extractRiskFromSection(section, keyword);
                        if (risk && !parsedData.diseaseRisks.find(r => r.title === risk.title)) {
                            parsedData.diseaseRisks.push(risk);
                        }
                    }
                });
                
                // Check for environmental risks
                environmentalKeywords.forEach(keyword => {
                    if (lowerSection.includes(keyword)) {
                        const risk = extractEnvironmentalRisk(section, keyword);
                        if (risk && !parsedData.environmentalRisks.find(r => r.title === risk.title)) {
                            parsedData.environmentalRisks.push(risk);
                        }
                    }
                });
                
                // Check for vaccinations
                if (lowerSection.includes('vaccin') || lowerSection.includes('immuniz')) {
                    const vaccines = extractVaccinationsFromText(section);
                    vaccines.forEach(vaccine => {
                        if (!parsedData.vaccinations.find(v => v.title === vaccine.title)) {
                            parsedData.vaccinations.push(vaccine);
                        }
                    });
                }
            });
        }
        
        return parsedData;
    }
    
    function extractRiskFromSection(section, keyword) {
        const lines = section.split('\n').filter(line => line.toLowerCase().includes(keyword));
        if (lines.length === 0) return null;
        
        const line = lines[0].trim();
        const title = keyword.charAt(0).toUpperCase() + keyword.slice(1);
        
        return {
            title: title,
            description: line.replace(new RegExp(keyword, 'gi'), '').trim() || 'Risk present in this region',
            recommendations: 'Consult healthcare provider for prevention measures',
            severity: determineSeverity(line),
            severityText: getSeverityText(determineSeverity(line))
        };
    }
    
    function extractRisksFromText(text, type = 'disease') {
        const risks = [];
        const lines = text.split('\n').filter(line => line.trim());
        
        lines.forEach(line => {
            const cleanLine = line.trim();
            if (cleanLine.length > 10) {
                // Look for disease names or risk patterns
                const diseaseMatch = cleanLine.match(/([A-Z][a-z]+(?:\s+[A-Z])?[a-z]*)/);
                if (diseaseMatch) {
                    const title = diseaseMatch[1];
                    const description = cleanLine.replace(title, '').trim();
                    
                    risks.push({
                        title: title,
                        description: description || 'Risk present in this region',
                        recommendations: 'Consult healthcare provider for prevention measures',
                        severity: determineSeverity(cleanLine),
                        severityText: getSeverityText(determineSeverity(cleanLine))
                    });
                }
            }
        });
        
        return risks;
    }
    
    function extractEnvironmentalRisk(text, keyword) {
        const lines = text.split('\n').filter(line => line.toLowerCase().includes(keyword));
        if (lines.length === 0) return null;
        
        const line = lines[0].trim();
        let title = keyword.charAt(0).toUpperCase() + keyword.slice(1);
        let icon = 'exclamation-triangle';
        
        // Map keywords to proper titles and icons
        const mappings = {
            'altitude': { title: 'Altitude Sickness', icon: 'mountain' },
            'heat': { title: 'Heat-related Illness', icon: 'thermometer-full' },
            'water': { title: 'Water Safety', icon: 'tint' },
            'sun': { title: 'Sun Exposure', icon: 'sun' },
            'insect': { title: 'Insect-borne Diseases', icon: 'bug' },
            'animal': { title: 'Animal-related Risks', icon: 'paw' }
        };
        
        if (mappings[keyword]) {
            title = mappings[keyword].title;
            icon = mappings[keyword].icon;
        }
        
        return {
            title: title,
            description: line.replace(new RegExp(keyword, 'gi'), '').trim() || 'Environmental risk present',
            recommendations: 'Follow standard precautions and local guidance',
            icon: icon
        };
    }
    
    function extractVaccinationsFromText(text, required = false) {
        const vaccines = [];
        const commonVaccines = ['hepatitis a', 'hepatitis b', 'typhoid', 'yellow fever', 'meningitis', 'japanese encephalitis', 'rabies', 'mmr', 'dtp', 'polio', 'influenza'];
        
        const lowerText = text.toLowerCase();
        
        commonVaccines.forEach(vaccine => {
            if (lowerText.includes(vaccine)) {
                const vaccineTitle = vaccine.split(' ').map(word => 
                    word.charAt(0).toUpperCase() + word.slice(1)
                ).join(' ');
                
                // Extract context around the vaccine mention
                const sentences = text.split(/[.!?]+/);
                const relevantSentence = sentences.find(sentence => 
                    sentence.toLowerCase().includes(vaccine)
                );
                
                vaccines.push({
                    title: vaccineTitle,
                    description: relevantSentence ? relevantSentence.trim() : `${vaccineTitle} vaccination`,
                    recommendations: required ? 'Required for entry' : 'Recommended for travelers',
                    required: required
                });
            }
        });
        
        return vaccines;
    }
    
    function splitIntoSections(text) {
        // Split by numbered sections, bullet points, or double line breaks
        return text.split(/\n\s*\n|\d+\.\s|\*\s|-\s/).filter(section => section.trim().length > 20);
    }
    
    function determineSeverity(text) {
        const lowerText = text.toLowerCase();
        if (lowerText.includes('high') || lowerText.includes('severe') || lowerText.includes('critical')) {
            return 'high';
        } else if (lowerText.includes('moderate') || lowerText.includes('medium')) {
            return 'moderate';
        } else {
            return 'low';
        }
    }
    
    function getSeverityText(severity) {
        const severityMap = {
            'high': 'High',
            'moderate': 'Moderate',
            'low': 'Low'
        };
        return severityMap[severity] || 'Moderate';
    }
    
    function loadAIRecommendations() {
        fetch('<?= site_url('travel-medicine/generate-ai-recommendations') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.recommendations) {
                displayAIRecommendations(data.recommendations, data.fallback || false);
            } else {
                displayError('Failed to generate AI recommendations. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            displayError('Network error while generating recommendations. Please check your connection and try again.');
        });
    }
    
    function displayAIRecommendations(recommendations, isFallback) {
        // Hide loading spinner
        document.getElementById('risk-loading').style.display = 'none';
        
        // Create and show AI recommendations content
        const contentDiv = document.createElement('div');
        contentDiv.id = 'risk-content';
        contentDiv.className = 'mt-4 fade-in';
        
        let contentHTML = '';
        
        // Parse AI response into structured cards
        const parsedData = parseAIResponse(recommendations);
        
        // Disease Risks Section
        if (parsedData.diseaseRisks && parsedData.diseaseRisks.length > 0) {
            contentHTML += `
                <div class="risk-category-section disease-risk">
                    <div class="risk-section-header">
                        <div class="risk-section-icon">
                            <i class="fas fa-virus"></i>
                        </div>
                        <h3 class="risk-section-title">Disease Risks</h3>
                        <span class="risk-section-count">${parsedData.diseaseRisks.length} risks</span>
                    </div>
            `;
            
            parsedData.diseaseRisks.forEach(risk => {
                // Assign different icons based on disease type
                let icon = 'prescription-bottle';
                if (risk.title.toLowerCase().includes('malaria') || risk.title.toLowerCase().includes('dengue')) {
                    icon = 'mosquito';
                } else if (risk.title.toLowerCase().includes('hepatitis')) {
                    icon = 'virus';
                } else if (risk.title.toLowerCase().includes('typhoid')) {
                    icon = 'bacteria';
                } else if (risk.title.toLowerCase().includes('yellow fever')) {
                    icon = 'disease';
                }
                
                contentHTML += `
                    <div class="risk-item">
                        <div class="risk-icon">
                            <i class="fas fa-${icon}"></i>
                        </div>
                        <div class="risk-content">
                            <div class="risk-title">
                                ${risk.title}
                                <span class="severity-badge severity-${risk.severity}">${risk.severityText}</span>
                            </div>
                            <div class="risk-description">${risk.description}</div>
                            <div class="risk-recommendations">${risk.recommendations}</div>
                        </div>
                    </div>
                `;
            });
            
            contentHTML += '</div>';
        }
        
        // Environmental Risks Section
        if (parsedData.environmentalRisks && parsedData.environmentalRisks.length > 0) {
            contentHTML += `
                <div class="risk-category-section environmental-risk">
                    <div class="risk-section-header">
                        <div class="risk-section-icon">
                            <i class="fas fa-cloud-sun"></i>
                        </div>
                        <h3 class="risk-section-title">Environmental Risks</h3>
                        <span class="risk-section-count">${parsedData.environmentalRisks.length} risks</span>
                    </div>
            `;
            
            parsedData.environmentalRisks.forEach(risk => {
                contentHTML += `
                    <div class="risk-item">
                        <div class="risk-icon">
                            <i class="fas fa-${risk.icon || 'exclamation-triangle'}"></i>
                        </div>
                        <div class="risk-content">
                            <div class="risk-title">${risk.title}</div>
                            <div class="risk-description">${risk.description}</div>
                            <div class="risk-recommendations">${risk.recommendations}</div>
                        </div>
                    </div>
                `;
            });
            
            contentHTML += '</div>';
        }
        
        // Vaccinations Section
        if (parsedData.vaccinations && parsedData.vaccinations.length > 0) {
            contentHTML += `
                <div class="risk-category-section vaccination-risk">
                    <div class="risk-section-header">
                        <div class="risk-section-icon">
                            <i class="fas fa-syringe"></i>
                        </div>
                        <h3 class="risk-section-title">Vaccinations</h3>
                        <span class="risk-section-count">${parsedData.vaccinations.length} vaccines</span>
                    </div>
                    <div class="p-3 bg-light">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Please select the vaccinations you want to include in the vaccination plan. The doctor will review your selections.
                        </div>
                    </div>
            `;
            
            parsedData.vaccinations.forEach((vaccine, index) => {
                const vaccineId = `vaccine_${index}_${vaccine.title.replace(/\s+/g, '_').toLowerCase()}`;
                const isRequired = vaccine.required === true;
                
                const animationDelay = (index % 3) + 1;
                contentHTML += `
                    <div class="risk-item vaccination-item ${isRequired ? 'required' : 'recommended'} fade-in-delay-${animationDelay}">
                        <div class="form-check form-check-inline me-3">
                            <input class="form-check-input vaccination-checkbox" type="checkbox" 
                                id="${vaccineId}" name="selected_vaccinations[]" 
                                value="${encodeURIComponent(JSON.stringify(vaccine))}" 
                                ${isRequired ? 'checked disabled' : ''}>
                            <label class="form-check-label" for="${vaccineId}"></label>
                        </div>
                        <div class="vaccination-item-content-wrapper">
                            <div class="vaccination-item-header">
                                <div class="vaccination-item-icon ${isRequired ? 'required' : 'recommended'}">
                                    <i class="fas fa-${isRequired ? 'syringe' : 'shield-virus'}"></i>
                                </div>
                                <div class="vaccination-item-content">
                                    <div class="vaccination-badge ${isRequired ? 'required' : 'recommended'}">
                                        ${isRequired ? 'REQUIRED' : 'RECOMMENDED'}
                                    </div>
                                    <h5 class="vaccination-item-title">${vaccine.title}</h5>
                                </div>
                            </div>
                            <div class="vaccination-item-body">
                                <div class="risk-description">${vaccine.description || 'No description available.'}</div>
                                <div class="risk-recommendations">${vaccine.recommendations || ''}</div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            contentHTML += '</div>';
        }
        
        // If no structured data found, show fallback
        if (!contentHTML) {
            contentHTML = `
                <div class="medical-card">
                    <div class="medical-card-header">
                        <div class="medical-card-icon" style="background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);">
                            <i class="fas fa-robot"></i>
                        </div>
                        <h3 class="medical-card-title">AI Risk Assessment</h3>
                        <div class="ai-badge">
                            <i class="fas fa-microchip me-1"></i>
                            ${recommendations.model_used || 'AI Generated'}
                        </div>
                    </div>
                    <div class="medical-card-body">
                        <div class="ai-section">
                            <h4 class="ai-section-title">
                                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                Travel Health Recommendations
                            </h4>
                            <div class="ai-content">
                                ${recommendations.full_response ? recommendations.full_response.replace(/\n/g, '<br>') : 'No recommendations available'}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        // Add sources if available
        if (recommendations.sources && recommendations.sources.length > 0) {
            contentHTML += `
                <div class="ai-sources mt-3">
                    <h5 class="sources-title">
                        <i class="fas fa-book me-2"></i>
                        Guidelines Referenced
                    </h5>
                    <div class="sources-list">
                        ${recommendations.sources.map(source => `<span class="source-badge">${source}</span>`).join('')}
                    </div>
                </div>
            `;
        }
        
        contentHTML += `<input type="hidden" name="ai_recommendations" value="${encodeURIComponent(JSON.stringify(recommendations))}">`;
        
        contentDiv.innerHTML = contentHTML;
        
        // Insert the content after the loading div
        const loadingDiv = document.getElementById('risk-loading');
        loadingDiv.parentNode.insertBefore(contentDiv, loadingDiv.nextSibling);
        
        // Initialize interactions
        initializeRiskInteractions();
        
        // Animate appearance
        setTimeout(() => {
            contentDiv.style.opacity = '1';
            contentDiv.style.transform = 'translateY(0)';
        }, 100);
    }
    
    function displayError(message) {
        document.getElementById('risk-loading').style.display = 'none';
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger mt-4';
        errorDiv.innerHTML = `
            <i class="fas fa-exclamation-circle me-2"></i>
            ${message}
            <button type="button" class="btn btn-outline-danger btn-sm ms-3" onclick="loadAIRecommendations()">
                <i class="fas fa-redo me-1"></i>Retry
            </button>
        `;
        
        const loadingDiv = document.getElementById('risk-loading');
        loadingDiv.parentNode.insertBefore(errorDiv, loadingDiv.nextSibling);
    }
    
    function initializeRiskInteractions() {
        // Add click-to-expand functionality for risk items
        const riskItems = document.querySelectorAll('.risk-item');
        riskItems.forEach(item => {
            const content = item.querySelector('.risk-content');
            const recommendations = item.querySelector('.risk-recommendations');
            const checkbox = item.querySelector('.vaccination-checkbox');
            
            if (recommendations) {
                // Initially hide recommendations
                recommendations.style.maxHeight = '0';
                recommendations.style.overflow = 'hidden';
                recommendations.style.transition = 'max-height 0.3s ease';
                
                // Add click handler to expand/collapse
                item.addEventListener('click', function(e) {
                    // Don't toggle if clicking on the checkbox
                    if (e.target === checkbox || checkbox && checkbox.contains(e.target)) {
                        return;
                    }
                    
                    const isExpanded = recommendations.style.maxHeight !== '0px';
                    
                    if (isExpanded) {
                        recommendations.style.maxHeight = '0';
                        item.classList.remove('expanded');
                    } else {
                        recommendations.style.maxHeight = recommendations.scrollHeight + 'px';
                        item.classList.add('expanded');
                    }
                });
                
                // Add visual indicator for clickable items
                item.style.cursor = 'pointer';
                item.title = 'Click to view recommendations';
            }
        });
        
        // Initialize vaccination checkboxes
        const vaccinationCheckboxes = document.querySelectorAll('.vaccination-checkbox');
        vaccinationCheckboxes.forEach(checkbox => {
            // Prevent click events from bubbling up to the parent
            checkbox.addEventListener('click', function(e) {
                e.stopPropagation();
                updateVaccinationSummary();
            });
        });
        
        // Initial update of vaccination summary
        updateVaccinationSummary();
        
        // Add hover effects for severity badges
        const severityBadges = document.querySelectorAll('.severity-badge');
        severityBadges.forEach(badge => {
            badge.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.1)';
                this.style.transition = 'transform 0.2s ease';
            });
            
            badge.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });
    }
    // Function to update the vaccination summary section
    function updateVaccinationSummary() {
        const selectedCheckboxes = document.querySelectorAll('.vaccination-checkbox:checked');
        const summarySection = document.getElementById('vaccination-summary');
        const selectedList = document.getElementById('selected-vaccinations-list');
        const selectedCount = document.querySelector('.selected-count');
        const selectedVaccinationsInput = document.getElementById('selected-vaccinations-input');
        
        // Array to store selected vaccination data
        const selectedVaccinations = [];
        
        // Check if summary section exists
        if (!summarySection || !selectedList) {
            console.error('Vaccination summary elements not found');
            return;
        }
        
        if (selectedCheckboxes.length > 0) {
            summarySection.style.display = 'block';
            
            // Update count if element exists
            if (selectedCount) {
                selectedCount.textContent = `${selectedCheckboxes.length} vaccines`;
            }
            
            // Clear previous content
            selectedList.innerHTML = '';
            
            // Create a row for selected vaccinations
            const row = document.createElement('div');
            row.className = 'row';
            
            selectedCheckboxes.forEach((checkbox, index) => {
                try {
                    const vaccineData = JSON.parse(decodeURIComponent(checkbox.value));
                    selectedVaccinations.push(vaccineData);
                    
                    const isRequired = vaccineData.required;
                    const animationDelay = (index % 3) + 1;
                    
                    // Create column
                    const col = document.createElement('div');
                    col.className = 'col-md-6 mb-3';
                    
                    // Create vaccination item
                    const item = document.createElement('div');
                    item.className = `vaccination-item ${isRequired ? 'required' : 'recommended'} fade-in-delay-${animationDelay}`;
                    
                    // Create content wrapper
                    const contentWrapper = document.createElement('div');
                    contentWrapper.className = 'vaccination-item-content-wrapper';
                    
                    // Create header
                    const header = document.createElement('div');
                    header.className = 'vaccination-item-header';
                    
                    // Create icon
                    const icon = document.createElement('div');
                    icon.className = `vaccination-item-icon ${isRequired ? 'required' : 'recommended'}`;
                    icon.innerHTML = `<i class="fas fa-${isRequired ? 'syringe' : 'shield-virus'}"></i>`;
                    
                    // Create content container
                    const contentContainer = document.createElement('div');
                    contentContainer.className = 'vaccination-item-content';
                    
                    // Create badge
                    const badge = document.createElement('div');
                    badge.className = `vaccination-badge ${isRequired ? 'required' : 'recommended'}`;
                    badge.textContent = isRequired ? 'REQUIRED' : 'RECOMMENDED';
                    
                    // Create title
                    const title = document.createElement('h5');
                    title.className = 'vaccination-item-title';
                    title.textContent = vaccineData.title || 'Vaccination';
                    
                    // Assemble content container
                    contentContainer.appendChild(badge);
                    contentContainer.appendChild(title);
                    
                    // Assemble header
                    header.appendChild(icon);
                    header.appendChild(contentContainer);
                    
                    // Create body
                    const body = document.createElement('div');
                    body.className = 'vaccination-item-body';
                    body.innerHTML = vaccineData.description || 'No additional information available.';
                    
                    // Assemble content wrapper
                    contentWrapper.appendChild(header);
                    contentWrapper.appendChild(body);
                    
                    // Assemble item
                    item.appendChild(contentWrapper);
                    
                    // Add to column and row
                    col.appendChild(item);
                    row.appendChild(col);
                } catch (e) {
                    console.error('Error parsing vaccination data:', e);
                }
            });
            
            selectedList.appendChild(row);
        } else {
            summarySection.style.display = 'none';
        }
        
        // Store selected vaccinations in hidden input field if the element exists
        if (selectedVaccinationsInput) {
            selectedVaccinationsInput.value = JSON.stringify(selectedVaccinations);
        } else {
            console.warn('Selected vaccinations input element not found');
        }
    }
</script>