<?php
/**
 * Travel Protocol - Step 4: Vaccination Plan
 */
?>

<!-- CSS for protocol steps styling -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="<?= site_url('resources/css/protocol-steps.css') ?>">
<link rel="stylesheet" href="<?= site_url('resources/css/medical-ui-enhanced.css') ?>">

<div class="protocol-layout">
    <!-- Left sidebar with steps -->
    <div class="protocol-sidebar">
        <?php include __DIR__ . '/step-nav.php'; ?>
    </div>

    <!-- Main content area -->
    <div class="protocol-content">
        <div class="card shadow-sm">
            <div class="card-header bg-white">
                <h4 class="mb-0">Vaccination Plan</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-info mb-4">
                    <div class="d-flex">
                        <div class="me-3">
                            <i class="fas fa-syringe fa-2x text-info"></i>
                        </div>
                        <div>
                            <h5 class="alert-heading">Destination: <?= htmlspecialchars($protocol_data['destination'] ?? 'Unknown') ?></h5>
                            <p class="mb-0">Based on the risk assessment and patient profile, we've generated a personalized vaccination plan.</p>
                        </div>
                    </div>
                </div>

                <form action="<?= site_url('travel-medicine/process-step') ?>" method="post">
                    <?= csrf_field() ?>
                    <input type="hidden" name="step" value="4">

                    <div class="mb-4">
                        <h5>Recommended Vaccinations</h5>
                        <div class="card">
                            <div class="card-body">
                                <?php if (isset($vaccination_plan) && !empty($vaccination_plan)): ?>
                                    <div class="vaccination-plan-content">
                                        <!-- Enhanced Vaccination Plan Display -->
                                        <!-- Required Vaccinations Section -->
                                        <div class="vaccination-category-section vaccination-required">
                                            <div class="vaccination-section-header">
                                                <div class="vaccination-section-icon">
                                                    <i class="fas fa-exclamation-circle"></i>
                                                </div>
                                                <h3 class="vaccination-section-title">Required Vaccinations</h3>
                                                <span class="vaccination-section-count">3 vaccines</span>
                                            </div>
                                            
                                            <div class="vaccination-item">
                                                <div class="vaccination-icon">
                                                    <i class="fas fa-syringe"></i>
                                                </div>
                                                <div class="vaccination-content">
                                                    <div class="vaccination-title">Hepatitis A</div>
                                                    <div class="vaccination-description">Strongly recommended for all travelers to prevent liver infection</div>
                                                    <div class="vaccination-timing">
                                                        <i class="fas fa-clock timing-icon"></i>
                                                        <span class="timing-text">At least 2 weeks before travel</span>
                                                    </div>
                                                </div>
                                                <div class="priority-badge priority-required">
                                                    <i class="fas fa-star"></i>
                                                    Required
                                                </div>
                                            </div>

                                            <div class="vaccination-item">
                                                <div class="vaccination-icon">
                                                    <i class="fas fa-shield-virus"></i>
                                                </div>
                                                <div class="vaccination-content">
                                                    <div class="vaccination-title">DTP booster</div>
                                                    <div class="vaccination-description">Required according to CDC guidelines</div>
                                                    <div class="vaccination-timing">
                                                        <i class="fas fa-clock timing-icon"></i>
                                                        <span class="timing-text">If >10 years since last dose</span>
                                                    </div>
                                                </div>
                                                <div class="priority-badge priority-required">
                                                    <i class="fas fa-star"></i>
                                                    Required
                                                </div>
                                            </div>

                                            <div class="vaccination-item">
                                                <div class="vaccination-icon">
                                                    <i class="fas fa-certificate"></i>
                                                </div>
                                                <div class="vaccination-content">
                                                    <div class="vaccination-title">Destination-specific requirements</div>
                                                    <div class="vaccination-description">Required according to CDC guidelines</div>
                                                    <div class="vaccination-timing">
                                                        <i class="fas fa-clock timing-icon"></i>
                                                        <span class="timing-text">Required according to CDC guidelines</span>
                                                    </div>
                                                </div>
                                                <div class="priority-badge priority-required">
                                                    <i class="fas fa-star"></i>
                                                    Required
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Recommended Vaccinations Section -->
                                        <div class="vaccination-category-section vaccination-recommended">
                                            <div class="vaccination-section-header">
                                                <div class="vaccination-section-icon">
                                                    <i class="fas fa-thumbs-up"></i>
                                                </div>
                                                <h3 class="vaccination-section-title">Recommended Vaccinations</h3>
                                                <span class="vaccination-section-count">3 vaccines</span>
                                            </div>

                                            <div class="vaccination-item">
                                                <div class="vaccination-icon">
                                                    <i class="fas fa-utensils"></i>
                                                </div>
                                                <div class="vaccination-content">
                                                    <div class="vaccination-title">Typhoid</div>
                                                    <div class="vaccination-description">Consider based on specific travel circumstances</div>
                                                    <div class="vaccination-timing">
                                                        <i class="fas fa-clock timing-icon"></i>
                                                        <span class="timing-text">Consider based on specific travel circumstances</span>
                                                    </div>
                                                </div>
                                                <div class="priority-badge priority-recommended">
                                                    <i class="fas fa-check"></i>
                                                    Recommended
                                                </div>
                                            </div>

                                            <div class="vaccination-item">
                                                <div class="vaccination-icon">
                                                    <i class="fas fa-dog"></i>
                                                </div>
                                                <div class="vaccination-content">
                                                    <div class="vaccination-title">Rabies</div>
                                                    <div class="vaccination-description">Consider based on specific travel circumstances</div>
                                                    <div class="vaccination-timing">
                                                        <i class="fas fa-clock timing-icon"></i>
                                                        <span class="timing-text">Consider based on specific travel circumstances</span>
                                                    </div>
                                                </div>
                                                <div class="priority-badge priority-recommended">
                                                    <i class="fas fa-check"></i>
                                                    Recommended
                                                </div>
                                            </div>

                                            <div class="vaccination-item">
                                                <div class="vaccination-icon">
                                                    <i class="fas fa-brain"></i>
                                                </div>
                                                <div class="vaccination-content">
                                                    <div class="vaccination-title">Japanese Encephalitis</div>
                                                    <div class="vaccination-description">Consider based on specific travel circumstances</div>
                                                    <div class="vaccination-timing">
                                                        <i class="fas fa-clock timing-icon"></i>
                                                        <span class="timing-text">Consider based on specific travel circumstances</span>
                                                    </div>
                                                </div>
                                                <div class="priority-badge priority-recommended">
                                                    <i class="fas fa-check"></i>
                                                    Recommended
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <input type="hidden" name="vaccination_plan" value="<?= htmlspecialchars($vaccination_plan) ?>">
                                    <script>
                                        // Initialize interactions for pre-loaded content
                                        document.addEventListener('DOMContentLoaded', function() {
                                            initializeVaccinationInteractions();
                                        });
                                        
                                        function initializeVaccinationInteractions() {
                                            // Add click-to-expand functionality for vaccination details
                                            const vaccinationItems = document.querySelectorAll('.vaccination-item');
                                            vaccinationItems.forEach(item => {
                                                const content = item.querySelector('.vaccination-content');
                                                
                                                // Create expandable details section
                                                const detailsDiv = document.createElement('div');
                                                detailsDiv.className = 'vaccination-details';
                                                detailsDiv.innerHTML = `
                                                    <div class="vaccination-details-content">
                                                        <div class="detail-row">
                                                            <span class="detail-label">Side Effects:</span>
                                                            <span class="detail-value">Mild soreness, low-grade fever</span>
                                                        </div>
                                                        <div class="detail-row">
                                                            <span class="detail-label">Contraindications:</span>
                                                            <span class="detail-value">Severe illness, pregnancy (consult doctor)</span>
                                                        </div>
                                                        <div class="detail-row">
                                                            <span class="detail-label">Cost Range:</span>
                                                            <span class="detail-value">$50 - $150 per dose</span>
                                                        </div>
                                                    </div>
                                                `;
                                                
                                                content.appendChild(detailsDiv);
                                                
                                                // Add click handler to expand/collapse
                                                item.addEventListener('click', function(e) {
                                                    e.preventDefault();
                                                    const isExpanded = detailsDiv.classList.contains('expanded');
                                                    
                                                    // Close all other expanded items
                                                    document.querySelectorAll('.vaccination-details.expanded').forEach(detail => {
                                                        if (detail !== detailsDiv) {
                                                            detail.classList.remove('expanded');
                                                        }
                                                    });
                                                    
                                                    if (isExpanded) {
                                                        detailsDiv.classList.remove('expanded');
                                                        item.classList.remove('expanded');
                                                    } else {
                                                        detailsDiv.classList.add('expanded');
                                                        item.classList.add('expanded');
                                                    }
                                                });
                                                
                                                // Add visual indicator for clickable items
                                                item.style.cursor = 'pointer';
                                                item.title = 'Click to view detailed information';
                                                
                                                // Add expand indicator
                                                const expandIcon = document.createElement('div');
                                                expandIcon.innerHTML = '<i class="fas fa-chevron-down"></i>';
                                                expandIcon.className = 'expand-indicator';
                                                expandIcon.style.cssText = `
                                                    position: absolute;
                                                    bottom: 1rem;
                                                    right: 1rem;
                                                    color: #6c757d;
                                                    font-size: 0.875rem;
                                                    transition: transform 0.3s ease;
                                                `;
                                                item.style.position = 'relative';
                                                item.appendChild(expandIcon);
                                                
                                                // Update expand indicator on click
                                                item.addEventListener('click', function() {
                                                    const isExpanded = detailsDiv.classList.contains('expanded');
                                                    expandIcon.style.transform = isExpanded ? 'rotate(180deg)' : 'rotate(0deg)';
                                                });
                                            });
                                            
                                            // Add hover effects for priority badges
                                            const priorityBadges = document.querySelectorAll('.priority-badge');
                                            priorityBadges.forEach(badge => {
                                                badge.addEventListener('mouseenter', function() {
                                                    this.style.transform = 'scale(1.05)';
                                                    this.style.transition = 'transform 0.2s ease';
                                                });
                                                
                                                badge.addEventListener('mouseleave', function() {
                                                    this.style.transform = 'scale(1)';
                                                });
                                            });
                                        }
                                    </script>
                                <?php else: ?>
                                    <div class="d-flex justify-content-center align-items-center p-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <span class="ms-3">Generating vaccination plan...</span>
                                    </div>
                                    <div class="vaccination-plan-content fade-in" style="display: none;">
                                        <!-- Required Vaccinations Section -->
                                        <div class="vaccination-category-section vaccination-required">
                                            <div class="vaccination-section-header">
                                                <div class="vaccination-section-icon">
                                                    <i class="fas fa-exclamation-circle"></i>
                                                </div>
                                                <h3 class="vaccination-section-title">Required Vaccinations</h3>
                                                <span class="vaccination-section-count">3 vaccines</span>
                                            </div>
                                            
                                            <div class="vaccination-item">
                                                <div class="vaccination-icon">
                                                    <i class="fas fa-syringe"></i>
                                                </div>
                                                <div class="vaccination-content">
                                                    <div class="vaccination-title">
                                                        Hepatitis A
                                                    </div>
                                                    <div class="vaccination-description">Strongly recommended for all travelers to prevent liver infection from contaminated food and water</div>
                                                    <div class="vaccination-timing">
                                                        <i class="fas fa-clock timing-icon"></i>
                                                        <span class="timing-text">At least 2 weeks before travel</span>
                                                    </div>
                                                    <div class="vaccination-timeline">
                                                        <div class="timeline-step">
                                                            <div class="timeline-dot active">1</div>
                                                            <div class="timeline-label">Initial Dose</div>
                                                        </div>
                                                        <div class="timeline-step">
                                                            <div class="timeline-dot">2</div>
                                                            <div class="timeline-label">6-12 months</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="priority-badge priority-required">
                                                    <i class="fas fa-star"></i>
                                                    Required
                                                </div>
                                            </div>

                                            <div class="vaccination-item">
                                                <div class="vaccination-icon">
                                                    <i class="fas fa-shield-virus"></i>
                                                </div>
                                                <div class="vaccination-content">
                                                    <div class="vaccination-title">
                                                        Typhoid
                                                    </div>
                                                    <div class="vaccination-description">Recommended for most travelers to prevent bacterial infection from contaminated food and water</div>
                                                    <div class="vaccination-timing">
                                                        <i class="fas fa-clock timing-icon"></i>
                                                        <span class="timing-text">At least 2 weeks before travel</span>
                                                    </div>
                                                    <div class="vaccination-timeline">
                                                        <div class="timeline-step">
                                                            <div class="timeline-dot active">1</div>
                                                            <div class="timeline-label">Single Dose</div>
                                                        </div>
                                                        <div class="timeline-step">
                                                            <div class="timeline-dot">2</div>
                                                            <div class="timeline-label">Booster 2-3 years</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="priority-badge priority-required">
                                                    <i class="fas fa-star"></i>
                                                    Required
                                                </div>
                                            </div>

                                            <div class="vaccination-item">
                                                <div class="vaccination-icon">
                                                    <i class="fas fa-certificate"></i>
                                                </div>
                                                <div class="vaccination-content">
                                                    <div class="vaccination-title">
                                                        Yellow Fever
                                                    </div>
                                                    <div class="vaccination-description">Required for entry to some countries and provides lifelong protection against yellow fever virus</div>
                                                    <div class="vaccination-timing">
                                                        <i class="fas fa-clock timing-icon"></i>
                                                        <span class="timing-text">At least 10 days before travel</span>
                                                    </div>
                                                    <div class="vaccination-timeline">
                                                        <div class="timeline-step">
                                                            <div class="timeline-dot active">1</div>
                                                            <div class="timeline-label">Single Dose</div>
                                                        </div>
                                                        <div class="timeline-step">
                                                            <div class="timeline-dot completed">✓</div>
                                                            <div class="timeline-label">Lifelong Protection</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="priority-badge priority-required">
                                                    <i class="fas fa-star"></i>
                                                    Required
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Recommended Vaccinations Section -->
                                        <div class="vaccination-category-section vaccination-recommended">
                                            <div class="vaccination-section-header">
                                                <div class="vaccination-section-icon">
                                                    <i class="fas fa-thumbs-up"></i>
                                                </div>
                                                <h3 class="vaccination-section-title">Recommended Vaccinations</h3>
                                                <span class="vaccination-section-count">3 vaccines</span>
                                            </div>

                                            <div class="vaccination-item">
                                                <div class="vaccination-icon">
                                                    <i class="fas fa-liver"></i>
                                                </div>
                                                <div class="vaccination-content">
                                                    <div class="vaccination-title">
                                                        Hepatitis B
                                                    </div>
                                                    <div class="vaccination-description">Consider for longer stays or potential exposure to blood/bodily fluids</div>
                                                    <div class="vaccination-timing">
                                                        <i class="fas fa-clock timing-icon"></i>
                                                        <span class="timing-text">Series starts 6 months before travel</span>
                                                    </div>
                                                    <div class="vaccination-timeline">
                                                        <div class="timeline-step">
                                                            <div class="timeline-dot active">1</div>
                                                            <div class="timeline-label">Initial</div>
                                                        </div>
                                                        <div class="timeline-step">
                                                            <div class="timeline-dot">2</div>
                                                            <div class="timeline-label">1 month</div>
                                                        </div>
                                                        <div class="timeline-step">
                                                            <div class="timeline-dot">3</div>
                                                            <div class="timeline-label">6 months</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="priority-badge priority-recommended">
                                                    <i class="fas fa-check"></i>
                                                    Recommended
                                                </div>
                                            </div>

                                            <div class="vaccination-item">
                                                <div class="vaccination-icon">
                                                    <i class="fas fa-dog"></i>
                                                </div>
                                                <div class="vaccination-content">
                                                    <div class="vaccination-title">
                                                        Rabies
                                                    </div>
                                                    <div class="vaccination-description">Consider for rural travel or potential wildlife exposure</div>
                                                    <div class="vaccination-timing">
                                                        <i class="fas fa-clock timing-icon"></i>
                                                        <span class="timing-text">Series of 3 doses over 21-28 days</span>
                                                    </div>
                                                    <div class="vaccination-timeline">
                                                        <div class="timeline-step">
                                                            <div class="timeline-dot active">1</div>
                                                            <div class="timeline-label">Day 0</div>
                                                        </div>
                                                        <div class="timeline-step">
                                                            <div class="timeline-dot">2</div>
                                                            <div class="timeline-label">Day 7</div>
                                                        </div>
                                                        <div class="timeline-step">
                                                            <div class="timeline-dot">3</div>
                                                            <div class="timeline-label">Day 21-28</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="priority-badge priority-recommended">
                                                    <i class="fas fa-check"></i>
                                                    Recommended
                                                </div>
                                            </div>

                                            <div class="vaccination-item">
                                                <div class="vaccination-icon">
                                                    <i class="fas fa-brain"></i>
                                                </div>
                                                <div class="vaccination-content">
                                                    <div class="vaccination-title">
                                                        Japanese Encephalitis
                                                    </div>
                                                    <div class="vaccination-description">Consider for rural areas during transmission season</div>
                                                    <div class="vaccination-timing">
                                                        <i class="fas fa-clock timing-icon"></i>
                                                        <span class="timing-text">2 doses, 28 days apart</span>
                                                    </div>
                                                    <div class="vaccination-timeline">
                                                        <div class="timeline-step">
                                                            <div class="timeline-dot active">1</div>
                                                            <div class="timeline-label">Initial Dose</div>
                                                        </div>
                                                        <div class="timeline-step">
                                                            <div class="timeline-dot">2</div>
                                                            <div class="timeline-label">28 days later</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="priority-badge priority-recommended">
                                                    <i class="fas fa-check"></i>
                                                    Recommended
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Preventive Medications Section -->
                                        <div class="vaccination-category-section vaccination-preventive">
                                            <div class="vaccination-section-header">
                                                <div class="vaccination-section-icon">
                                                    <i class="fas fa-pills"></i>
                                                </div>
                                                <h3 class="vaccination-section-title">Preventive Medications</h3>
                                                <span class="vaccination-section-count">2 medications</span>
                                            </div>

                                            <div class="vaccination-item">
                                                <div class="vaccination-icon">
                                                    <i class="fas fa-capsules"></i>
                                                </div>
                                                <div class="vaccination-content">
                                                    <div class="vaccination-title">
                                                        Malaria Prophylaxis
                                                    </div>
                                                    <div class="vaccination-description">Recommended based on destination regions and travel dates</div>
                                                    <div class="vaccination-timing">
                                                        <i class="fas fa-clock timing-icon"></i>
                                                        <span class="timing-text">Start 1-2 weeks before travel, continue during and after</span>
                                                    </div>
                                                </div>
                                                <div class="priority-badge priority-preventive">
                                                    <i class="fas fa-shield-alt"></i>
                                                    Preventive
                                                </div>
                                            </div>

                                            <div class="vaccination-item">
                                                <div class="vaccination-icon">
                                                    <i class="fas fa-first-aid"></i>
                                                </div>
                                                <div class="vaccination-content">
                                                    <div class="vaccination-title">
                                                        Traveler's Diarrhea Kit
                                                    </div>
                                                    <div class="vaccination-description">Consider antibiotics prescription for self-treatment</div>
                                                    <div class="vaccination-timing">
                                                        <i class="fas fa-clock timing-icon"></i>
                                                        <span class="timing-text">Carry during travel for emergency use</span>
                                                    </div>
                                                </div>
                                                <div class="priority-badge priority-preventive">
                                                    <i class="fas fa-shield-alt"></i>
                                                    Preventive
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="alert alert-info mt-4">
                                            <div class="d-flex">
                                                <div class="me-3">
                                                    <i class="fas fa-info-circle fa-2x text-info"></i>
                                                </div>
                                                <div>
                                                    <h6 class="alert-heading">Important Note</h6>
                                                    <p class="mb-0">This plan is based on current CDC and WHO recommendations for <?= htmlspecialchars($protocol_data['destination'] ?? 'this region') ?>. Individual recommendations may vary based on patient-specific factors, medical history, and current health status.</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <script>
                                        // Simulate loading of vaccination plan with enhanced animations
                                        window.addEventListener('DOMContentLoaded', function() {
                                            setTimeout(function() {
                                                var spinner = document.querySelector('.spinner-border').parentElement;
                                                var content = document.querySelector('.vaccination-plan-content');
                                                var input = document.querySelector('input[name="vaccination_plan"]');
                                                
                                                if (spinner && content && input) {
                                                    spinner.style.display = 'none';
                                                    content.style.display = 'block';
                                                    input.value = content.innerHTML;
                                                    
                                                    // Animate vaccination sections appearing
                                                    const vaccinationSections = content.querySelectorAll('.vaccination-category-section');
                                                    vaccinationSections.forEach((section, index) => {
                                                        section.style.opacity = '0';
                                                        section.style.transform = 'translateY(20px)';
                                                        setTimeout(() => {
                                                            section.style.transition = 'all 0.5s ease';
                                                            section.style.opacity = '1';
                                                            section.style.transform = 'translateY(0)';
                                                        }, index * 300);
                                                    });
                                                }
                                            }, 1500);
                                            
                                            // Add interactive vaccination features
                                            setTimeout(function() {
                                                initializeVaccinationInteractions();
                                            }, 2000);
                                        });
                                        
                                        function initializeVaccinationInteractions() {
                                            // Add click-to-expand functionality for vaccination details
                                            const vaccinationItems = document.querySelectorAll('.vaccination-item');
                                            vaccinationItems.forEach(item => {
                                                const content = item.querySelector('.vaccination-content');
                                                const timeline = item.querySelector('.vaccination-timeline');
                                                
                                                // Create expandable details section
                                                const detailsDiv = document.createElement('div');
                                                detailsDiv.className = 'vaccination-details';
                                                detailsDiv.innerHTML = `
                                                    <div class="vaccination-details-content">
                                                        <div class="detail-row">
                                                            <span class="detail-label">Side Effects:</span>
                                                            <span class="detail-value">Mild soreness, low-grade fever</span>
                                                        </div>
                                                        <div class="detail-row">
                                                            <span class="detail-label">Contraindications:</span>
                                                            <span class="detail-value">Severe illness, pregnancy (consult doctor)</span>
                                                        </div>
                                                        <div class="detail-row">
                                                            <span class="detail-label">Cost Range:</span>
                                                            <span class="detail-value">$50 - $150 per dose</span>
                                                        </div>
                                                    </div>
                                                `;
                                                
                                                content.appendChild(detailsDiv);
                                                
                                                // Add click handler to expand/collapse
                                                item.addEventListener('click', function(e) {
                                                    e.preventDefault();
                                                    const isExpanded = detailsDiv.classList.contains('expanded');
                                                    
                                                    // Close all other expanded items
                                                    document.querySelectorAll('.vaccination-details.expanded').forEach(detail => {
                                                        if (detail !== detailsDiv) {
                                                            detail.classList.remove('expanded');
                                                        }
                                                    });
                                                    
                                                    if (isExpanded) {
                                                        detailsDiv.classList.remove('expanded');
                                                        item.classList.remove('expanded');
                                                    } else {
                                                        detailsDiv.classList.add('expanded');
                                                        item.classList.add('expanded');
                                                    }
                                                });
                                                
                                                // Add visual indicator for clickable items
                                                item.style.cursor = 'pointer';
                                                item.title = 'Click to view detailed information';
                                                
                                                // Add expand indicator
                                                const expandIcon = document.createElement('div');
                                                expandIcon.innerHTML = '<i class="fas fa-chevron-down"></i>';
                                                expandIcon.className = 'expand-indicator';
                                                expandIcon.style.cssText = `
                                                    position: absolute;
                                                    bottom: 1rem;
                                                    right: 1rem;
                                                    color: #6c757d;
                                                    font-size: 0.875rem;
                                                    transition: transform 0.3s ease;
                                                `;
                                                item.style.position = 'relative';
                                                item.appendChild(expandIcon);
                                                
                                                // Update expand indicator on click
                                                item.addEventListener('click', function() {
                                                    const isExpanded = detailsDiv.classList.contains('expanded');
                                                    expandIcon.style.transform = isExpanded ? 'rotate(180deg)' : 'rotate(0deg)';
                                                });
                                            });
                                            
                                            // Add hover effects for priority badges
                                            const priorityBadges = document.querySelectorAll('.priority-badge');
                                            priorityBadges.forEach(badge => {
                                                badge.addEventListener('mouseenter', function() {
                                                    this.style.transform = 'scale(1.05)';
                                                    this.style.transition = 'transform 0.2s ease';
                                                });
                                                
                                                badge.addEventListener('mouseleave', function() {
                                                    this.style.transform = 'scale(1)';
                                                });
                                            });
                                            
                                            // Add timeline interaction
                                            const timelineDots = document.querySelectorAll('.timeline-dot');
                                            timelineDots.forEach(dot => {
                                                dot.addEventListener('mouseenter', function() {
                                                    if (!this.classList.contains('active') && !this.classList.contains('completed')) {
                                                        this.style.backgroundColor = '#007bff';
                                                        this.style.color = 'white';
                                                        this.style.transform = 'scale(1.1)';
                                                        this.style.transition = 'all 0.2s ease';
                                                    }
                                                });
                                                
                                                dot.addEventListener('mouseleave', function() {
                                                    if (!this.classList.contains('active') && !this.classList.contains('completed')) {
                                                        this.style.backgroundColor = '#dee2e6';
                                                        this.style.color = '#6c757d';
                                                        this.style.transform = 'scale(1)';
                                                    }
                                                });
                                            });
                                            
                                            // Add section collapse/expand functionality
                                            const sectionHeaders = document.querySelectorAll('.vaccination-section-header');
                                            sectionHeaders.forEach(header => {
                                                const section = header.parentElement;
                                                const vaccinationItems = section.querySelectorAll('.vaccination-item');
                                                
                                                // Add collapse button
                                                const collapseBtn = document.createElement('button');
                                                collapseBtn.innerHTML = '<i class="fas fa-chevron-up"></i>';
                                                collapseBtn.className = 'btn btn-sm btn-outline-secondary ms-2';
                                                collapseBtn.style.border = 'none';
                                                collapseBtn.title = 'Collapse section';
                                                
                                                header.appendChild(collapseBtn);
                                                
                                                collapseBtn.addEventListener('click', function(e) {
                                                    e.stopPropagation();
                                                    const isCollapsed = section.classList.contains('collapsed');
                                                    
                                                    if (isCollapsed) {
                                                        // Expand
                                                        vaccinationItems.forEach(item => {
                                                            item.style.display = 'flex';
                                                        });
                                                        section.classList.remove('collapsed');
                                                        collapseBtn.innerHTML = '<i class="fas fa-chevron-up"></i>';
                                                        collapseBtn.title = 'Collapse section';
                                                    } else {
                                                        // Collapse
                                                        vaccinationItems.forEach(item => {
                                                            item.style.display = 'none';
                                                        });
                                                        section.classList.add('collapsed');
                                                        collapseBtn.innerHTML = '<i class="fas fa-chevron-down"></i>';
                                                        collapseBtn.title = 'Expand section';
                                                    }
                                                });
                                            });
                                        }
                                    </script>
                                    <input type="hidden" name="vaccination_plan" value="">
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <a href="<?= site_url('travel-medicine/create/3') ?>" class="btn btn-outline-secondary btn-lg">
                            <i class="fas fa-arrow-left me-2"></i>Previous
                        </a>
                        <button type="submit" class="btn btn-primary btn-lg">
                            Next<i class="fas fa-arrow-right ms-2"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>