<?php
/**
 * Travel Protocol - Step 3: Risk Analysis
 */
?>

<!-- CSS for protocol steps styling -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="<?= site_url('resources/css/protocol-steps.css') ?>">
<link rel="stylesheet" href="<?= site_url('resources/css/medical-ui-enhanced.css') ?>">

<div class="protocol-layout">
    <!-- Left sidebar with steps -->
    <div class="protocol-sidebar">
        <?php include __DIR__ . '/step-nav.php'; ?>
    </div>

    <!-- Main content area -->
    <div class="protocol-content">
        <div class="card shadow-sm">
            <div class="card-header bg-white">
                <h4 class="mb-0">Risk Analysis</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-info mb-4">
                    <div class="d-flex">
                        <div class="me-3">
                            <i class="fas fa-info-circle fa-2x text-info"></i>
                        </div>
                        <div>
                            <h5 class="alert-heading">Destination: <?= htmlspecialchars($protocol_data['destination'] ?? 'Unknown') ?></h5>
                            <p class="mb-0">Based on the travel information and patient assessment, we've analyzed potential health risks for this destination.</p>
                        </div>
                    </div>
                </div>

                <form action="<?= site_url('travel-medicine/process-step') ?>" method="post">
                    <?= csrf_field() ?>
                    <input type="hidden" name="step" value="3">

                    <div class="mb-4">
                        <h5>Risk Assessment</h5>
                        <div class="card">
                            <div class="card-body">
                                <?php if (isset($risk_assessment) && !empty($risk_assessment)): ?>
                                    <div class="risk-assessment-content">
                                        <!-- Enhanced Risk Assessment Display -->
                                        <div class="risk-category-section disease-risk-high">
                                            <div class="risk-section-header">
                                                <div class="risk-section-icon">
                                                    <i class="fas fa-virus"></i>
                                                </div>
                                                <h3 class="risk-section-title">High Risk Diseases</h3>
                                                <span class="risk-section-count">2 risks</span>
                                            </div>
                                            <div class="risk-item">
                                                <div class="risk-icon">
                                                    <i class="fas fa-bug"></i>
                                                </div>
                                                <div class="risk-content">
                                                    <div class="risk-title">
                                                        Malaria
                                                        <span class="severity-badge severity-high">High Risk</span>
                                                    </div>
                                                    <div class="risk-description">High risk in rural areas, moderate in urban centers</div>
                                                    <div class="risk-recommendations">Prophylaxis recommended, use insect repellent, sleep under nets</div>
                                                </div>
                                            </div>
                                            <div class="risk-item">
                                                <div class="risk-icon">
                                                    <i class="fas fa-mosquito"></i>
                                                </div>
                                                <div class="risk-content">
                                                    <div class="risk-title">
                                                        Dengue Fever
                                                        <span class="severity-badge severity-high">High Risk</span>
                                                    </div>
                                                    <div class="risk-description">Present year-round with increased risk during rainy season</div>
                                                    <div class="risk-recommendations">No vaccine available, focus on mosquito bite prevention</div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Moderate Risk Diseases -->
                                        <div class="risk-category-section disease-risk-moderate">
                                            <div class="risk-section-header">
                                                <div class="risk-section-icon">
                                                    <i class="fas fa-exclamation-triangle"></i>
                                                </div>
                                                <h3 class="risk-section-title">Moderate Risk Diseases</h3>
                                                <span class="risk-section-count">2 risks</span>
                                            </div>
                                            <div class="risk-item">
                                                <div class="risk-icon">
                                                    <i class="fas fa-utensils"></i>
                                                </div>
                                                <div class="risk-content">
                                                    <div class="risk-title">
                                                        Typhoid
                                                        <span class="severity-badge severity-moderate">Moderate</span>
                                                    </div>
                                                    <div class="risk-description">Moderate risk through contaminated food and water</div>
                                                    <div class="risk-recommendations">Vaccination recommended, practice food and water safety</div>
                                                </div>
                                            </div>
                                            <div class="risk-item">
                                                <div class="risk-icon">
                                                    <i class="fas fa-prescription-bottle"></i>
                                                </div>
                                                <div class="risk-content">
                                                    <div class="risk-title">
                                                        Hepatitis A
                                                        <span class="severity-badge severity-moderate">Moderate</span>
                                                    </div>
                                                    <div class="risk-description">Present throughout the region</div>
                                                    <div class="risk-recommendations">Vaccination strongly recommended for all travelers</div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Environmental Risks -->
                                        <div class="risk-category-section environmental-risk">
                                            <div class="risk-section-header">
                                                <div class="risk-section-icon">
                                                    <i class="fas fa-cloud-sun"></i>
                                                </div>
                                                <h3 class="risk-section-title">Environmental Risks</h3>
                                                <span class="risk-section-count">3 risks</span>
                                            </div>
                                            <div class="risk-item">
                                                <div class="risk-icon">
                                                    <i class="fas fa-thermometer-full"></i>
                                                </div>
                                                <div class="risk-content">
                                                    <div class="risk-title">Altitude Sickness</div>
                                                    <div class="risk-description">Some regions have high elevation, risk of altitude sickness</div>
                                                    <div class="risk-recommendations">Gradual ascent, stay hydrated, consider medication</div>
                                                </div>
                                            </div>
                                            <div class="risk-item">
                                                <div class="risk-icon">
                                                    <i class="fas fa-thermometer-full"></i>
                                                </div>
                                                <div class="risk-content">
                                                    <div class="risk-title">Heat-related Illness</div>
                                                    <div class="risk-description">Risk of heat exhaustion and dehydration</div>
                                                    <div class="risk-recommendations">Stay hydrated, avoid midday sun, wear light clothing</div>
                                                </div>
                                            </div>
                                            <div class="risk-item">
                                                <div class="risk-icon">
                                                    <i class="fas fa-tint"></i>
                                                </div>
                                                <div class="risk-content">
                                                    <div class="risk-title">Water Safety</div>
                                                    <div class="risk-description">Risk from contaminated water sources</div>
                                                    <div class="risk-recommendations">Drink only bottled or boiled water, avoid ice</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <input type="hidden" name="risk_assessment" value="<?= htmlspecialchars($risk_assessment) ?>">
                                    <script>
                                        // Initialize interactions for pre-loaded content
                                        document.addEventListener('DOMContentLoaded', function() {
                                            initializeRiskInteractions();
                                        });
                                        
                                        function initializeRiskInteractions() {
                                            // Add click-to-expand functionality for risk items
                                            const riskItems = document.querySelectorAll('.risk-item');
                                            riskItems.forEach(item => {
                                                const content = item.querySelector('.risk-content');
                                                const recommendations = item.querySelector('.risk-recommendations');
                                                
                                                if (recommendations) {
                                                    // Initially hide recommendations
                                                    recommendations.style.maxHeight = '0';
                                                    recommendations.style.overflow = 'hidden';
                                                    recommendations.style.transition = 'max-height 0.3s ease';
                                                    
                                                    // Add click handler to expand/collapse
                                                    item.addEventListener('click', function() {
                                                        const isExpanded = recommendations.style.maxHeight !== '0px';
                                                        
                                                        if (isExpanded) {
                                                            recommendations.style.maxHeight = '0';
                                                            item.classList.remove('expanded');
                                                        } else {
                                                            recommendations.style.maxHeight = recommendations.scrollHeight + 'px';
                                                            item.classList.add('expanded');
                                                        }
                                                    });
                                                    
                                                    // Add visual indicator for clickable items
                                                    item.style.cursor = 'pointer';
                                                    item.title = 'Click to view recommendations';
                                                }
                                            });
                                            
                                            // Add hover effects for severity badges
                                            const severityBadges = document.querySelectorAll('.severity-badge');
                                            severityBadges.forEach(badge => {
                                                badge.addEventListener('mouseenter', function() {
                                                    this.style.transform = 'scale(1.1)';
                                                    this.style.transition = 'transform 0.2s ease';
                                                });
                                                
                                                badge.addEventListener('mouseleave', function() {
                                                    this.style.transform = 'scale(1)';
                                                });
                                            });
                                        }
                                    </script>
                                <?php else: ?>
                                    <div class="d-flex justify-content-center align-items-center p-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <span class="ms-3">Generating risk assessment...</span>
                                    </div>
                                    <div class="risk-assessment-content fade-in" style="display: none;">
                                        <!-- Disease Risks Section -->
                                        <div class="risk-category-section disease-risk-high">
                                            <div class="risk-section-header">
                                                <div class="risk-section-icon">
                                                    <i class="fas fa-virus"></i>
                                                </div>
                                                <h3 class="risk-section-title">High Risk Diseases</h3>
                                                <span class="risk-section-count">2 risks</span>
                                            </div>
                                            <div class="risk-item">
                                                <div class="risk-icon">
                                                    <i class="fas fa-bug"></i>
                                                </div>
                                                <div class="risk-content">
                                                    <div class="risk-title">
                                                        Malaria
                                                        <span class="severity-badge severity-high">High Risk</span>
                                                    </div>
                                                    <div class="risk-description">High risk in rural areas, moderate in urban centers</div>
                                                    <div class="risk-recommendations">Prophylaxis recommended, use insect repellent, sleep under nets</div>
                                                </div>
                                            </div>
                                            <div class="risk-item">
                                                <div class="risk-icon">
                                                    <i class="fas fa-mosquito"></i>
                                                </div>
                                                <div class="risk-content">
                                                    <div class="risk-title">
                                                        Dengue Fever
                                                        <span class="severity-badge severity-high">High Risk</span>
                                                    </div>
                                                    <div class="risk-description">Present year-round with increased risk during rainy season</div>
                                                    <div class="risk-recommendations">No vaccine available, focus on mosquito bite prevention</div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Moderate Risk Diseases -->
                                        <div class="risk-category-section disease-risk-moderate">
                                            <div class="risk-section-header">
                                                <div class="risk-section-icon">
                                                    <i class="fas fa-exclamation-triangle"></i>
                                                </div>
                                                <h3 class="risk-section-title">Moderate Risk Diseases</h3>
                                                <span class="risk-section-count">2 risks</span>
                                            </div>
                                            <div class="risk-item">
                                                <div class="risk-icon">
                                                    <i class="fas fa-utensils"></i>
                                                </div>
                                                <div class="risk-content">
                                                    <div class="risk-title">
                                                        Typhoid
                                                        <span class="severity-badge severity-moderate">Moderate</span>
                                                    </div>
                                                    <div class="risk-description">Moderate risk through contaminated food and water</div>
                                                    <div class="risk-recommendations">Vaccination recommended, practice food and water safety</div>
                                                </div>
                                            </div>
                                            <div class="risk-item">
                                                <div class="risk-icon">
                                                    <i class="fas fa-prescription-bottle"></i>
                                                </div>
                                                <div class="risk-content">
                                                    <div class="risk-title">
                                                        Hepatitis A
                                                        <span class="severity-badge severity-moderate">Moderate</span>
                                                    </div>
                                                    <div class="risk-description">Present throughout the region</div>
                                                    <div class="risk-recommendations">Vaccination strongly recommended for all travelers</div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Low Risk Diseases -->
                                        <div class="risk-category-section disease-risk-low">
                                            <div class="risk-section-header">
                                                <div class="risk-section-icon">
                                                    <i class="fas fa-info-circle"></i>
                                                </div>
                                                <h3 class="risk-section-title">Common Travel Risks</h3>
                                                <span class="risk-section-count">1 risk</span>
                                            </div>
                                            <div class="risk-item">
                                                <div class="risk-icon">
                                                    <i class="fas fa-stomach"></i>
                                                </div>
                                                <div class="risk-content">
                                                    <div class="risk-title">
                                                        Traveler's Diarrhea
                                                        <span class="severity-badge severity-low">Common</span>
                                                    </div>
                                                    <div class="risk-description">Common risk for visitors</div>
                                                    <div class="risk-recommendations">Carry anti-diarrheal medication, practice food safety</div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Environmental Risks -->
                                        <div class="risk-category-section environmental-risk">
                                            <div class="risk-section-header">
                                                <div class="risk-section-icon">
                                                    <i class="fas fa-cloud-sun"></i>
                                                </div>
                                                <h3 class="risk-section-title">Environmental Risks</h3>
                                                <span class="risk-section-count">3 risks</span>
                                            </div>
                                            <div class="risk-item">
                                                <div class="risk-icon">
                                                    <i class="fas fa-thermometer-full"></i>
                                                </div>
                                                <div class="risk-content">
                                                    <div class="risk-title">Heat and Humidity</div>
                                                    <div class="risk-description">Risk of heat exhaustion and dehydration</div>
                                                    <div class="risk-recommendations">Stay hydrated, avoid midday sun, wear light clothing</div>
                                                </div>
                                            </div>
                                            <div class="risk-item">
                                                <div class="risk-icon">
                                                    <i class="fas fa-smog"></i>
                                                </div>
                                                <div class="risk-content">
                                                    <div class="risk-title">Air Quality</div>
                                                    <div class="risk-description">Poor in urban areas, may exacerbate respiratory conditions</div>
                                                    <div class="risk-recommendations">Consider air pollution masks, limit outdoor activities</div>
                                                </div>
                                            </div>
                                            <div class="risk-item">
                                                <div class="risk-icon">
                                                    <i class="fas fa-mountain"></i>
                                                </div>
                                                <div class="risk-content">
                                                    <div class="risk-title">Altitude</div>
                                                    <div class="risk-description">Some regions have high elevation, risk of altitude sickness</div>
                                                    <div class="risk-recommendations">Gradual ascent, stay hydrated, consider medication</div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Safety Considerations -->
                                        <div class="risk-category-section safety-risk">
                                            <div class="risk-section-header">
                                                <div class="risk-section-icon">
                                                    <i class="fas fa-shield-alt"></i>
                                                </div>
                                                <h3 class="risk-section-title">Safety Considerations</h3>
                                                <span class="risk-section-count">2 risks</span>
                                            </div>
                                            <div class="risk-item">
                                                <div class="risk-icon">
                                                    <i class="fas fa-car-crash"></i>
                                                </div>
                                                <div class="risk-content">
                                                    <div class="risk-title">Road Safety</div>
                                                    <div class="risk-description">Higher accident rates than Western countries</div>
                                                    <div class="risk-recommendations">Use reputable transport, wear seatbelts, avoid night travel</div>
                                                </div>
                                            </div>
                                            <div class="risk-item">
                                                <div class="risk-icon">
                                                    <i class="fas fa-hospital"></i>
                                                </div>
                                                <div class="risk-content">
                                                    <div class="risk-title">Medical Facilities</div>
                                                    <div class="risk-description">Variable quality, limited in rural areas</div>
                                                    <div class="risk-recommendations">Obtain travel insurance, locate nearest quality facilities</div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Patient-Specific Risks -->
                                        <div class="risk-category-section patient-specific-risk">
                                            <div class="risk-section-header">
                                                <div class="risk-section-icon">
                                                    <i class="fas fa-user-md"></i>
                                                </div>
                                                <h3 class="risk-section-title">Patient-Specific Considerations</h3>
                                                <span class="risk-section-count">Individual</span>
                                            </div>
                                            <div class="risk-item">
                                                <div class="risk-icon">
                                                    <i class="fas fa-pills"></i>
                                                </div>
                                                <div class="risk-content">
                                                    <div class="risk-title">Medication Management</div>
                                                    <div class="risk-description">Based on the patient profile, additional considerations include potential medication interactions and pre-existing condition management during travel.</div>
                                                    <div class="risk-recommendations">Review current medications, ensure adequate supply, check for interactions</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <script>
                                        // Simulate loading of risk assessment with enhanced animations
                                        window.addEventListener('DOMContentLoaded', function() {
                                            setTimeout(function() {
                                                var spinner = document.querySelector('.spinner-border').parentElement;
                                                var content = document.querySelector('.risk-assessment-content');
                                                var input = document.querySelector('input[name="risk_assessment"]');
                                                
                                                if (spinner && content && input) {
                                                    spinner.style.display = 'none';
                                                    content.style.display = 'block';
                                                    input.value = content.innerHTML;
                                                    
                                                    // Animate risk sections appearing
                                                    const riskSections = content.querySelectorAll('.risk-category-section');
                                                    riskSections.forEach((section, index) => {
                                                        section.style.opacity = '0';
                                                        section.style.transform = 'translateY(20px)';
                                                        setTimeout(() => {
                                                            section.style.transition = 'all 0.5s ease';
                                                            section.style.opacity = '1';
                                                            section.style.transform = 'translateY(0)';
                                                        }, index * 200);
                                                    });
                                                }
                                            }, 1500);
                                            
                                            // Add interactive hover effects and tooltips
                                            setTimeout(function() {
                                                initializeRiskInteractions();
                                            }, 2000);
                                        });
                                        
                                        function initializeRiskInteractions() {
                                            // Add click-to-expand functionality for risk items
                                            const riskItems = document.querySelectorAll('.risk-item');
                                            riskItems.forEach(item => {
                                                const content = item.querySelector('.risk-content');
                                                const recommendations = item.querySelector('.risk-recommendations');
                                                
                                                if (recommendations) {
                                                    // Initially hide recommendations
                                                    recommendations.style.maxHeight = '0';
                                                    recommendations.style.overflow = 'hidden';
                                                    recommendations.style.transition = 'max-height 0.3s ease';
                                                    
                                                    // Add click handler to expand/collapse
                                                    item.addEventListener('click', function() {
                                                        const isExpanded = recommendations.style.maxHeight !== '0px';
                                                        
                                                        if (isExpanded) {
                                                            recommendations.style.maxHeight = '0';
                                                            item.classList.remove('expanded');
                                                        } else {
                                                            recommendations.style.maxHeight = recommendations.scrollHeight + 'px';
                                                            item.classList.add('expanded');
                                                        }
                                                    });
                                                    
                                                    // Add visual indicator for clickable items
                                                    item.style.cursor = 'pointer';
                                                    item.title = 'Click to view recommendations';
                                                }
                                            });
                                            
                                            // Add hover effects for severity badges
                                            const severityBadges = document.querySelectorAll('.severity-badge');
                                            severityBadges.forEach(badge => {
                                                badge.addEventListener('mouseenter', function() {
                                                    this.style.transform = 'scale(1.1)';
                                                    this.style.transition = 'transform 0.2s ease';
                                                });
                                                
                                                badge.addEventListener('mouseleave', function() {
                                                    this.style.transform = 'scale(1)';
                                                });
                                            });
                                            
                                            // Add section collapse/expand functionality
                                            const sectionHeaders = document.querySelectorAll('.risk-section-header');
                                            sectionHeaders.forEach(header => {
                                                const section = header.parentElement;
                                                const riskItems = section.querySelectorAll('.risk-item');
                                                
                                                // Add collapse button
                                                const collapseBtn = document.createElement('button');
                                                collapseBtn.innerHTML = '<i class="fas fa-chevron-up"></i>';
                                                collapseBtn.className = 'btn btn-sm btn-outline-secondary ms-2';
                                                collapseBtn.style.border = 'none';
                                                collapseBtn.title = 'Collapse section';
                                                
                                                header.appendChild(collapseBtn);
                                                
                                                collapseBtn.addEventListener('click', function(e) {
                                                    e.stopPropagation();
                                                    const isCollapsed = section.classList.contains('collapsed');
                                                    
                                                    if (isCollapsed) {
                                                        // Expand
                                                        riskItems.forEach(item => {
                                                            item.style.display = 'flex';
                                                        });
                                                        section.classList.remove('collapsed');
                                                        collapseBtn.innerHTML = '<i class="fas fa-chevron-up"></i>';
                                                        collapseBtn.title = 'Collapse section';
                                                    } else {
                                                        // Collapse
                                                        riskItems.forEach(item => {
                                                            item.style.display = 'none';
                                                        });
                                                        section.classList.add('collapsed');
                                                        collapseBtn.innerHTML = '<i class="fas fa-chevron-down"></i>';
                                                        collapseBtn.title = 'Expand section';
                                                    }
                                                });
                                            });
                                        }
                                    </script>
                                    <input type="hidden" name="risk_assessment" value="">
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <a href="<?= site_url('travel-medicine/create/2') ?>" class="btn btn-outline-secondary btn-lg">
                            <i class="fas fa-arrow-left me-2"></i>Previous
                        </a>
                        <button type="submit" class="btn btn-primary btn-lg">
                            Next<i class="fas fa-arrow-right ms-2"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>