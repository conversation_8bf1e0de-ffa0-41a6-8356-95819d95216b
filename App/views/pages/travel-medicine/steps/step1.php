<?php
/**
 * Travel Protocol - Step 1: Travel Information
 */
?>

<!-- CSS for protocol steps styling -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="<?= site_url('resources/css/protocol-steps.css') ?>">
<link rel="stylesheet" href="<?= site_url('resources/css/medical-ui-enhanced.css') ?>">

<div class="protocol-layout">
    <!-- Left sidebar with steps -->
    <div class="protocol-sidebar">
        <?php include __DIR__ . '/step-nav.php'; ?>
    </div>

    <!-- Main content area -->
    <div class="protocol-content">
        <div class="card shadow-sm">
            <div class="card-header bg-white">
                <h4 class="mb-0">Travel Information</h4>
            </div>
            <div class="card-body">
                    <div class="alert alert-info mb-4">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-plane fa-2x text-info"></i>
                            </div>
                            <div>
                                <h5 class="alert-heading">Travel Information Collection</h5>
                                <p class="mb-0">Please provide details about your upcoming travel to help us create a personalized health and vaccination plan.</p>
                            </div>
                        </div>
                    </div>

                    <form action="<?= site_url('travel-medicine/process-step') ?>" method="post">
                        <?= csrf_field() ?>
                        <input type="hidden" name="step" value="1">

                        <!-- Destination Information Card -->
                        <div class="medical-card mb-4">
                            <div class="medical-card-header">
                                <div class="medical-card-icon" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <h3 class="medical-card-title">Destination Details</h3>
                            </div>
                            <div class="medical-card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="destination" class="form-label medical-heading-3">Destination Country/Region</label>
                                        <input type="text" class="form-control form-control-lg" id="destination" name="destination" 
                                               placeholder="e.g., Thailand, Kenya, Brazil" 
                                               value="<?= $protocol_data['destination'] ?? '' ?>" required>
                                        <small class="form-text text-muted">Enter the primary destination for your travel</small>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="travel_type" class="form-label medical-heading-3">Travel Type</label>
                                        <select class="form-select form-select-lg" id="travel_type" name="travel_type">
                                            <option value="">Select travel type</option>
                                            <option value="business" <?= isset($protocol_data['travel_type']) && $protocol_data['travel_type'] === 'business' ? 'selected' : '' ?>>
                                                <i class="fas fa-briefcase"></i> Business
                                            </option>
                                            <option value="tourism" <?= isset($protocol_data['travel_type']) && $protocol_data['travel_type'] === 'tourism' ? 'selected' : '' ?>>
                                                <i class="fas fa-camera"></i> Tourism
                                            </option>
                                            <option value="backpacking" <?= isset($protocol_data['travel_type']) && $protocol_data['travel_type'] === 'backpacking' ? 'selected' : '' ?>>
                                                <i class="fas fa-hiking"></i> Backpacking
                                            </option>
                                            <option value="volunteering" <?= isset($protocol_data['travel_type']) && $protocol_data['travel_type'] === 'volunteering' ? 'selected' : '' ?>>
                                                <i class="fas fa-hands-helping"></i> Volunteering
                                            </option>
                                            <option value="visiting_family" <?= isset($protocol_data['travel_type']) && $protocol_data['travel_type'] === 'visiting_family' ? 'selected' : '' ?>>
                                                <i class="fas fa-home"></i> Visiting Family
                                            </option>
                                        </select>
                                        <small class="form-text text-muted">This helps determine specific health risks</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Travel Timeline Card -->
                        <div class="medical-card mb-4">
                            <div class="medical-card-header">
                                <div class="medical-card-icon" style="background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <h3 class="medical-card-title">Travel Timeline</h3>
                            </div>
                            <div class="medical-card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="duration" class="form-label medical-heading-3">Duration</label>
                                        <input type="text" class="form-control form-control-lg" id="duration" name="duration" 
                                               placeholder="e.g., 2 weeks, 3 months" 
                                               value="<?= $protocol_data['duration'] ?? '' ?>">
                                        <small class="form-text text-muted">How long will you be traveling?</small>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="departure_date" class="form-label medical-heading-3">Departure Date</label>
                                        <input type="date" class="form-control form-control-lg" id="departure_date" name="departure_date" 
                                               value="<?= $protocol_data['departure_date'] ?? '' ?>">
                                        <small class="form-text text-muted">When do you plan to depart?</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <button type="button" class="btn btn-outline-secondary btn-lg" disabled>
                                <i class="fas fa-arrow-left me-2"></i>Previous
                            </button>
                            <button type="submit" class="btn btn-primary btn-lg">
                                Next<i class="fas fa-arrow-right ms-2"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
