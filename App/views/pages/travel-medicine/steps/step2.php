<?php
/**
 * Travel Protocol - Step 2: Patient Assessment
 */
?>

<!-- CSS for protocol steps styling -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="<?= site_url('resources/css/protocol-steps.css') ?>">
<link rel="stylesheet" href="<?= site_url('resources/css/medical-ui-enhanced.css') ?>">

<div class="protocol-layout">
    <!-- Left sidebar with steps -->
    <div class="protocol-sidebar">
        <?php include __DIR__ . '/step-nav.php'; ?>
    </div>
    
    <!-- Main content area -->
    <div class="protocol-content">
        <div class="card shadow-sm">
            <div class="card-header bg-white">
                <h4 class="mb-0">Patient Assessment</h4>
            </div>
            <div class="card-body">
                    <div class="alert alert-info mb-4">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-user-md fa-2x text-info"></i>
                            </div>
                            <div>
                                <h5 class="alert-heading">Patient Assessment</h5>
                                <p class="mb-0">Please provide patient information and medical history to ensure safe and appropriate travel health recommendations.</p>
                            </div>
                        </div>
                    </div>

                    <form action="<?= site_url('travel-medicine/process-step') ?>" method="post">
                        <?= csrf_field() ?>
                        <input type="hidden" name="step" value="2">

                        <!-- Patient Selection Card -->
                        <div class="medical-card mb-4">
                            <div class="medical-card-header">
                                <div class="medical-card-icon" style="background: linear-gradient(135deg, #198754 0%, #157347 100%);">
                                    <i class="fas fa-user"></i>
                                </div>
                                <h3 class="medical-card-title">Patient Selection</h3>
                            </div>
                            <div class="medical-card-body">
                                <div class="mb-3">
                                    <label for="patient_id" class="form-label medical-heading-3">Select Patient</label>
                                    <select class="form-select form-select-lg" id="patient_id" name="patient_id" required>
                                        <option value="">Choose a patient from your records</option>
                                        <?php foreach ($patients as $patient): ?>
                                            <option value="<?= $patient['id'] ?>" <?= isset($protocol_data['patient_id']) && $protocol_data['patient_id'] === $patient['id'] ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($patient['first_name'] . ' ' . $patient['last_name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <small class="form-text text-muted">Select the patient who will be traveling</small>
                                </div>
                            </div>
                        </div>

                        <!-- Demographics Card -->
                        <div class="medical-card mb-4">
                            <div class="medical-card-header">
                                <div class="medical-card-icon" style="background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);">
                                    <i class="fas fa-id-card"></i>
                                </div>
                                <h3 class="medical-card-title">Demographics</h3>
                            </div>
                            <div class="medical-card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="age" class="form-label medical-heading-3">Age</label>
                                        <input type="number" class="form-control form-control-lg" id="age" name="age" 
                                               placeholder="Enter age in years"
                                               value="<?= $protocol_data['age'] ?? '' ?>">
                                        <small class="form-text text-muted">Age affects vaccination recommendations</small>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="gender" class="form-label medical-heading-3">Gender</label>
                                        <select class="form-select form-select-lg" id="gender" name="gender">
                                            <option value="">Select gender</option>
                                            <option value="female" <?= isset($protocol_data['gender']) && $protocol_data['gender'] === 'female' ? 'selected' : '' ?>>
                                                <i class="fas fa-venus"></i> Female
                                            </option>
                                            <option value="male" <?= isset($protocol_data['gender']) && $protocol_data['gender'] === 'male' ? 'selected' : '' ?>>
                                                <i class="fas fa-mars"></i> Male
                                            </option>
                                        </select>
                                        <small class="form-text text-muted">Gender-specific health considerations</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Reproductive Health Card -->
                        <div class="medical-card mb-4">
                            <div class="medical-card-header">
                                <div class="medical-card-icon" style="background: linear-gradient(135deg, #e83e8c 0%, #d91a72 100%);">
                                    <i class="fas fa-baby"></i>
                                </div>
                                <h3 class="medical-card-title">Reproductive Health</h3>
                            </div>
                            <div class="medical-card-body">
                                <div class="mb-3">
                                    <label for="pregnancy_status" class="form-label medical-heading-3">Pregnancy Status (if applicable)</label>
                                    <select class="form-select form-select-lg" id="pregnancy_status" name="pregnancy_status">
                                        <option value="">Select status</option>
                                        <option value="pregnant" <?= isset($protocol_data['pregnancy_status']) && $protocol_data['pregnancy_status'] === 'pregnant' ? 'selected' : '' ?>>
                                            <i class="fas fa-baby"></i> Pregnant
                                        </option>
                                        <option value="breastfeeding" <?= isset($protocol_data['pregnancy_status']) && $protocol_data['pregnancy_status'] === 'breastfeeding' ? 'selected' : '' ?>>
                                            <i class="fas fa-heart"></i> Breastfeeding
                                        </option>
                                        <option value="planning_pregnancy" <?= isset($protocol_data['pregnancy_status']) && $protocol_data['pregnancy_status'] === 'planning_pregnancy' ? 'selected' : '' ?>>
                                            <i class="fas fa-calendar-plus"></i> Planning Pregnancy
                                        </option>
                                        <option value="not_pregnant" <?= isset($protocol_data['pregnancy_status']) && $protocol_data['pregnancy_status'] === 'not_pregnant' ? 'selected' : '' ?>>
                                            <i class="fas fa-times-circle"></i> Not Pregnant
                                        </option>
                                        <option value="not_applicable" <?= isset($protocol_data['pregnancy_status']) && $protocol_data['pregnancy_status'] === 'not_applicable' ? 'selected' : '' ?>>
                                            <i class="fas fa-minus-circle"></i> Not Applicable
                                        </option>
                                    </select>
                                    <small class="form-text text-muted">Important for vaccine safety considerations</small>
                                </div>
                            </div>
                        </div>

                        <!-- Medical History Card -->
                        <div class="medical-card mb-4">
                            <div class="medical-card-header">
                                <div class="medical-card-icon" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <h3 class="medical-card-title">Medical History</h3>
                            </div>
                            <div class="medical-card-body">
                                <div class="mb-3">
                                    <label for="allergies" class="form-label medical-heading-3">Known Allergies</label>
                                    <textarea class="form-control form-control-lg" id="allergies" name="allergies" rows="4" 
                                              placeholder="List any known allergies, including medications, foods, or environmental allergens..."><?= $protocol_data['allergies'] ?? 'None reported' ?></textarea>
                                    <small class="form-text text-muted">Include all known allergies to ensure safe vaccine recommendations</small>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <a href="<?= site_url('travel-medicine/create/1') ?>" class="btn btn-outline-secondary btn-lg">
                                <i class="fas fa-arrow-left me-2"></i>Previous
                            </a>
                            <button type="submit" class="btn btn-primary btn-lg">
                                Next<i class="fas fa-arrow-right ms-2"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
