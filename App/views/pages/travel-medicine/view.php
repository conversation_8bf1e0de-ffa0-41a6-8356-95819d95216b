<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h3 class="mb-0">Travel Protocol Details</h3>
                    <div>
                        <a href="<?= site_url('travel-medicine') ?>" class="btn btn-outline-secondary">Back to List</a>
                        <?php if (isset($protocol) && $protocol['status'] !== 'completed'): ?>
                            <a href="<?= site_url('travel-medicine/create') ?>" class="btn btn-primary ms-2">Continue Protocol</a>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Protocol Status Badge -->
                    <div class="mb-4">
                        <?php if (isset($protocol) && $protocol['status'] === 'completed'): ?>
                            <span class="badge bg-success">Completed</span>
                        <?php else: ?>
                            <span class="badge bg-warning">In Progress</span>
                        <?php endif; ?>
                        <span class="ms-2 text-muted">Created: <?= isset($consultation['started_at']) ? date('F j, Y, g:i a', strtotime($consultation['started_at'])) : 'N/A' ?></span>
                    </div>
                    
                    <!-- Protocol Summary -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">Travel Information</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tbody>
                                            <tr>
                                                <th scope="row" width="40%">Patient</th>
                                                <td><?= isset($consultation['patient_first_name']) ? htmlspecialchars($consultation['patient_first_name'] . ' ' . $consultation['patient_last_name']) : 'N/A' ?></td>
                                            </tr>
                                            <tr>
                                                <th scope="row">Destination</th>
                                                <td><?= isset($protocol['destination']) ? htmlspecialchars($protocol['destination']) : 'N/A' ?></td>
                                            </tr>
                                            <tr>
                                                <th scope="row">Travel Type</th>
                                                <td>
                                                    <?php 
                                                    $travel_types = [
                                                        'business' => 'Business',
                                                        'tourism' => 'Tourism',
                                                        'backpacking' => 'Backpacking',
                                                        'volunteering' => 'Volunteering',
                                                        'visiting_family' => 'Visiting Family'
                                                    ];
                                                    echo isset($protocol['travel_type']) && isset($travel_types[$protocol['travel_type']]) ? 
                                                        htmlspecialchars($travel_types[$protocol['travel_type']]) : 'N/A';
                                                    ?>
                                                </td>
                                            </tr>
                                            <tr>
                                                <th scope="row">Duration</th>
                                                <td><?= isset($protocol['duration']) ? htmlspecialchars($protocol['duration']) : 'N/A' ?></td>
                                            </tr>
                                            <tr>
                                                <th scope="row">Departure Date</th>
                                                <td><?= isset($protocol['departure_date']) ? date('F j, Y', strtotime($protocol['departure_date'])) : 'N/A' ?></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">Patient Assessment</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tbody>
                                            <tr>
                                                <th scope="row" width="40%">Age</th>
                                                <td><?= isset($protocol['age']) ? htmlspecialchars($protocol['age']) : 'N/A' ?></td>
                                            </tr>
                                            <tr>
                                                <th scope="row">Gender</th>
                                                <td><?= isset($protocol['gender']) ? ucfirst(htmlspecialchars($protocol['gender'])) : 'N/A' ?></td>
                                            </tr>
                                            <tr>
                                                <th scope="row">Pregnancy Status</th>
                                                <td>
                                                    <?php 
                                                    $pregnancy_statuses = [
                                                        'pregnant' => 'Pregnant',
                                                        'breastfeeding' => 'Breastfeeding',
                                                        'planning_pregnancy' => 'Planning Pregnancy',
                                                        'not_pregnant' => 'Not Pregnant',
                                                        'not_applicable' => 'Not Applicable'
                                                    ];
                                                    echo isset($protocol['pregnancy_status']) && isset($pregnancy_statuses[$protocol['pregnancy_status']]) ? 
                                                        htmlspecialchars($pregnancy_statuses[$protocol['pregnancy_status']]) : 'N/A';
                                                    ?>
                                                </td>
                                            </tr>
                                            <tr>
                                                <th scope="row">Allergies</th>
                                                <td><?= isset($protocol['allergies']) ? nl2br(htmlspecialchars($protocol['allergies'])) : 'None reported' ?></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Risk Assessment -->
                    <?php if (isset($protocol['risk_assessment']) && !empty($protocol['risk_assessment'])): ?>
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">Risk Assessment</h5>
                                </div>
                                <div class="card-body">
                                    <div class="risk-assessment">
                                        <?= $protocol['risk_assessment'] ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <!-- Vaccination Plan -->
                    <?php if (isset($protocol['vaccination_plan']) && !empty($protocol['vaccination_plan'])): ?>
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">Vaccination Plan</h5>
                                </div>
                                <div class="card-body">
                                    <div class="vaccination-plan">
                                        <?= $protocol['vaccination_plan'] ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <!-- Additional Notes -->
                    <?php if (isset($protocol['notes']) && !empty($protocol['notes'])): ?>
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">Additional Notes</h5>
                                </div>
                                <div class="card-body">
                                    <p><?= nl2br(htmlspecialchars($protocol['notes'])) ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
