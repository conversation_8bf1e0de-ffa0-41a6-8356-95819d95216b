<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <h1 class="text-2xl font-semibold text-gray-900">Create New Travel Medicine Consultation</h1>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">Record a new travel medicine consultation</p>
    </div>
    
    <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
        <form method="POST" action="<?= base_url('travel-medicine/store') ?>" class="space-y-8">
            <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
            <input type="hidden" name="consultation_type" value="travel_medicine">
            
            <!-- Patient Selection -->
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Basic Information</h3>
                <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="patient_id" class="block text-sm font-medium text-gray-700">Patient</label>
                        <div class="mt-1">
                            <select id="patient_id" name="patient_id" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" required>
                                <option value="">Select patient</option>
                                <?php foreach ($patients as $patient): ?>
                                    <option value="<?= e($patient['id']) ?>" <?= isset($formData['patient_id']) && $formData['patient_id'] === $patient['id'] ? 'selected' : '' ?>>
                                        <?= e($patient['first_name'] . ' ' . $patient['last_name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="sm:col-span-3">
                        <label for="started_at" class="block text-sm font-medium text-gray-700">Date & Time</label>
                        <div class="mt-1">
                            <input type="datetime-local" id="started_at" name="started_at" 
                                   value="<?= isset($formData['started_at']) ? date('Y-m-d\TH:i', strtotime($formData['started_at'])) : date('Y-m-d\TH:i') ?>" 
                                   class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" 
                                   required>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Travel Information -->
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Travel Information</h3>
                <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="destination" class="block text-sm font-medium text-gray-700">Destination Country</label>
                        <div class="mt-1">
                            <input type="text" id="destination" name="destination" 
                                   value="<?= isset($formData['destination']) ? e($formData['destination']) : '' ?>" 
                                   class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" 
                                   required>
                        </div>
                    </div>
                    
                    <div class="sm:col-span-3">
                        <label for="travel_dates" class="block text-sm font-medium text-gray-700">Travel Dates</label>
                        <div class="mt-1">
                            <input type="text" id="travel_dates" name="travel_dates" 
                                   value="<?= isset($formData['travel_dates']) ? e($formData['travel_dates']) : '' ?>" 
                                   placeholder="e.g., Jan 15 - Feb 20, 2026" 
                                   class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" 
                                   required>
                        </div>
                    </div>
                    
                    <div class="sm:col-span-6">
                        <label for="travel_purpose" class="block text-sm font-medium text-gray-700">Purpose of Travel</label>
                        <div class="mt-1">
                            <select id="travel_purpose" name="travel_purpose" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                <option value="">Select purpose</option>
                                <option value="tourism" <?= isset($formData['travel_purpose']) && $formData['travel_purpose'] === 'tourism' ? 'selected' : '' ?>>Tourism</option>
                                <option value="business" <?= isset($formData['travel_purpose']) && $formData['travel_purpose'] === 'business' ? 'selected' : '' ?>>Business</option>
                                <option value="education" <?= isset($formData['travel_purpose']) && $formData['travel_purpose'] === 'education' ? 'selected' : '' ?>>Education</option>
                                <option value="volunteer" <?= isset($formData['travel_purpose']) && $formData['travel_purpose'] === 'volunteer' ? 'selected' : '' ?>>Volunteer/Missionary</option>
                                <option value="visiting_family" <?= isset($formData['travel_purpose']) && $formData['travel_purpose'] === 'visiting_family' ? 'selected' : '' ?>>Visiting Family</option>
                                <option value="other" <?= isset($formData['travel_purpose']) && $formData['travel_purpose'] === 'other' ? 'selected' : '' ?>>Other</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="sm:col-span-6">
                        <label for="accommodation_type" class="block text-sm font-medium text-gray-700">Accommodation Type</label>
                        <div class="mt-1">
                            <select id="accommodation_type" name="accommodation_type" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                <option value="">Select accommodation</option>
                                <option value="hotel" <?= isset($formData['accommodation_type']) && $formData['accommodation_type'] === 'hotel' ? 'selected' : '' ?>>Hotel/Resort</option>
                                <option value="hostel" <?= isset($formData['accommodation_type']) && $formData['accommodation_type'] === 'hostel' ? 'selected' : '' ?>>Hostel</option>
                                <option value="family" <?= isset($formData['accommodation_type']) && $formData['accommodation_type'] === 'family' ? 'selected' : '' ?>>Family/Friends</option>
                                <option value="camping" <?= isset($formData['accommodation_type']) && $formData['accommodation_type'] === 'camping' ? 'selected' : '' ?>>Camping/Outdoor</option>
                                <option value="rural" <?= isset($formData['accommodation_type']) && $formData['accommodation_type'] === 'rural' ? 'selected' : '' ?>>Rural/Remote</option>
                                <option value="other" <?= isset($formData['accommodation_type']) && $formData['accommodation_type'] === 'other' ? 'selected' : '' ?>>Other</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Medical History -->
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Medical History</h3>
                <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-6">
                        <label for="existing_conditions" class="block text-sm font-medium text-gray-700">Existing Medical Conditions</label>
                        <div class="mt-1">
                            <textarea id="existing_conditions" name="existing_conditions" rows="3" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['existing_conditions']) ? e($formData['existing_conditions']) : '' ?></textarea>
                        </div>
                    </div>
                    
                    <div class="sm:col-span-6">
                        <label for="current_medications" class="block text-sm font-medium text-gray-700">Current Medications</label>
                        <div class="mt-1">
                            <textarea id="current_medications" name="current_medications" rows="3" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['current_medications']) ? e($formData['current_medications']) : '' ?></textarea>
                        </div>
                    </div>
                    
                    <div class="sm:col-span-6">
                        <label for="allergies" class="block text-sm font-medium text-gray-700">Allergies</label>
                        <div class="mt-1">
                            <textarea id="allergies" name="allergies" rows="2" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['allergies']) ? e($formData['allergies']) : '' ?></textarea>
                        </div>
                    </div>
                    
                    <div class="sm:col-span-6">
                        <label for="vaccination_history" class="block text-sm font-medium text-gray-700">Vaccination History</label>
                        <div class="mt-1">
                            <textarea id="vaccination_history" name="vaccination_history" rows="3" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['vaccination_history']) ? e($formData['vaccination_history']) : '' ?></textarea>
                        </div>
                        <p class="mt-2 text-sm text-gray-500">Include dates of previous travel vaccinations if known.</p>
                    </div>
                </div>
            </div>
            
            <!-- Recommendations -->
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Recommendations</h3>
                <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-6">
                        <label for="recommended_vaccinations" class="block text-sm font-medium text-gray-700">Recommended Vaccinations</label>
                        <div class="mt-1">
                            <textarea id="recommended_vaccinations" name="recommended_vaccinations" rows="4" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['recommended_vaccinations']) ? e($formData['recommended_vaccinations']) : '' ?></textarea>
                        </div>
                    </div>
                    
                    <div class="sm:col-span-6">
                        <label for="malaria_prophylaxis" class="block text-sm font-medium text-gray-700">Malaria Prophylaxis</label>
                        <div class="mt-1">
                            <textarea id="malaria_prophylaxis" name="malaria_prophylaxis" rows="3" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['malaria_prophylaxis']) ? e($formData['malaria_prophylaxis']) : '' ?></textarea>
                        </div>
                    </div>
                    
                    <div class="sm:col-span-6">
                        <label for="other_recommendations" class="block text-sm font-medium text-gray-700">Other Recommendations</label>
                        <div class="mt-1">
                            <textarea id="other_recommendations" name="other_recommendations" rows="4" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['other_recommendations']) ? e($formData['other_recommendations']) : '' ?></textarea>
                        </div>
                        <p class="mt-2 text-sm text-gray-500">Include food/water precautions, insect bite prevention, etc.</p>
                    </div>
                </div>
            </div>
            
            <!-- Notes -->
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Additional Notes</h3>
                <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-6">
                        <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                        <div class="mt-1">
                            <textarea id="notes" name="notes" rows="4" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['notes']) ? e($formData['notes']) : '' ?></textarea>
                        </div>
                    </div>
                </div>
            </div>

            <div class="pt-5">
                <div class="flex justify-end">
                    <a href="<?= base_url('travel-medicine') ?>" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Cancel
                    </a>
                    <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Create Consultation
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
