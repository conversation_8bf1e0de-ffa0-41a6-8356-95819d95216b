<!-- CSS for enhanced medical UI -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="<?= site_url('resources/css/protocol-steps.css') ?>">
<link rel="stylesheet" href="<?= site_url('resources/css/medical-ui-enhanced.css') ?>">

<div class="medical-container">
    <!-- Enhanced Header -->
    <div class="medical-card">
        <div class="medical-card-header">
            <div class="medical-card-icon" style="background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);">
                <i class="fas fa-question-circle"></i>
            </div>
            <h1 class="medical-card-title">Clinical Q&A Assistant</h1>
        </div>
        <div class="medical-card-body">
            <p class="medical-body">Ask questions about Dutch medical guidelines, protocols, and clinical decision-making. Get evidence-based answers for your practice.</p>
            <div class="d-flex justify-content-end">
                <a href="<?= base_url('clinical-qa/create') ?>" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus me-2"></i>New Q&A Session
                </a>
            </div>
        </div>
    </div>

    <!-- Quick Questions Section -->
    <div class="medical-card">
        <div class="medical-card-header">
            <div class="medical-card-icon" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);">
                <i class="fas fa-bolt"></i>
            </div>
            <h3 class="medical-card-title">Quick Questions</h3>
        </div>
        <div class="medical-card-body">
            <div class="quick-questions-grid">
                <div class="quick-question-item" onclick="askQuickQuestion('What vaccines are required for travel to Thailand?')">
                    <div class="question-icon">
                        <i class="fas fa-plane"></i>
                    </div>
                    <div class="question-text">What vaccines are required for travel to Thailand?</div>
                </div>
                <div class="quick-question-item" onclick="askQuickQuestion('STD testing protocol for symptomatic patient?')">
                    <div class="question-icon">
                        <i class="fas fa-vial"></i>
                    </div>
                    <div class="question-text">STD testing protocol for symptomatic patient?</div>
                </div>
                <div class="quick-question-item" onclick="askQuickQuestion('COVID testing guidelines for healthcare workers?')">
                    <div class="question-icon">
                        <i class="fas fa-virus"></i>
                    </div>
                    <div class="question-text">COVID testing guidelines for healthcare workers?</div>
                </div>
                <div class="quick-question-item" onclick="askQuickQuestion('Contraindications for yellow fever vaccine?')">
                    <div class="question-icon">
                        <i class="fas fa-syringe"></i>
                    </div>
                    <div class="question-text">Contraindications for yellow fever vaccine?</div>
                </div>
                <div class="quick-question-item" onclick="askQuickQuestion('Malaria prophylaxis for pregnant travelers?')">
                    <div class="question-icon">
                        <i class="fas fa-baby"></i>
                    </div>
                    <div class="question-text">Malaria prophylaxis for pregnant travelers?</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <?php if (!empty($consultations)): ?>
    <div class="medical-card">
        <div class="medical-card-header">
            <div class="medical-card-icon" style="background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);">
                <i class="fas fa-search"></i>
            </div>
            <h3 class="medical-card-title">Previous Q&A Sessions</h3>
        </div>
        <div class="medical-card-body">
            <form action="<?= base_url('clinical-qa') ?>" method="GET" class="search-form">
                <div class="search-input-group">
                    <input type="text" name="search" value="<?= htmlspecialchars($filters['search']) ?>" 
                           placeholder="Search previous questions..." class="form-input form-control-lg">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                    </button>
                    <?php if (!empty($filters['search'])): ?>
                        <a href="<?= base_url('clinical-qa') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>

    <!-- Previous Sessions -->
    <div class="medical-card">
        <div class="medical-card-header">
            <div class="medical-card-icon" style="background: linear-gradient(135deg, #198754 0%, #157347 100%);">
                <i class="fas fa-history"></i>
            </div>
            <h3 class="medical-card-title">Recent Sessions</h3>
        </div>
        <div class="medical-card-body">
            <div class="sessions-list">
                <?php foreach ($consultations as $consultation): ?>
                    <div class="session-item">
                        <div class="session-icon">
                            <i class="fas fa-comment-medical"></i>
                        </div>
                        <div class="session-content">
                            <div class="session-title">
                                <a href="<?= base_url('clinical-qa/' . $consultation['id']) ?>">
                                    Q&A Session #<?= $consultation['id'] ?>
                                </a>
                            </div>
                            <div class="session-meta">
                                <span class="session-date">
                                    <i class="fas fa-calendar me-1"></i>
                                    <?= date('M j, Y g:i A', strtotime($consultation['created_at'])) ?>
                                </span>
                                <span class="session-status status-<?= strtolower($consultation['status']) ?>">
                                    <?= ucfirst($consultation['status']) ?>
                                </span>
                            </div>
                            <?php if (!empty($consultation['notes'])): ?>
                                <div class="session-preview">
                                    <?= substr(strip_tags($consultation['notes']), 0, 150) ?>...
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="session-actions">
                            <a href="<?= base_url('clinical-qa/' . $consultation['id']) ?>" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye"></i> View
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Pagination -->
            <?php if ($pagination['totalPages'] > 1): ?>
                <div class="pagination-container">
                    <nav aria-label="Page navigation">
                        <ul class="pagination">
                            <?php if ($pagination['hasPrevPage']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="<?= base_url('clinical-qa?page=' . ($pagination['currentPage'] - 1) . (!empty($filters['search']) ? '&search=' . urlencode($filters['search']) : '')) ?>">
                                        <i class="fas fa-chevron-left"></i> Previous
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = 1; $i <= $pagination['totalPages']; $i++): ?>
                                <li class="page-item <?= $i === $pagination['currentPage'] ? 'active' : '' ?>">
                                    <a class="page-link" href="<?= base_url('clinical-qa?page=' . $i . (!empty($filters['search']) ? '&search=' . urlencode($filters['search']) : '')) ?>">
                                        <?= $i ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($pagination['hasNextPage']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="<?= base_url('clinical-qa?page=' . ($pagination['currentPage'] + 1) . (!empty($filters['search']) ? '&search=' . urlencode($filters['search']) : '')) ?>">
                                        Next <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <?php else: ?>
    <!-- Empty State -->
    <div class="medical-card">
        <div class="medical-card-body text-center">
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="fas fa-question-circle"></i>
                </div>
                <h3 class="empty-state-title">No Q&A Sessions Yet</h3>
                <p class="empty-state-description">Start by asking your first clinical question or try one of the quick questions above.</p>
                <a href="<?= base_url('clinical-qa/create') ?>" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus me-2"></i>Ask Your First Question
                </a>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<style>
/* Clinical Q&A Enhanced Styles */
.medical-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.quick-questions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.quick-question-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: linear-gradient(135deg, #f8f5ff 0%, #ffffff 100%);
    border: 2px solid #e9ecef;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quick-question-item:hover {
    border-color: #6f42c1;
    box-shadow: 0 4px 12px rgba(111, 66, 193, 0.15);
    transform: translateY(-2px);
}

.question-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.question-text {
    color: #2c3e50;
    font-weight: 500;
    line-height: 1.4;
}

.search-form {
    margin-bottom: 1.5rem;
}

.search-input-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.search-input-group .form-input {
    flex: 1;
}

.sessions-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.session-item {
    display: flex;
    align-items: flex-start;
    padding: 1.5rem;
    background: rgba(0,0,0,0.02);
    border-radius: 12px;
    border: 2px solid #e9ecef;
    transition: all 0.2s ease;
}

.session-item:hover {
    border-color: #198754;
    box-shadow: 0 4px 12px rgba(25, 135, 84, 0.1);
}

.session-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #198754 0%, #157347 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.session-content {
    flex: 1;
}

.session-title a {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1.1rem;
    text-decoration: none;
}

.session-title a:hover {
    color: #198754;
}

.session-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 0.5rem 0;
    font-size: 0.9rem;
    color: #6c757d;
}

.session-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-completed {
    background: #d1e7dd;
    color: #0f5132;
}

.status-pending {
    background: #fff3cd;
    color: #664d03;
}

.status-in_progress {
    background: #cff4fc;
    color: #055160;
}

.session-preview {
    color: #495057;
    font-size: 0.95rem;
    line-height: 1.4;
    margin-top: 0.5rem;
}

.session-actions {
    margin-left: 1rem;
}

.empty-state {
    padding: 3rem 2rem;
}

.empty-state-icon {
    font-size: 4rem;
    color: #6c757d;
    margin-bottom: 1.5rem;
}

.empty-state-title {
    color: #2c3e50;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.empty-state-description {
    color: #6c757d;
    font-size: 1.1rem;
    margin-bottom: 2rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.pagination-container {
    margin-top: 2rem;
    display: flex;
    justify-content: center;
}

.pagination {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 0.25rem;
}

.page-item {
    display: flex;
}

.page-link {
    padding: 0.75rem 1rem;
    color: #6c757d;
    text-decoration: none;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.page-link:hover {
    background: #e9ecef;
    color: #495057;
}

.page-item.active .page-link {
    background: #6f42c1;
    color: white;
    border-color: #6f42c1;
}

@media (max-width: 768px) {
    .medical-container {
        padding: 1rem;
    }
    
    .quick-questions-grid {
        grid-template-columns: 1fr;
    }
    
    .session-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .session-icon {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }
    
    .session-actions {
        margin-left: 0;
    }
    
    .search-input-group {
        flex-direction: column;
    }
    
    .search-input-group .form-input {
        margin-bottom: 0.5rem;
    }
}
</style>

<script>
function askQuickQuestion(question) {
    // Redirect to create page with pre-filled question
    const encodedQuestion = encodeURIComponent(question);
    window.location.href = `<?= base_url('clinical-qa/create') ?>?question=${encodedQuestion}`;
}

document.addEventListener('DOMContentLoaded', function() {
    // Add smooth scrolling for pagination
    const pageLinks = document.querySelectorAll('.page-link');
    pageLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Add loading state
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        });
    });
});
</script>