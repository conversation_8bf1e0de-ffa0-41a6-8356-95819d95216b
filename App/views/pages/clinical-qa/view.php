<!-- CSS for enhanced medical UI -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="<?= site_url('resources/css/protocol-steps.css') ?>">
<link rel="stylesheet" href="<?= site_url('resources/css/medical-ui-enhanced.css') ?>">

<div class="medical-container">
    <!-- Enhanced Header -->
    <div class="medical-card">
        <div class="medical-card-header">
            <div class="medical-card-icon" style="background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);">
                <i class="fas fa-eye"></i>
            </div>
            <h1 class="medical-card-title">Q&A Session Details</h1>
        </div>
        <div class="medical-card-body">
            <div class="session-meta-info">
                <div class="meta-item">
                    <div class="meta-label">
                        <i class="fas fa-hashtag text-primary me-2"></i>
                        <strong>Session ID:</strong>
                    </div>
                    <div class="meta-value">#<?= $consultation['id'] ?></div>
                </div>
                <div class="meta-item">
                    <div class="meta-label">
                        <i class="fas fa-calendar text-info me-2"></i>
                        <strong>Created:</strong>
                    </div>
                    <div class="meta-value"><?= date('F j, Y g:i A', strtotime($consultation['created_at'])) ?></div>
                </div>
                <div class="meta-item">
                    <div class="meta-label">
                        <i class="fas fa-flag text-success me-2"></i>
                        <strong>Status:</strong>
                    </div>
                    <div class="meta-value">
                        <span class="status-badge status-<?= strtolower($consultation['status']) ?>">
                            <?= ucfirst($consultation['status']) ?>
                        </span>
                    </div>
                </div>
            </div>
            <div class="d-flex justify-content-start gap-2 mt-3">
                <a href="<?= base_url('clinical-qa') ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Q&A
                </a>
                <button onclick="printSession()" class="btn btn-outline-primary">
                    <i class="fas fa-print me-2"></i>Print Session
                </button>
            </div>
        </div>
    </div>

    <!-- Session Content -->
    <div class="medical-card">
        <div class="medical-card-header">
            <div class="medical-card-icon" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);">
                <i class="fas fa-comments"></i>
            </div>
            <h3 class="medical-card-title">Q&A Conversation</h3>
        </div>
        <div class="medical-card-body">
            <?php 
            $notesData = null;
            $aiResponse = null;
            
            // Parse notes JSON
            if (!empty($consultation['notes'])) {
                $notesData = json_decode($consultation['notes'], true);
            }
            
            // Parse AI recommendations JSON
            if (!empty($consultation['ai_recommendations'])) {
                $aiResponse = json_decode($consultation['ai_recommendations'], true);
            }
            
            if ($notesData || $aiResponse): ?>
                <div class="qa-conversation">
                    <?php if ($notesData && isset($notesData['question'])): ?>
                        <!-- User Question -->
                        <div class="message user-message">
                            <div class="message-avatar">
                                <i class="fas fa-user-md"></i>
                            </div>
                            <div class="message-content">
                                <div class="message-text">
                                    <?= nl2br(htmlspecialchars($notesData['question'])) ?>
                                </div>
                                <div class="message-time">
                                    <?= date('g:i A', strtotime($consultation['created_at'])) ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($aiResponse && isset($aiResponse['answer'])): ?>
                        <!-- AI Response -->
                        <div class="message assistant-message">
                            <div class="message-avatar">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="message-content">
                                <div class="message-text">
                                    <?= nl2br(htmlspecialchars($aiResponse['answer'])) ?>
                                </div>
                                <div class="message-meta">
                                    <div class="message-time">
                                        <?= isset($aiResponse['timestamp']) ? date('g:i A', strtotime($aiResponse['timestamp'])) : date('g:i A', strtotime($consultation['created_at'])) ?>
                                    </div>
                                    <?php if (isset($aiResponse['model_used']) && $aiResponse['model_used'] !== 'fallback'): ?>
                                        <div class="message-model">
                                            <i class="fas fa-microchip me-1"></i>
                                            <?= htmlspecialchars($aiResponse['model_used']) ?>
                                        </div>
                                    <?php endif; ?>
                                    <?php if (isset($aiResponse['sources']) && !empty($aiResponse['sources'])): ?>
                                        <div class="message-sources">
                                            <i class="fas fa-book me-1"></i>
                                            Sources: <?= implode(', ', $aiResponse['sources']) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            <?php else: ?>
                <div class="empty-conversation">
                    <div class="empty-conversation-icon">
                        <i class="fas fa-comment-slash"></i>
                    </div>
                    <h4>No Conversation Content</h4>
                    <p>This Q&A session doesn't have any recorded conversation yet.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Patient Information (if available) -->
    <?php if (!empty($consultation['patient_id'])): ?>
    <div class="medical-card">
        <div class="medical-card-header">
            <div class="medical-card-icon" style="background: linear-gradient(135deg, #198754 0%, #157347 100%);">
                <i class="fas fa-user-md"></i>
            </div>
            <h3 class="medical-card-title">Patient Information</h3>
        </div>
        <div class="medical-card-body">
            <div class="patient-info">
                <div class="info-item">
                    <div class="info-label">
                        <i class="fas fa-id-badge text-primary me-2"></i>
                        <strong>Patient ID:</strong>
                    </div>
                    <div class="info-value"><?= $consultation['patient_id'] ?></div>
                </div>
                <!-- Add more patient details if available in the consultation data -->
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Guidelines Reference -->
    <div class="medical-card">
        <div class="medical-card-header">
            <div class="medical-card-icon" style="background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);">
                <i class="fas fa-book-medical"></i>
            </div>
            <h3 class="medical-card-title">Guidelines Referenced</h3>
        </div>
        <div class="medical-card-body">
            <div class="guidelines-reference">
                <div class="reference-item">
                    <div class="reference-icon lcr">
                        <i class="fas fa-plane"></i>
                    </div>
                    <div class="reference-content">
                        <h5>LCR Guidelines</h5>
                        <p>Travel medicine and vaccination protocols</p>
                    </div>
                </div>
                <div class="reference-item">
                    <div class="reference-icon rivm">
                        <i class="fas fa-virus"></i>
                    </div>
                    <div class="reference-content">
                        <h5>RIVM Standards</h5>
                        <p>Infectious disease prevention and control</p>
                    </div>
                </div>
                <div class="reference-item">
                    <div class="reference-icon nnaac">
                        <i class="fas fa-vial"></i>
                    </div>
                    <div class="reference-content">
                        <h5>NNAAC Protocols</h5>
                        <p>STD testing and treatment guidelines</p>
                    </div>
                </div>
                <div class="reference-item">
                    <div class="reference-icon nhg">
                        <i class="fas fa-stethoscope"></i>
                    </div>
                    <div class="reference-content">
                        <h5>NHG Standards</h5>
                        <p>General practice clinical guidelines</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Session Actions -->
    <div class="medical-card">
        <div class="medical-card-header">
            <div class="medical-card-icon" style="background: linear-gradient(135deg, #e83e8c 0%, #d91a72 100%);">
                <i class="fas fa-cogs"></i>
            </div>
            <h3 class="medical-card-title">Session Actions</h3>
        </div>
        <div class="medical-card-body">
            <div class="action-buttons">
                <a href="<?= base_url('clinical-qa/create') ?>" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus me-2"></i>Start New Q&A Session
                </a>
                <button onclick="shareSession()" class="btn btn-outline-info btn-lg">
                    <i class="fas fa-share me-2"></i>Share Session
                </button>
                <button onclick="exportSession()" class="btn btn-outline-success btn-lg">
                    <i class="fas fa-download me-2"></i>Export Session
                </button>
            </div>
        </div>
    </div>

    <!-- Disclaimer -->
    <div class="medical-card disclaimer-card">
        <div class="medical-card-body">
            <div class="disclaimer-content">
                <div class="disclaimer-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="disclaimer-text">
                    <strong>Important:</strong> All AI responses are based on Dutch medical guidelines but should not replace clinical judgment. This session is for reference purposes only and should be used to supplement professional medical decision-making.
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Clinical Q&A View Page Styles */
.medical-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem;
}

.session-meta-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.meta-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
    background: rgba(0,0,0,0.02);
    border-radius: 8px;
    border-left: 4px solid #dee2e6;
}

.meta-label {
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
}

.meta-value {
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 500;
}

.status-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-completed {
    background: #d1e7dd;
    color: #0f5132;
}

.status-pending {
    background: #fff3cd;
    color: #664d03;
}

.status-in_progress {
    background: #cff4fc;
    color: #055160;
}

.qa-conversation {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 2rem;
    border: 2px solid #e9ecef;
}

.message {
    display: flex;
    margin-bottom: 2rem;
    animation: fadeInUp 0.3s ease;
}

.message-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
    font-size: 1.2rem;
}

.assistant-message .message-avatar {
    background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
    color: white;
}

.user-message {
    flex-direction: row-reverse;
}

.user-message .message-avatar {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    color: white;
    margin-right: 0;
    margin-left: 1rem;
}

.message-content {
    flex: 1;
    max-width: 75%;
}

.user-message .message-content {
    text-align: right;
}

.message-text {
    background: white;
    padding: 1.25rem 1.5rem;
    border-radius: 18px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    color: #2c3e50;
    line-height: 1.6;
    margin-bottom: 0.75rem;
    font-size: 1.05rem;
}

.user-message .message-text {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    color: white;
}

.message-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    font-size: 0.8rem;
    color: #6c757d;
    padding: 0 0.5rem;
}

.user-message .message-meta {
    justify-content: flex-end;
}

.message-time, .message-model, .message-sources {
    display: flex;
    align-items: center;
}

.message-sources {
    font-weight: 500;
    color: #198754;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.empty-conversation {
    text-align: center;
    padding: 3rem 2rem;
    color: #6c757d;
}

.empty-conversation-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-conversation h4 {
    color: #495057;
    margin-bottom: 0.5rem;
}

.patient-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
    background: rgba(0,0,0,0.02);
    border-radius: 8px;
    border-left: 4px solid #198754;
}

.info-label {
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
}

.info-value {
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 500;
}

.guidelines-reference {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1rem;
}

.reference-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: rgba(0,0,0,0.02);
    border-radius: 12px;
    border: 2px solid #e9ecef;
    transition: all 0.2s ease;
}

.reference-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.reference-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.reference-icon.lcr {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.reference-icon.rivm {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.reference-icon.nnaac {
    background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);
}

.reference-icon.nhg {
    background: linear-gradient(135deg, #198754 0%, #157347 100%);
}

.reference-content h5 {
    color: #2c3e50;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.reference-content p {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
    line-height: 1.3;
}

.action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
}

.disclaimer-card {
    background: linear-gradient(135deg, #fff3cd 0%, #ffffff 100%);
    border: 2px solid #ffc107;
}

.disclaimer-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.disclaimer-icon {
    color: #856404;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.disclaimer-text {
    color: #856404;
    font-size: 0.95rem;
    line-height: 1.4;
}

@media (max-width: 768px) {
    .medical-container {
        padding: 1rem;
    }
    
    .session-meta-info {
        grid-template-columns: 1fr;
    }
    
    .guidelines-reference {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .action-buttons .btn {
        width: 100%;
    }
    
    .disclaimer-content {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }
    
    .conversation-content {
        padding: 1.5rem;
    }
}

@media print {
    .btn, .action-buttons {
        display: none !important;
    }
    
    .medical-card {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }
    
    .disclaimer-card {
        background: #fff !important;
        border: 1px solid #000 !important;
    }
}
</style>

<script>
function printSession() {
    window.print();
}

function shareSession() {
    if (navigator.share) {
        navigator.share({
            title: 'Clinical Q&A Session #<?= $consultation["id"] ?>',
            text: 'Clinical Q&A Session from <?= date("F j, Y", strtotime($consultation["created_at"])) ?>',
            url: window.location.href
        }).catch(console.error);
    } else {
        // Fallback: copy URL to clipboard
        navigator.clipboard.writeText(window.location.href).then(() => {
            alert('Session URL copied to clipboard!');
        }).catch(() => {
            alert('Unable to share. Please copy the URL manually.');
        });
    }
}

function exportSession() {
    const sessionData = {
        id: '<?= $consultation["id"] ?>',
        created_at: '<?= $consultation["created_at"] ?>',
        status: '<?= $consultation["status"] ?>',
        notes: <?= json_encode($consultation["notes"]) ?>,
        patient_id: '<?= $consultation["patient_id"] ?? "" ?>'
    };
    
    const dataStr = JSON.stringify(sessionData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `clinical-qa-session-${sessionData.id}.json`;
    link.click();
}

document.addEventListener('DOMContentLoaded', function() {
    // Add smooth scrolling for internal links
    const internalLinks = document.querySelectorAll('a[href^="#"]');
    internalLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });
});
</script>