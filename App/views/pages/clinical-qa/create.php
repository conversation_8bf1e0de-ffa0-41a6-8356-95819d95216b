<!-- CSS for enhanced medical UI -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="<?= site_url('resources/css/protocol-steps.css') ?>">
<link rel="stylesheet" href="<?= site_url('resources/css/medical-ui-enhanced.css') ?>">

<div class="medical-container">
    <!-- Enhanced Header -->
    <div class="medical-card">
        <div class="medical-card-header">
            <div class="medical-card-icon" style="background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);">
                <i class="fas fa-robot"></i>
            </div>
            <h1 class="medical-card-title">Clinical Q&A Assistant</h1>
        </div>
        <div class="medical-card-body">
            <p class="medical-body">Ask questions about Dutch medical guidelines, protocols, guidelines, or clinical decision-making. I follow LCR, RIVM, NNAAC, and NHG standards.</p>
            <div class="d-flex justify-content-start">
                <a href="<?= base_url('clinical-qa') ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Q&A
                </a>
            </div>
        </div>
    </div>

    <!-- Chat Interface -->
    <div class="medical-card chat-container">
        <div class="medical-card-header">
            <div class="medical-card-icon" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);">
                <i class="fas fa-comments"></i>
            </div>
            <h3 class="medical-card-title">Ask Your Question</h3>
        </div>
        <div class="medical-card-body">
            <!-- Chat Messages Area -->
            <div class="chat-messages" id="chatMessages">
                <!-- Welcome Message -->
                <div class="message assistant-message">
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <div class="message-text">
                            Hello! I'm your AI clinical assistant. Ask me any questions about Dutch medical protocols, guidelines, or clinical decision-making. I follow LCR, RIVM, NNAAC, and NHG standards.
                        </div>
                        <div class="message-time">
                            <?= date('g:i A') ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chat Input -->
            <div class="chat-input-container">
                <form id="chatForm" class="chat-form">
                    <div class="chat-input-group">
                        <textarea id="questionInput" 
                                  placeholder="Type your clinical question here..." 
                                  class="chat-input"
                                  rows="3"
                                  required></textarea>
                        <button type="submit" class="chat-send-btn" id="sendBtn">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Quick Guidelines Reference -->
    <div class="medical-card">
        <div class="medical-card-header">
            <div class="medical-card-icon" style="background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);">
                <i class="fas fa-book-medical"></i>
            </div>
            <h3 class="medical-card-title">Quick Guidelines Reference</h3>
        </div>
        <div class="medical-card-body">
            <div class="guidelines-grid">
                <div class="guideline-item">
                    <div class="guideline-icon lcr">
                        <i class="fas fa-plane"></i>
                    </div>
                    <div class="guideline-content">
                        <h4>LCR Guidelines</h4>
                        <p>Travel medicine and vaccination protocols</p>
                    </div>
                </div>
                <div class="guideline-item">
                    <div class="guideline-icon rivm">
                        <i class="fas fa-virus"></i>
                    </div>
                    <div class="guideline-content">
                        <h4>RIVM Standards</h4>
                        <p>Infectious disease prevention and control</p>
                    </div>
                </div>
                <div class="guideline-item">
                    <div class="guideline-icon nnaac">
                        <i class="fas fa-vial"></i>
                    </div>
                    <div class="guideline-content">
                        <h4>NNAAC Protocols</h4>
                        <p>STD testing and treatment guidelines</p>
                    </div>
                </div>
                <div class="guideline-item">
                    <div class="guideline-icon nhg">
                        <i class="fas fa-stethoscope"></i>
                    </div>
                    <div class="guideline-content">
                        <h4>NHG Standards</h4>
                        <p>General practice clinical guidelines</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Disclaimer -->
    <div class="medical-card disclaimer-card">
        <div class="medical-card-body">
            <div class="disclaimer-content">
                <div class="disclaimer-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="disclaimer-text">
                    <strong>AI responses are based on Dutch medical guidelines but should not replace clinical judgment.</strong>
                    All responses are based on current medical guidelines and should be used as reference only.
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Clinical Q&A Create Page Styles */
.medical-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem;
}

.chat-container {
    min-height: 600px;
    display: flex;
    flex-direction: column;
}

.chat-messages {
    flex: 1;
    max-height: 500px;
    overflow-y: auto;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 12px;
    margin-bottom: 1rem;
}

.message {
    display: flex;
    margin-bottom: 1.5rem;
    animation: fadeInUp 0.3s ease;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.assistant-message .message-avatar {
    background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
    color: white;
}

.user-message {
    flex-direction: row-reverse;
}

.user-message .message-avatar {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    color: white;
    margin-right: 0;
    margin-left: 1rem;
}

.message-content {
    flex: 1;
    max-width: 70%;
}

.user-message .message-content {
    text-align: right;
}

.message-text {
    background: white;
    padding: 1rem 1.25rem;
    border-radius: 18px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    color: #2c3e50;
    line-height: 1.5;
    margin-bottom: 0.5rem;
}

.user-message .message-text {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    color: white;
}

.message-time {
    font-size: 0.75rem;
    color: #6c757d;
    padding: 0 0.5rem;
}

.chat-input-container {
    border-top: 2px solid #e9ecef;
    padding-top: 1rem;
}

.chat-input-group {
    display: flex;
    gap: 1rem;
    align-items: flex-end;
}

.chat-input {
    flex: 1;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1rem;
    font-size: 1rem;
    resize: vertical;
    min-height: 60px;
    max-height: 120px;
    transition: all 0.2s ease;
}

.chat-input:focus {
    border-color: #6f42c1;
    box-shadow: 0 0 0 3px rgba(111, 66, 193, 0.1);
    outline: none;
}

.chat-send-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.chat-send-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(111, 66, 193, 0.3);
}

.chat-send-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.guidelines-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.guideline-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: rgba(0,0,0,0.02);
    border-radius: 12px;
    border: 2px solid #e9ecef;
    transition: all 0.2s ease;
}

.guideline-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.guideline-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.guideline-icon.lcr {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.guideline-icon.rivm {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.guideline-icon.nnaac {
    background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);
}

.guideline-icon.nhg {
    background: linear-gradient(135deg, #198754 0%, #157347 100%);
}

.guideline-content h4 {
    color: #2c3e50;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.guideline-content p {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
    line-height: 1.3;
}

.disclaimer-card {
    background: linear-gradient(135deg, #fff3cd 0%, #ffffff 100%);
    border: 2px solid #ffc107;
}

.disclaimer-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.disclaimer-icon {
    color: #856404;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.disclaimer-text {
    color: #856404;
    font-size: 0.95rem;
    line-height: 1.4;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.25rem;
    background: white;
    border-radius: 18px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.typing-dots {
    display: flex;
    gap: 0.25rem;
}

.typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #6c757d;
    animation: typingDot 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typingDot {
    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media (max-width: 768px) {
    .medical-container {
        padding: 1rem;
    }
    
    .guidelines-grid {
        grid-template-columns: 1fr;
    }
    
    .message-content {
        max-width: 85%;
    }
    
    .chat-input-group {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .chat-send-btn {
        align-self: flex-end;
    }
    
    .disclaimer-content {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const chatForm = document.getElementById('chatForm');
    const questionInput = document.getElementById('questionInput');
    const sendBtn = document.getElementById('sendBtn');
    const chatMessages = document.getElementById('chatMessages');
    
    // Check for pre-filled question from URL
    const urlParams = new URLSearchParams(window.location.search);
    const prefilledQuestion = urlParams.get('question');
    if (prefilledQuestion) {
        questionInput.value = decodeURIComponent(prefilledQuestion);
        questionInput.focus();
    }
    
    // Handle form submission
    chatForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const question = questionInput.value.trim();
        if (!question) return;
        
        // Add user message
        addMessage(question, 'user');
        
        // Clear input and disable send button
        questionInput.value = '';
        sendBtn.disabled = true;
        sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        
        // Show typing indicator
        showTypingIndicator();
        
        // Make actual API call to backend
        fetch('<?= base_url('clinical-qa/ajax-ask') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                question: question
            })
        })
        .then(response => response.json())
        .then(data => {
            hideTypingIndicator();
            
            if (data.success && data.response) {
                addMessage(data.response.answer, 'assistant');
            } else if (data.fallback) {
                addMessage(data.fallback, 'assistant');
            } else {
                addMessage('I apologize, but I encountered an error processing your question. Please try again or consult the relevant medical guidelines directly.', 'assistant');
            }
            
            // Re-enable send button
            sendBtn.disabled = false;
            sendBtn.innerHTML = '<i class="fas fa-paper-plane"></i>';
            questionInput.focus();
        })
        .catch(error => {
            console.error('Error:', error);
            hideTypingIndicator();
            
            // Show fallback response
            const fallbackResponse = generateMockResponse(question);
            addMessage(fallbackResponse, 'assistant');
            
            // Re-enable send button
            sendBtn.disabled = false;
            sendBtn.innerHTML = '<i class="fas fa-paper-plane"></i>';
            questionInput.focus();
        });
    });
    
    // Handle Enter key (Shift+Enter for new line)
    questionInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            chatForm.dispatchEvent(new Event('submit'));
        }
    });
    
    function addMessage(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;
        
        const currentTime = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        
        messageDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-${sender === 'user' ? 'user' : 'robot'}"></i>
            </div>
            <div class="message-content">
                <div class="message-text">${text}</div>
                <div class="message-time">${currentTime}</div>
            </div>
        `;
        
        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    function showTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message assistant-message typing-message';
        typingDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="message-content">
                <div class="typing-indicator">
                    <span>AI is thinking</span>
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            </div>
        `;
        
        chatMessages.appendChild(typingDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    function hideTypingIndicator() {
        const typingMessage = chatMessages.querySelector('.typing-message');
        if (typingMessage) {
            typingMessage.remove();
        }
    }
    
    function generateMockResponse(question) {
        const lowerQuestion = question.toLowerCase();
        
        if (lowerQuestion.includes('travel') || lowerQuestion.includes('vaccine') || lowerQuestion.includes('thailand')) {
            return `Based on LCR guidelines for travel to Thailand:

**Required Vaccinations:**
- Hepatitis A: Recommended for all travelers
- Typhoid: Recommended for most travelers
- Japanese Encephalitis: Consider for rural areas during transmission season

**Malaria Prevention:**
- Low risk in most tourist areas
- Consider prophylaxis for rural/border areas
- Use DEET-containing repellent and bed nets

**Additional Recommendations:**
- Ensure routine vaccinations are up to date
- Consider Hepatitis B for longer stays
- Traveler's diarrhea prevention measures

*Reference: LCR Travel Health Guidelines 2024*`;
        }
        
        if (lowerQuestion.includes('std') || lowerQuestion.includes('chlamydia') || lowerQuestion.includes('gonorrhea')) {
            return `According to NNAAC STD testing protocols:

**For Symptomatic Patients:**
- Chlamydia/Gonorrhea NAAT (urine or swab)
- Consider additional testing based on exposure history
- HIV and Syphilis screening recommended

**Testing Windows:**
- Chlamydia/Gonorrhea: 1-2 weeks post-exposure
- HIV: 4th generation test at 4-6 weeks
- Syphilis: 3-6 weeks post-exposure

**Treatment:**
- Follow current NNAAC treatment guidelines
- Partner notification and treatment essential
- Follow-up testing as indicated

*Reference: NNAAC STD Guidelines 2024*`;
        }
        
        if (lowerQuestion.includes('covid') || lowerQuestion.includes('coronavirus')) {
            return `Current RIVM guidelines for COVID-19 in healthcare settings:

**Healthcare Worker Testing:**
- Test if symptomatic regardless of vaccination status
- Consider testing for high-risk patient contact
- Use rapid antigen or PCR based on clinical situation

**Isolation Recommendations:**
- Isolate until fever-free for 24 hours
- Minimum 5 days from symptom onset
- Return to work when clinically recovered

**Prevention Measures:**
- Standard precautions for all patient care
- Enhanced PPE for aerosol-generating procedures
- Regular hand hygiene and surface disinfection

*Reference: RIVM COVID-19 Guidelines, Updated 2024*`;
        }
        
        // Default response
        return `Thank you for your question. Based on current Dutch medical guidelines:

I'd be happy to help you with specific clinical questions. For the most accurate and up-to-date information, please:

1. **Specify the clinical scenario** - patient demographics, symptoms, or situation
2. **Indicate which guidelines** you'd like me to reference (LCR, RIVM, NNAAC, NHG)
3. **Provide relevant context** - urgency, patient history, or specific concerns

This will help me provide more targeted guidance based on the appropriate Dutch medical standards.

*Please remember: AI responses should supplement, not replace, clinical judgment.*`;
    }
});
</script>