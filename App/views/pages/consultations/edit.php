<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <h1 class="text-2xl font-semibold text-gray-900">Edit Consultation</h1>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">Update consultation details</p>
    </div>
    
    <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
        <form method="POST" action="<?= base_url('consultations/update/' . $consultation['id']) ?>" class="space-y-8">
            <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
            
            <!-- Patient and Doctor Selection -->
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Basic Information</h3>
                <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="patient_id" class="block text-sm font-medium text-gray-700">Patient</label>
                        <div class="mt-1">
                            <select id="patient_id" name="patient_id" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" required>
                                <option value="">Select patient</option>
                                <?php foreach ($patients as $patient): ?>
                                    <option value="<?= e($patient['id']) ?>" <?= isset($formData['patient_id']) && $formData['patient_id'] === $patient['id'] ? 'selected' : '' ?>>
                                        <?= e($patient['first_name'] . ' ' . $patient['last_name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="sm:col-span-3">
                        <label for="doctor_id" class="block text-sm font-medium text-gray-700">Doctor</label>
                        <div class="mt-1">
                            <select id="doctor_id" name="doctor_id" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" required>
                                <option value="">Select doctor</option>
                                <?php foreach ($doctors as $doctor): ?>
                                    <option value="<?= e($doctor['id']) ?>" <?= isset($formData['doctor_id']) && $formData['doctor_id'] === $doctor['id'] ? 'selected' : '' ?>>
                                        <?= e($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="sm:col-span-3">
                        <label for="started_at" class="block text-sm font-medium text-gray-700">Date & Time</label>
                        <div class="mt-1">
                            <input type="datetime-local" id="started_at" name="started_at" 
                                   value="<?= isset($formData['started_at']) ? date('Y-m-d\TH:i', strtotime($formData['started_at'])) : '' ?>" 
                                   class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" 
                                   required>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Chief Complaint -->
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Chief Complaint</h3>
                <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-6">
                        <label for="chief_complaint" class="block text-sm font-medium text-gray-700">Chief Complaint</label>
                        <div class="mt-1">
                            <textarea id="chief_complaint" name="chief_complaint" rows="3" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" required><?= isset($formData['chief_complaint']) ? e($formData['chief_complaint']) : '' ?></textarea>
                        </div>
                        <p class="mt-2 text-sm text-gray-500">Patient's primary reason for visit, in their own words.</p>
                    </div>
                </div>
            </div>
            
            <!-- History -->
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">History</h3>
                <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-6">
                        <label for="history_of_present_illness" class="block text-sm font-medium text-gray-700">History of Present Illness</label>
                        <div class="mt-1">
                            <textarea id="history_of_present_illness" name="history_of_present_illness" rows="4" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['history_of_present_illness']) ? e($formData['history_of_present_illness']) : '' ?></textarea>
                        </div>
                        <p class="mt-2 text-sm text-gray-500">Detailed chronological description of the development of the patient's illness.</p>
                    </div>
                    
                    <div class="sm:col-span-6">
                        <label for="past_medical_history" class="block text-sm font-medium text-gray-700">Past Medical History</label>
                        <div class="mt-1">
                            <textarea id="past_medical_history" name="past_medical_history" rows="4" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['past_medical_history']) ? e($formData['past_medical_history']) : '' ?></textarea>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Medications and Allergies -->
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Medications and Allergies</h3>
                <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="medications" class="block text-sm font-medium text-gray-700">Current Medications</label>
                        <div class="mt-1">
                            <textarea id="medications" name="medications" rows="4" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['medications']) ? e($formData['medications']) : '' ?></textarea>
                        </div>
                    </div>
                    
                    <div class="sm:col-span-3">
                        <label for="allergies" class="block text-sm font-medium text-gray-700">Allergies</label>
                        <div class="mt-1">
                            <textarea id="allergies" name="allergies" rows="4" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['allergies']) ? e($formData['allergies']) : '' ?></textarea>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Physical Examination -->
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Physical Examination</h3>
                <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-6">
                        <label for="physical_examination" class="block text-sm font-medium text-gray-700">Physical Examination Findings</label>
                        <div class="mt-1">
                            <textarea id="physical_examination" name="physical_examination" rows="6" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['physical_examination']) ? e($formData['physical_examination']) : '' ?></textarea>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Assessment and Plan -->
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Assessment and Plan</h3>
                <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-6">
                        <label for="assessment" class="block text-sm font-medium text-gray-700">Assessment</label>
                        <div class="mt-1">
                            <textarea id="assessment" name="assessment" rows="4" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['assessment']) ? e($formData['assessment']) : '' ?></textarea>
                        </div>
                        <p class="mt-2 text-sm text-gray-500">Your assessment of the patient's condition.</p>
                    </div>
                    
                    <div class="sm:col-span-6">
                        <label for="diagnosis" class="block text-sm font-medium text-gray-700">Diagnosis</label>
                        <div class="mt-1">
                            <textarea id="diagnosis" name="diagnosis" rows="2" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['diagnosis']) ? e($formData['diagnosis']) : '' ?></textarea>
                        </div>
                    </div>
                    
                    <div class="sm:col-span-6">
                        <label for="treatment_plan" class="block text-sm font-medium text-gray-700">Treatment Plan</label>
                        <div class="mt-1">
                            <textarea id="treatment_plan" name="treatment_plan" rows="4" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['treatment_plan']) ? e($formData['treatment_plan']) : '' ?></textarea>
                        </div>
                    </div>
                    
                    <div class="sm:col-span-6">
                        <label for="follow_up" class="block text-sm font-medium text-gray-700">Follow-up Instructions</label>
                        <div class="mt-1">
                            <textarea id="follow_up" name="follow_up" rows="2" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['follow_up']) ? e($formData['follow_up']) : '' ?></textarea>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Additional Notes -->
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Additional Notes</h3>
                <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-6">
                        <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                        <div class="mt-1">
                            <textarea id="notes" name="notes" rows="4" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['notes']) ? e($formData['notes']) : '' ?></textarea>
                        </div>
                        <p class="mt-2 text-sm text-gray-500">Any additional notes or information not covered above.</p>
                    </div>
                </div>
            </div>

            <div class="pt-5">
                <div class="flex justify-end">
                    <a href="<?= base_url('consultations/view/' . $consultation['id']) ?>" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Cancel
                    </a>
                    <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Update Consultation
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
