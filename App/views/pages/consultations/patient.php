<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">Patient Consultations</h1>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">
                Medical history for <?= e($patient['first_name'] . ' ' . $patient['last_name']) ?>
            </p>
        </div>
        <div class="flex space-x-2">
            <a href="<?= base_url('patients/view/' . e($patient['id'])) ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Patient
            </a>
            <a href="<?= base_url('consultations/create?patient_id=' . e($patient['id'])) ?>" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4" />
                </svg>
                New Consultation
            </a>
        </div>
    </div>
    
    <!-- Patient Summary -->
    <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div class="bg-gray-50 px-4 py-5 sm:rounded-lg">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Patient Information</h3>
                <dl class="mt-2 grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500">Name</dt>
                        <dd class="mt-1 text-sm text-gray-900"><?= e($patient['first_name'] . ' ' . $patient['last_name']) ?></dd>
                    </div>
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500">Date of Birth</dt>
                        <dd class="mt-1 text-sm text-gray-900"><?= e(date('M d, Y', strtotime($patient['date_of_birth']))) ?></dd>
                    </div>
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500">Age</dt>
                        <dd class="mt-1 text-sm text-gray-900"><?= e(calculateAge($patient['date_of_birth'])) ?></dd>
                    </div>
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500">Gender</dt>
                        <dd class="mt-1 text-sm text-gray-900"><?= e(ucfirst($patient['gender'])) ?></dd>
                    </div>
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500">Phone</dt>
                        <dd class="mt-1 text-sm text-gray-900"><?= e($patient['phone']) ?></dd>
                    </div>
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500">Email</dt>
                        <dd class="mt-1 text-sm text-gray-900"><?= e($patient['email']) ?></dd>
                    </div>
                </dl>
            </div>
            
            <div class="bg-gray-50 px-4 py-5 sm:rounded-lg">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Medical Summary</h3>
                <dl class="mt-2 grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-1">
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500">Total Consultations</dt>
                        <dd class="mt-1 text-sm text-gray-900"><?= e(count($consultations)) ?></dd>
                    </div>
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500">Last Consultation</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <?= !empty($consultations) && isset($consultations[0]['started_at']) ? e(date('M d, Y', strtotime($consultations[0]['started_at']))) : 'None' ?>
                        </dd>
                    </div>
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500">Common Diagnoses</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <?php
                            $diagnoses = [];
                            foreach ($consultations as $consultation) {
                                if (!empty($consultation['diagnosis'])) {
                                    $diagnoses[] = $consultation['diagnosis'];
                                }
                            }
                            echo !empty($diagnoses) ? e(implode(', ', array_slice($diagnoses, 0, 3))) : 'None';
                            ?>
                        </dd>
                    </div>
                </dl>
            </div>
        </div>
    </div>
    
    <!-- Consultations List -->
    <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Consultation History</h3>
        
        <?php if (empty($consultations)): ?>
            <div class="bg-white px-4 py-5 border-b border-gray-200 text-center">
                <p class="text-gray-500">No consultation records found for this patient.</p>
            </div>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Doctor</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Chief Complaint</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Diagnosis</th>
                            <th scope="col" class="relative px-6 py-3">
                                <span class="sr-only">Actions</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($consultations as $consultation): ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?= isset($consultation['started_at']) ? e(date('M d, Y', strtotime($consultation['started_at']))) : 'N/A' ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">
                                        <?= e($consultation['doctor_name']) ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900 truncate max-w-xs">
                                        <?= e(substr($consultation['chief_complaint'], 0, 100)) . (strlen($consultation['chief_complaint']) > 100 ? '...' : '') ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900 truncate max-w-xs">
                                        <?= !empty($consultation['diagnosis']) ? e(substr($consultation['diagnosis'], 0, 100)) . (strlen($consultation['diagnosis']) > 100 ? '...' : '') : '<span class="text-gray-400">Not specified</span>' ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="<?= base_url('consultations/view/' . e($consultation['id'])) ?>" class="text-blue-600 hover:text-blue-900 mr-4">View</a>
                                    <a href="<?= base_url('consultations/edit/' . e($consultation['id'])) ?>" class="text-indigo-600 hover:text-indigo-900">Edit</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Timeline View -->
    <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Medical Timeline</h3>
        
        <?php if (empty($consultations)): ?>
            <div class="bg-white px-4 py-5 border-b border-gray-200 text-center">
                <p class="text-gray-500">No timeline data available.</p>
            </div>
        <?php else: ?>
            <div class="flow-root">
                <ul role="list" class="-mb-8">
                    <?php foreach ($consultations as $index => $consultation): ?>
                        <li>
                            <div class="relative pb-8">
                                <?php if ($index !== count($consultations) - 1): ?>
                                    <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                                <?php endif; ?>
                                <div class="relative flex space-x-3">
                                    <div>
                                        <span class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                                            <svg class="h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                                            </svg>
                                        </span>
                                    </div>
                                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                        <div>
                                            <p class="text-sm text-gray-500">
                                                <a href="<?= base_url('consultations/view/' . e($consultation['id'])) ?>" class="font-medium text-gray-900">
                                                    Consultation with Dr. <?= e(explode(' ', $consultation['doctor_name'])[1]) ?>
                                                </a>
                                            </p>
                                            <p class="mt-1 text-sm text-gray-900">
                                                <?= e(substr($consultation['chief_complaint'], 0, 150)) . (strlen($consultation['chief_complaint']) > 150 ? '...' : '') ?>
                                            </p>
                                            <?php if (!empty($consultation['diagnosis'])): ?>
                                                <p class="mt-2 text-sm text-gray-700">
                                                    <span class="font-medium">Diagnosis:</span> <?= e($consultation['diagnosis']) ?>
                                                </p>
                                            <?php endif; ?>
                                            <?php if (!empty($consultation['treatment_plan'])): ?>
                                                <p class="mt-1 text-sm text-gray-700">
                                                    <span class="font-medium">Treatment:</span> <?= e(substr($consultation['treatment_plan'], 0, 150)) . (strlen($consultation['treatment_plan']) > 150 ? '...' : '') ?>
                                                </p>
                                            <?php endif; ?>
                                        </div>
                                        <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                            <?php if(isset($consultation['started_at'])): ?>
                                                <time datetime="<?= e($consultation['started_at']) ?>"><?= e(date('M d, Y', strtotime($consultation['started_at']))) ?></time>
                                            <?php else: ?>
                                                N/A
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
    </div>
</div>
