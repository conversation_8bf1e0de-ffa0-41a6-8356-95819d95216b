<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">Doctor Consultations</h1>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">
                Consultations by Dr. <?= e($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
            </p>
        </div>
        <div class="flex space-x-2">
            <a href="<?= base_url('consultations') ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                All Consultations
            </a>
        </div>
    </div>
    
    <!-- Doctor Summary -->
    <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div class="bg-gray-50 px-4 py-5 sm:rounded-lg">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Doctor Information</h3>
                <dl class="mt-2 grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500">Name</dt>
                        <dd class="mt-1 text-sm text-gray-900">Dr. <?= e($doctor['first_name'] . ' ' . $doctor['last_name']) ?></dd>
                    </div>
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500">Specialty</dt>
                        <dd class="mt-1 text-sm text-gray-900"><?= e($doctor['specialty'] ?? 'General Practice') ?></dd>
                    </div>
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500">Email</dt>
                        <dd class="mt-1 text-sm text-gray-900"><?= e($doctor['email']) ?></dd>
                    </div>
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500">Phone</dt>
                        <dd class="mt-1 text-sm text-gray-900"><?= e($doctor['phone'] ?? 'Not provided') ?></dd>
                    </div>
                </dl>
            </div>
            
            <div class="bg-gray-50 px-4 py-5 sm:rounded-lg">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Consultation Summary</h3>
                <dl class="mt-2 grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-1">
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500">Total Consultations</dt>
                        <dd class="mt-1 text-sm text-gray-900"><?= e(count($consultations)) ?></dd>
                    </div>
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500">Recent Period</dt>
                        <dd class="mt-1 text-sm text-gray-900">Last 30 days: <?= e($recentCount) ?> consultations</dd>
                    </div>
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500">Most Recent</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <?= !empty($consultations) && isset($consultations[0]['started_at']) ? e(date('M d, Y', strtotime($consultations[0]['started_at']))) : 'None' ?>
                        </dd>
                    </div>
                </dl>
            </div>
        </div>
    </div>
    
    <!-- Filter Form -->
    <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
        <form method="GET" action="<?= base_url('consultations/doctor/' . e($doctor['id'])) ?>" class="space-y-4">
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-4">
                <div class="sm:col-span-1">
                    <label for="date_from" class="block text-sm font-medium text-gray-700">Date From</label>
                    <div class="mt-1">
                        <input type="date" name="date_from" id="date_from" value="<?= e($filters['date_from'] ?? '') ?>" 
                               class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                    </div>
                </div>
                
                <div class="sm:col-span-1">
                    <label for="date_to" class="block text-sm font-medium text-gray-700">Date To</label>
                    <div class="mt-1">
                        <input type="date" name="date_to" id="date_to" value="<?= e($filters['date_to'] ?? '') ?>" 
                               class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                    </div>
                </div>
                
                <div class="sm:col-span-1">
                    <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                    <div class="mt-1">
                        <input type="text" name="search" id="search" value="<?= e($filters['search'] ?? '') ?>" 
                               placeholder="Patient name or diagnosis" 
                               class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                    </div>
                </div>
                
                <div class="sm:col-span-1 flex items-end">
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        Filter
                    </button>
                </div>
            </div>
        </form>
    </div>
    
    <!-- Consultations List -->
    <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Consultation List</h3>
        
        <?php if (empty($consultations)): ?>
            <div class="bg-white px-4 py-5 border-b border-gray-200 text-center">
                <p class="text-gray-500">No consultation records found for this doctor.</p>
            </div>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Chief Complaint</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Diagnosis</th>
                            <th scope="col" class="relative px-6 py-3">
                                <span class="sr-only">Actions</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($consultations as $consultation): ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?= isset($consultation['started_at']) ? e(date('M d, Y', strtotime($consultation['started_at']))) : 'N/A' ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">
                                        <a href="<?= base_url('patients/view/' . e($consultation['patient_id'])) ?>" class="text-blue-600 hover:text-blue-900">
                                            <?= e($consultation['patient_name']) ?>
                                        </a>
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        <?= e(calculateAge($consultation['patient_dob'])) ?> years, <?= e(ucfirst($consultation['gender'])) ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900 truncate max-w-xs">
                                        <?= e(substr($consultation['chief_complaint'], 0, 100)) . (strlen($consultation['chief_complaint']) > 100 ? '...' : '') ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900 truncate max-w-xs">
                                        <?= !empty($consultation['diagnosis']) ? e(substr($consultation['diagnosis'], 0, 100)) . (strlen($consultation['diagnosis']) > 100 ? '...' : '') : '<span class="text-gray-400">Not specified</span>' ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="<?= base_url('consultations/view/' . e($consultation['id'])) ?>" class="text-blue-600 hover:text-blue-900 mr-4">View</a>
                                    <a href="<?= base_url('consultations/edit/' . e($consultation['id'])) ?>" class="text-indigo-600 hover:text-indigo-900">Edit</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <div class="px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                Showing <span class="font-medium"><?= e($offset + 1) ?></span> to <span class="font-medium"><?= e(min($offset + $limit, $totalRecords)) ?></span> of <span class="font-medium"><?= e($totalRecords) ?></span> results
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                <?php if ($currentPage > 1): ?>
                                    <a href="<?= base_url('consultations/doctor/' . e($doctor['id']) . '?' . http_build_query(array_merge($filters, ['page' => $currentPage - 1]))) ?>" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">Previous</span>
                                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                        </svg>
                                    </a>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $currentPage - 2); $i <= min($totalPages, $currentPage + 2); $i++): ?>
                                    <a href="<?= base_url('consultations/doctor/' . e($doctor['id']) . '?' . http_build_query(array_merge($filters, ['page' => $i]))) ?>" 
                                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium <?= $i === $currentPage ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:bg-gray-50' ?>">
                                        <?= e($i) ?>
                                    </a>
                                <?php endfor; ?>
                                
                                <?php if ($currentPage < $totalPages): ?>
                                    <a href="<?= base_url('consultations/doctor/' . e($doctor['id']) . '?' . http_build_query(array_merge($filters, ['page' => $currentPage + 1]))) ?>" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">Next</span>
                                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                        </svg>
                                    </a>
                                <?php endif; ?>
                            </nav>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>
