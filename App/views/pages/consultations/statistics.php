<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">Consultation Statistics</h1>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">
                Analytics and insights for consultations
            </p>
        </div>
        <div class="flex space-x-2">
            <a href="<?= base_url('consultations') ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Consultations
            </a>
        </div>
    </div>
    
    <!-- Filter Form -->
    <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
        <form method="GET" action="<?= base_url('consultations/statistics') ?>" class="space-y-4">
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-6">
                <div class="sm:col-span-1">
                    <label for="period" class="block text-sm font-medium text-gray-700">Time Period</label>
                    <div class="mt-1">
                        <select id="period" name="period" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                            <option value="week" <?= ($period === 'week') ? 'selected' : '' ?>>Last 7 days</option>
                            <option value="month" <?= ($period === 'month') ? 'selected' : '' ?>>Last 30 days</option>
                            <option value="quarter" <?= ($period === 'quarter') ? 'selected' : '' ?>>Last 90 days</option>
                            <option value="year" <?= ($period === 'year') ? 'selected' : '' ?>>Last 365 days</option>
                            <option value="custom" <?= ($period === 'custom') ? 'selected' : '' ?>>Custom range</option>
                        </select>
                    </div>
                </div>
                
                <div class="sm:col-span-1 custom-date-range <?= ($period !== 'custom') ? 'hidden' : '' ?>">
                    <label for="date_from" class="block text-sm font-medium text-gray-700">Date From</label>
                    <div class="mt-1">
                        <input type="date" name="date_from" id="date_from" value="<?= e($dateFrom ?? '') ?>" 
                               class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                    </div>
                </div>
                
                <div class="sm:col-span-1 custom-date-range <?= ($period !== 'custom') ? 'hidden' : '' ?>">
                    <label for="date_to" class="block text-sm font-medium text-gray-700">Date To</label>
                    <div class="mt-1">
                        <input type="date" name="date_to" id="date_to" value="<?= e($dateTo ?? '') ?>" 
                               class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                    </div>
                </div>
                
                <div class="sm:col-span-1">
                    <label for="doctor_id" class="block text-sm font-medium text-gray-700">Doctor</label>
                    <div class="mt-1">
                        <select id="doctor_id" name="doctor_id" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                            <option value="">All Doctors</option>
                            <?php foreach ($doctors as $doctor): ?>
                                <option value="<?= e($doctor['id']) ?>" <?= ($doctorId === $doctor['id']) ? 'selected' : '' ?>>
                                    <?= e($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                
                <div class="sm:col-span-1 flex items-end">
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                        </svg>
                        Apply Filters
                    </button>
                </div>
            </div>
        </form>
    </div>
    
    <!-- Summary Cards -->
    <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
        <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <!-- Total Consultations -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 bg-blue-500 rounded-md p-3">
                            <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Consultations</dt>
                                <dd class="flex items-baseline">
                                    <div class="text-2xl font-semibold text-gray-900"><?= e($stats['total_consultations']) ?></div>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Average Consultations Per Day -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 bg-indigo-500 rounded-md p-3">
                            <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Average Per Day</dt>
                                <dd class="flex items-baseline">
                                    <div class="text-2xl font-semibold text-gray-900"><?= e(number_format($stats['avg_per_day'], 1)) ?></div>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Unique Patients -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 bg-green-500 rounded-md p-3">
                            <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Unique Patients</dt>
                                <dd class="flex items-baseline">
                                    <div class="text-2xl font-semibold text-gray-900"><?= e($stats['unique_patients']) ?></div>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Consultations Growth -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 bg-yellow-500 rounded-md p-3">
                            <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Growth vs Previous Period</dt>
                                <dd class="flex items-baseline">
                                    <div class="text-2xl font-semibold text-gray-900">
                                        <?php if ($stats['growth_percentage'] > 0): ?>
                                            <span class="text-green-600">+<?= e(number_format($stats['growth_percentage'], 1)) ?>%</span>
                                        <?php elseif ($stats['growth_percentage'] < 0): ?>
                                            <span class="text-red-600"><?= e(number_format($stats['growth_percentage'], 1)) ?>%</span>
                                        <?php else: ?>
                                            <span class="text-gray-500">0%</span>
                                        <?php endif; ?>
                                    </div>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Charts -->
    <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
        <div class="grid grid-cols-1 gap-5 lg:grid-cols-2">
            <!-- Consultations Over Time -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Consultations Over Time</h3>
                    <div class="mt-4 h-80">
                        <canvas id="consultationsChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- Top Diagnoses -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Top Diagnoses</h3>
                    <div class="mt-4 h-80">
                        <canvas id="diagnosisChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Doctor Performance -->
    <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Doctor Performance</h3>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Doctor</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Consultations</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unique Patients</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg. Consultations/Day</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($doctorStats as $doctor): ?>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    <?= e($doctor['name']) ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?= e($doctor['consultations']) ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?= e($doctor['unique_patients']) ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?= e(number_format($doctor['avg_per_day'], 1)) ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    // Toggle custom date range fields based on period selection
    document.getElementById('period').addEventListener('change', function() {
        const customDateFields = document.querySelectorAll('.custom-date-range');
        if (this.value === 'custom') {
            customDateFields.forEach(field => field.classList.remove('hidden'));
        } else {
            customDateFields.forEach(field => field.classList.add('hidden'));
        }
    });
    
    // Consultations Over Time Chart
    const timeCtx = document.getElementById('consultationsChart').getContext('2d');
    const timeChart = new Chart(timeCtx, {
        type: 'line',
        data: {
            labels: <?= json_encode($timeChartData['labels']) ?>,
            datasets: [{
                label: 'Consultations',
                data: <?= json_encode($timeChartData['data']) ?>,
                backgroundColor: 'rgba(59, 130, 246, 0.2)',
                borderColor: 'rgba(59, 130, 246, 1)',
                borderWidth: 2,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });
    
    // Top Diagnoses Chart
    const diagnosisCtx = document.getElementById('diagnosisChart').getContext('2d');
    const diagnosisChart = new Chart(diagnosisCtx, {
        type: 'bar',
        data: {
            labels: <?= json_encode($diagnosisChartData['labels']) ?>,
            datasets: [{
                label: 'Frequency',
                data: <?= json_encode($diagnosisChartData['data']) ?>,
                backgroundColor: [
                    'rgba(59, 130, 246, 0.6)',
                    'rgba(16, 185, 129, 0.6)',
                    'rgba(245, 158, 11, 0.6)',
                    'rgba(239, 68, 68, 0.6)',
                    'rgba(139, 92, 246, 0.6)',
                    'rgba(236, 72, 153, 0.6)',
                    'rgba(6, 182, 212, 0.6)',
                    'rgba(249, 115, 22, 0.6)',
                    'rgba(168, 85, 247, 0.6)',
                    'rgba(217, 119, 6, 0.6)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });
</script>
