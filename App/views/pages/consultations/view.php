<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">Consultation Details</h1>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">
                <?= e($consultation['formatted_date']) ?>
            </p>
        </div>
        <div class="flex space-x-2">
            <a href="<?= base_url('consultations/edit/' . e($consultation['id'])) ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Edit
            </a>
        </div>
    </div>
    
    <!-- Patient and Doctor Information -->
    <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">Patient & Doctor Information</h3>
        <div class="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div class="bg-gray-50 px-4 py-5 sm:rounded-lg">
                <h4 class="text-base font-medium text-gray-900">Patient</h4>
                <dl class="mt-2 grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
                    <div class="sm:col-span-2">
                        <dt class="text-sm font-medium text-gray-500">Name</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <a href="<?= base_url('patients/' . e($consultation['patient_id'])) ?>" class="text-blue-600 hover:text-blue-900">
                                <?= e($consultation['patient_name']) ?>
                            </a>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Age</dt>
                        <dd class="mt-1 text-sm text-gray-900"><?= e($consultation['patient_age']) ?></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Gender</dt>
                        <dd class="mt-1 text-sm text-gray-900"><?= e(ucfirst($consultation['gender'])) ?></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Phone</dt>
                        <dd class="mt-1 text-sm text-gray-900"><?= e($consultation['phone']) ?></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Email</dt>
                        <dd class="mt-1 text-sm text-gray-900"><?= e($consultation['email']) ?></dd>
                    </div>
                </dl>
            </div>
            
            <div class="bg-gray-50 px-4 py-5 sm:rounded-lg">
                <h4 class="text-base font-medium text-gray-900">Doctor</h4>
                <dl class="mt-2 grid grid-cols-1 gap-x-4 gap-y-4">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Name</dt>
                        <dd class="mt-1 text-sm text-gray-900"><?= e($consultation['doctor_name']) ?></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Date & Time</dt>
                        <dd class="mt-1 text-sm text-gray-900"><?= e($consultation['formatted_date']) ?></dd>
                    </div>
                </dl>
            </div>
        </div>
    </div>
    
    <!-- Consultation Details -->
    <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">Consultation Details</h3>
        <div class="mt-4 space-y-6">
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-4 py-5 sm:px-6 bg-gray-50">
                    <h3 class="text-base font-medium text-gray-900">Chief Complaint</h3>
                </div>
                <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                    <p class="text-sm text-gray-900"><?= nl2br(e($consultation['chief_complaint'])) ?></p>
                </div>
            </div>
            
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-4 py-5 sm:px-6 bg-gray-50">
                    <h3 class="text-base font-medium text-gray-900">History of Present Illness</h3>
                </div>
                <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                    <p class="text-sm text-gray-900">
                        <?= !empty($consultation['history_of_present_illness']) ? nl2br(e($consultation['history_of_present_illness'])) : '<span class="text-gray-400">Not documented</span>' ?>
                    </p>
                </div>
            </div>
            
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-4 py-5 sm:px-6 bg-gray-50">
                    <h3 class="text-base font-medium text-gray-900">Past Medical History</h3>
                </div>
                <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                    <p class="text-sm text-gray-900">
                        <?= !empty($consultation['past_medical_history']) ? nl2br(e($consultation['past_medical_history'])) : '<span class="text-gray-400">Not documented</span>' ?>
                    </p>
                </div>
            </div>
            
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                    <div class="px-4 py-5 sm:px-6 bg-gray-50">
                        <h3 class="text-base font-medium text-gray-900">Medications</h3>
                    </div>
                    <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                        <p class="text-sm text-gray-900">
                            <?= !empty($consultation['medications']) ? nl2br(e($consultation['medications'])) : '<span class="text-gray-400">None documented</span>' ?>
                        </p>
                    </div>
                </div>
                
                <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                    <div class="px-4 py-5 sm:px-6 bg-gray-50">
                        <h3 class="text-base font-medium text-gray-900">Allergies</h3>
                    </div>
                    <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                        <p class="text-sm text-gray-900">
                            <?= !empty($consultation['allergies']) ? nl2br(e($consultation['allergies'])) : '<span class="text-gray-400">None documented</span>' ?>
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-4 py-5 sm:px-6 bg-gray-50">
                    <h3 class="text-base font-medium text-gray-900">Physical Examination</h3>
                </div>
                <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                    <p class="text-sm text-gray-900">
                        <?= !empty($consultation['physical_examination']) ? nl2br(e($consultation['physical_examination'])) : '<span class="text-gray-400">Not documented</span>' ?>
                    </p>
                </div>
            </div>
            
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-4 py-5 sm:px-6 bg-gray-50">
                    <h3 class="text-base font-medium text-gray-900">Assessment</h3>
                </div>
                <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                    <p class="text-sm text-gray-900">
                        <?= !empty($consultation['assessment']) ? nl2br(e($consultation['assessment'])) : '<span class="text-gray-400">Not documented</span>' ?>
                    </p>
                </div>
            </div>
            
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-4 py-5 sm:px-6 bg-gray-50">
                    <h3 class="text-base font-medium text-gray-900">Diagnosis</h3>
                </div>
                <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                    <p class="text-sm text-gray-900">
                        <?= !empty($consultation['diagnosis']) ? nl2br(e($consultation['diagnosis'])) : '<span class="text-gray-400">Not documented</span>' ?>
                    </p>
                </div>
            </div>
            
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-4 py-5 sm:px-6 bg-gray-50">
                    <h3 class="text-base font-medium text-gray-900">Treatment Plan</h3>
                </div>
                <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                    <p class="text-sm text-gray-900">
                        <?= !empty($consultation['treatment_plan']) ? nl2br(e($consultation['treatment_plan'])) : '<span class="text-gray-400">Not documented</span>' ?>
                    </p>
                </div>
            </div>
            
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-4 py-5 sm:px-6 bg-gray-50">
                    <h3 class="text-base font-medium text-gray-900">Follow-up</h3>
                </div>
                <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                    <p class="text-sm text-gray-900">
                        <?= !empty($consultation['follow_up']) ? nl2br(e($consultation['follow_up'])) : '<span class="text-gray-400">Not documented</span>' ?>
                    </p>
                </div>
            </div>
            
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-4 py-5 sm:px-6 bg-gray-50">
                    <h3 class="text-base font-medium text-gray-900">Additional Notes</h3>
                </div>
                <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                    <p class="text-sm text-gray-900">
                        <?= !empty($consultation['notes']) ? nl2br(e($consultation['notes'])) : '<span class="text-gray-400">No additional notes</span>' ?>
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
        <div class="flex justify-between">
            <a href="<?= base_url('consultations') ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Consultations
            </a>
            
            <?php if (isAdmin()): ?>
                <button type="button" 
                        onclick="confirmDelete('<?= e($consultation['id']) ?>', '<?= e($consultation['patient_name']) ?>')" 
                        class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                    <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    Delete Consultation
                </button>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed z-10 inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                        <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                            Delete Consultation
                        </h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500">
                                Are you sure you want to delete the consultation for <span id="patientName"></span>? This action cannot be undone.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <form id="deleteForm" method="POST">
                    <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
                    <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                        Delete
                    </button>
                </form>
                <button type="button" onclick="closeDeleteModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    function confirmDelete(id, patientName) {
        document.getElementById('patientName').textContent = patientName;
        document.getElementById('deleteForm').action = '<?= site_url('consultations') ?>/' + id + '/delete';
        document.getElementById('deleteModal').classList.remove('hidden');
    }
    
    function closeDeleteModal() {
        document.getElementById('deleteModal').classList.add('hidden');
    }
</script>
