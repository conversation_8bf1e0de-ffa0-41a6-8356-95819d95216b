<?php
$pageTitle = $title ?? 'Calendar';
$currentMonth = $currentDate->format('F Y');
$currentYear = $currentDate->format('Y');
$currentMonthNum = $currentDate->format('n');

// Get first day of month and number of days
$firstDay = clone $currentDate;
$firstDay->setDate($currentYear, $currentMonthNum, 1);
$firstDayOfWeek = $firstDay->format('w'); // 0 = Sunday
$daysInMonth = $firstDay->format('t');

// Calculate previous and next month
$prevMonth = clone $currentDate;
$prevMonth->modify('-1 month');
$nextMonth = clone $currentDate;
$nextMonth->modify('+1 month');

// Today's date for highlighting
$today = new DateTime();
$todayStr = $today->format('Y-m-d');
?>

<div class="calendar-container">
    <!-- Calendar Header -->
    <div class="calendar-header">
        <div class="calendar-nav">
            <button class="nav-btn" onclick="navigateMonth(<?= $prevMonth->format('Y') ?>, <?= $prevMonth->format('n') ?>)">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="15,18 9,12 15,6"></polyline>
                </svg>
            </button>
            
            <div class="calendar-title">
                <h2><?= $currentMonth ?></h2>
            </div>
            
            <button class="nav-btn" onclick="navigateMonth(<?= $nextMonth->format('Y') ?>, <?= $nextMonth->format('n') ?>)">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="9,6 15,12 9,18"></polyline>
                </svg>
            </button>
        </div>
        
        <div class="calendar-controls">
            <div class="view-controls">
                <button class="view-btn active">Month</button>
                <button class="view-btn">Week</button>
                <button class="view-btn">Day</button>
            </div>
            
            <button class="btn btn-primary" onclick="openNewAppointmentModal()">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
                New Appointment
            </button>
        </div>
    </div>

    <!-- Calendar Grid -->
    <div class="calendar-grid">
        <!-- Day Headers -->
        <div class="calendar-weekdays">
            <div class="weekday">Sun</div>
            <div class="weekday">Mon</div>
            <div class="weekday">Tue</div>
            <div class="weekday">Wed</div>
            <div class="weekday">Thu</div>
            <div class="weekday">Fri</div>
            <div class="weekday">Sat</div>
        </div>

        <!-- Calendar Days -->
        <div class="calendar-days">
            <?php
            // Add empty cells for days before the first day of the month
            for ($i = 0; $i < $firstDayOfWeek; $i++) {
                $prevMonthDay = $prevMonth->format('t') - ($firstDayOfWeek - $i - 1);
                echo '<div class="calendar-day other-month">';
                echo '<div class="day-number">' . $prevMonthDay . '</div>';
                echo '</div>';
            }

            // Add days of the current month
            for ($day = 1; $day <= $daysInMonth; $day++) {
                $currentDayStr = $currentDate->format('Y-m-') . sprintf('%02d', $day);
                $isToday = $currentDayStr === $todayStr;
                $dayAppointments = $appointmentsByDate[$currentDayStr] ?? [];
                
                echo '<div class="calendar-day' . ($isToday ? ' today' : '') . '" data-date="' . $currentDayStr . '">';
                echo '<div class="day-number">' . $day . '</div>';
                
                // Display appointments
                if (!empty($dayAppointments)) {
                    echo '<div class="day-appointments">';
                    $appointmentCount = 0;
                    foreach ($dayAppointments as $appointment) {
                        if ($appointmentCount >= 3) {
                            $remaining = count($dayAppointments) - 3;
                            echo '<div class="appointment-more">+' . $remaining . ' more</div>';
                            break;
                        }
                        
                        $statusClass = 'appointment-' . $appointment['status'];
                        $time = date('H:i', strtotime($appointment['appointment_date']));
                        $patientName = $appointment['first_name'] . ' ' . $appointment['last_name'];
                        
                        echo '<div class="appointment-block ' . $statusClass . '" onclick="viewAppointment(\'' . $appointment['id'] . '\')">';
                        echo '<div class="appointment-time">' . $time . '</div>';
                        echo '<div class="appointment-patient">' . htmlspecialchars($patientName) . '</div>';
                        echo '</div>';
                        
                        $appointmentCount++;
                    }
                    echo '</div>';
                }
                
                echo '</div>';
            }

            // Calculate remaining cells and add next month days
            $totalCells = $firstDayOfWeek + $daysInMonth;
            $remainingCells = 42 - $totalCells; // 6 rows × 7 days = 42 cells
            
            for ($day = 1; $day <= $remainingCells; $day++) {
                echo '<div class="calendar-day other-month">';
                echo '<div class="day-number">' . $day . '</div>';
                echo '</div>';
            }
            ?>
        </div>
    </div>

    <!-- Selected Date Appointments Panel -->
    <div class="appointments-panel" id="appointmentsPanel">
        <div class="panel-header">
            <h3 id="selectedDateTitle">Appointments for Saturday, August 16, 2025</h3>
        </div>
        <div class="panel-content" id="appointmentsList">
            <div class="no-appointments">
                <p>No appointments scheduled for this day</p>
                <button class="btn btn-outline" onclick="openNewAppointmentModal()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="12" y1="5" x2="12" y2="19"></line>
                        <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                    Add Appointment
                </button>
            </div>
        </div>
    </div>
</div>

<!-- New Appointment Modal -->
<div class="modal" id="newAppointmentModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>New Appointment</h3>
            <button class="modal-close" onclick="closeNewAppointmentModal()">&times;</button>
        </div>
        <div class="modal-body">
            <form id="newAppointmentForm" action="/appointments/create" method="POST">
                <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
                <input type="hidden" name="appointment_date_selected" id="selectedDate" value="">
                
                <div class="form-group">
                    <label for="patient_id">Patient</label>
                    <select name="patient_id" id="patient_id" required>
                        <option value="">Select Patient</option>
                        <?php if (!empty($patients)): ?>
                            <?php foreach ($patients as $patient): ?>
                                <option value="<?= e($patient['id']) ?>">
                                    <?= e($patient['first_name'] . ' ' . $patient['last_name']) ?>
                                </option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="appointment_datetime">Date & Time</label>
                    <input type="datetime-local" name="appointment_date" id="appointment_datetime" required>
                </div>
                
                <div class="form-group">
                    <label for="duration_minutes">Duration (minutes)</label>
                    <select name="duration_minutes" id="duration_minutes">
                        <option value="15">15 minutes</option>
                        <option value="30" selected>30 minutes</option>
                        <option value="45">45 minutes</option>
                        <option value="60">60 minutes</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="reason">Reason for Visit</label>
                    <textarea name="reason" id="reason" rows="3" required></textarea>
                </div>
                
                <div class="form-group">
                    <label for="notes">Notes</label>
                    <textarea name="notes" id="notes" rows="2"></textarea>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeNewAppointmentModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Appointment</button>
                </div>
            </form>
        </div>
    </div>
</div>


<script>
let selectedDate = null;

// Initialize calendar
document.addEventListener('DOMContentLoaded', function() {
    // Set today as selected date initially
    const today = new Date();
    const todayStr = today.getFullYear() + '-' + 
                    String(today.getMonth() + 1).padStart(2, '0') + '-' + 
                    String(today.getDate()).padStart(2, '0');
    
    selectDate(todayStr);
    
    // Add click handlers to calendar days
    document.querySelectorAll('.calendar-day').forEach(day => {
        day.addEventListener('click', function() {
            const date = this.dataset.date;
            if (date) {
                selectDate(date);
            }
        });
    });
});

function navigateMonth(year, month) {
    window.location.href = `/calendar?year=${year}&month=${month}`;
}

function selectDate(dateStr) {
    selectedDate = dateStr;
    
    // Update selected date styling
    document.querySelectorAll('.calendar-day').forEach(day => {
        day.classList.remove('selected');
    });
    
    const selectedDay = document.querySelector(`[data-date="${dateStr}"]`);
    if (selectedDay) {
        selectedDay.classList.add('selected');
    }
    
    // Update appointments panel
    updateAppointmentsPanel(dateStr);
}

function updateAppointmentsPanel(dateStr) {
    const date = new Date(dateStr);
    const options = { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    };
    const formattedDate = date.toLocaleDateString('en-US', options);
    
    document.getElementById('selectedDateTitle').textContent = `Appointments for ${formattedDate}`;
    
    // Fetch appointments for the selected date
    fetch(`/appointments/getAppointmentsByDate?date=${dateStr}`)
        .then(response => response.json())
        .then(data => {
            const appointmentsList = document.getElementById('appointmentsList');
            
            if (data.success && data.appointments.length > 0) {
                let html = '<div class="appointments-list">';
                data.appointments.forEach(appointment => {
                    const statusClass = `appointment-${appointment.status}`;
                    html += `
                        <div class="appointment-item ${statusClass}" onclick="viewAppointment('${appointment.id}')">
                            <div class="appointment-time">${appointment.formatted_time}</div>
                            <div class="appointment-details">
                                <div class="appointment-patient">${appointment.patient_name}</div>
                                <div class="appointment-reason">${appointment.reason_for_visit || 'No reason specified'}</div>
                            </div>
                            <div class="appointment-status">${appointment.status}</div>
                        </div>
                    `;
                });
                html += '</div>';
                appointmentsList.innerHTML = html;
            } else {
                appointmentsList.innerHTML = `
                    <div class="no-appointments">
                        <p>No appointments scheduled for this day</p>
                        <button class="btn btn-outline" onclick="openNewAppointmentModal('${dateStr}')">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="12" y1="5" x2="12" y2="19"></line>
                                <line x1="5" y1="12" x2="19" y2="12"></line>
                            </svg>
                            Add Appointment
                        </button>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error fetching appointments:', error);
        });
}

function openNewAppointmentModal(dateStr = null) {
    const modal = document.getElementById('newAppointmentModal');
    const selectedDateInput = document.getElementById('selectedDate');
    const datetimeInput = document.getElementById('appointment_datetime');
    
    if (dateStr || selectedDate) {
        const date = dateStr || selectedDate;
        selectedDateInput.value = date;
        datetimeInput.value = date + 'T09:00';
    }
    
    // Load patients
    loadPatients();
    
    modal.classList.add('show');
}

function closeNewAppointmentModal() {
    const modal = document.getElementById('newAppointmentModal');
    modal.classList.remove('show');
}

function loadPatients() {
    fetch('/api/patients')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('patient_id');
            select.innerHTML = '<option value="">Select Patient</option>';
            
            if (data.success && data.patients) {
                data.patients.forEach(patient => {
                    const option = document.createElement('option');
                    option.value = patient.id;
                    option.textContent = `${patient.first_name} ${patient.last_name}`;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading patients:', error);
        });
}

function viewAppointment(appointmentId) {
    window.location.href = `/appointments/${appointmentId}`;
}

// Close modal when clicking outside
document.getElementById('newAppointmentModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeNewAppointmentModal();
    }
});
</script>
