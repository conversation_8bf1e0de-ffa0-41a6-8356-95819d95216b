<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <h1 class="text-2xl font-semibold text-gray-900">Edit Appointment</h1>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">Update appointment details</p>
    </div>
    
    <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
        <form method="POST" action="<?= base_url('appointments/' . $appointment['id'] . '/edit') ?>" class="space-y-8">
            <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
            
            <!-- Patient and Doctor Selection -->
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Appointment Details</h3>
                <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="patient_id" class="block text-sm font-medium text-gray-700">Patient</label>
                        <div class="mt-1">
                            <select id="patient_id" name="patient_id" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" required>
                                <option value="">Select patient</option>
                                <?php foreach ($patients as $patient): ?>
                                    <option value="<?= e($patient['id']) ?>" <?= isset($formData['patient_id']) && $formData['patient_id'] === $patient['id'] ? 'selected' : '' ?>>
                                        <?= e($patient['first_name'] . ' ' . $patient['last_name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <!-- Doctor field removed as appointments table doesn't have doctor_id column -->
                </div>
            </div>
            
            <!-- Date, Time and Duration -->
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Date and Time</h3>
                <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-2">
                        <label for="appointment_date_date" class="block text-sm font-medium text-gray-700">Date</label>
                        <div class="mt-1">
                            <input type="date" id="appointment_date_date" name="appointment_date_date" 
                                   value="<?= isset($formData['appointment_date']) ? date('Y-m-d', strtotime($formData['appointment_date'])) : '' ?>" 
                                   class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" 
                                   required>
                        </div>
                    </div>

                    <div class="sm:col-span-2">
                        <label for="appointment_time" class="block text-sm font-medium text-gray-700">Time</label>
                        <div class="mt-1">
                            <select id="appointment_time" name="appointment_time" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" required>
                                <option value="">Select time</option>
                                <!-- Current time will be added as an option regardless of availability -->
                                <?php if (isset($formData['appointment_date'])): ?>
                                <option value="<?= e($formData['appointment_date']) ?>" selected>
                                    <?= date('H:i', strtotime($formData['appointment_date'])) ?> (Current)
                                </option>
                                <?php endif; ?>
                                <!-- Other time slots will be populated dynamically -->
                            </select>
                        </div>
                    </div>

                    <div class="sm:col-span-2">
                        <label for="duration_minutes" class="block text-sm font-medium text-gray-700">Duration (minutes)</label>
                        <div class="mt-1">
                            <select id="duration_minutes" name="duration_minutes" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" required>
                                <option value="15" <?= isset($formData['duration_minutes']) && $formData['duration_minutes'] == 15 ? 'selected' : '' ?>>15 minutes</option>
                                <option value="30" <?= isset($formData['duration_minutes']) && $formData['duration_minutes'] == 30 ? 'selected' : '' ?>>30 minutes</option>
                                <option value="45" <?= isset($formData['duration_minutes']) && $formData['duration_minutes'] == 45 ? 'selected' : '' ?>>45 minutes</option>
                                <option value="60" <?= isset($formData['duration_minutes']) && $formData['duration_minutes'] == 60 ? 'selected' : '' ?>>60 minutes</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Reason and Status -->
            <div>
                <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-6">
                        <label for="reason_for_visit" class="block text-sm font-medium text-gray-700">Reason for appointment</label>
                        <div class="mt-1">
                            <textarea id="reason_for_visit" name="reason_for_visit" rows="3" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" required><?= isset($formData['reason_for_visit']) ? e($formData['reason_for_visit']) : '' ?></textarea>
                        </div>
                    </div>

                    <div class="sm:col-span-3">
                        <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                        <div class="mt-1">
                            <select id="status" name="status" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                <?php foreach ($statuses as $status): ?>
                                    <option value="<?= e($status) ?>" <?= isset($formData['status']) && $formData['status'] === $status ? 'selected' : '' ?>>
                                        <?= ucfirst(e($status)) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Notes -->
            <div>
                <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-6">
                        <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                        <div class="mt-1">
                            <textarea id="notes" name="notes" rows="4" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['notes']) ? e($formData['notes']) : '' ?></textarea>
                        </div>
                        <p class="mt-2 text-sm text-gray-500">Additional notes or instructions for the appointment.</p>
                    </div>
                </div>
            </div>

            <div class="pt-5">
                <div class="flex justify-end">
                    <a href="<?= base_url('appointments') ?>" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Cancel
                    </a>
                    <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Update Appointment
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM fully loaded - Edit page');
        const form = document.querySelector('form');
        const dateInput = document.getElementById('appointment_date_date');
        const timeSelect = document.getElementById('appointment_time');
        const currentAppointmentDateTime = "<?= isset($formData['appointment_date']) ? $formData['appointment_date'] : '' ?>";
        
        // Handle form submission
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('Form submitted');
            
            // Create hidden field for the combined date and time
            const hiddenDateTimeInput = document.createElement('input');
            hiddenDateTimeInput.type = 'hidden';
            hiddenDateTimeInput.name = 'appointment_date';
            hiddenDateTimeInput.value = timeSelect.value;
            console.log('Setting appointment_date to:', timeSelect.value);
            
            form.appendChild(hiddenDateTimeInput);
            form.submit();
        });
        
        // Generate time slots from 9:00 to 17:00 with 30-minute intervals
        function generateTimeSlots(date) {
            console.log('Generating time slots for date:', date);
            
            // Save current selected time option if it exists
            const currentSelectedOption = timeSelect.value;
            console.log('Current selected time:', currentSelectedOption);
            
            // Clear existing options
            timeSelect.innerHTML = '<option value="">Select time</option>';
            
            // Add back the current appointment time as the first option if it exists
            if (currentAppointmentDateTime) {
                const currentTime = new Date(currentAppointmentDateTime);
                const hours = currentTime.getHours().toString().padStart(2, '0');
                const minutes = currentTime.getMinutes().toString().padStart(2, '0');
                const timeStr = `${hours}:${minutes}`;
                
                const option = document.createElement('option');
                option.value = currentAppointmentDateTime;
                option.textContent = `${timeStr} (Current)`;
                option.selected = true;
                timeSelect.appendChild(option);
                console.log('Added current appointment time:', timeStr);
            }
            
            const selectedDate = new Date(date);
            const startHour = 9;
            const endHour = 17;
            const slotDuration = 30; // minutes
            
            let currentTime = new Date(selectedDate);
            currentTime.setHours(startHour, 0, 0, 0);
            
            const endTime = new Date(selectedDate);
            endTime.setHours(endHour, 0, 0, 0);
            
            console.log('Start time:', currentTime);
            console.log('End time:', endTime);
            
            while (currentTime < endTime) {
                const startHours = currentTime.getHours().toString().padStart(2, '0');
                const startMinutes = currentTime.getMinutes().toString().padStart(2, '0');
                const startTimeStr = `${startHours}:${startMinutes}`;
                
                // Create end time (30 minutes later)
                const endTimeObj = new Date(currentTime.getTime() + slotDuration * 60000);
                const endHours = endTimeObj.getHours().toString().padStart(2, '0');
                const endMinutes = endTimeObj.getMinutes().toString().padStart(2, '0');
                const endTimeStr = `${endHours}:${endMinutes}`;
                
                // Format the datetime value for the option (YYYY-MM-DD HH:MM:SS)
                const year = selectedDate.getFullYear();
                const month = (selectedDate.getMonth() + 1).toString().padStart(2, '0');
                const day = selectedDate.getDate().toString().padStart(2, '0');
                const datetimeValue = `${year}-${month}-${day} ${startHours}:${startMinutes}:00`;
                
                // Skip if this is the current appointment time (already added above)
                if (datetimeValue !== currentAppointmentDateTime) {
                    const option = document.createElement('option');
                    option.value = datetimeValue;
                    option.textContent = `${startTimeStr} - ${endTimeStr}`;
                    
                    // Restore previously selected value if it matches
                    if (datetimeValue === currentSelectedOption) {
                        option.selected = true;
                    }
                    
                    timeSelect.appendChild(option);
                    console.log('Added time slot:', startTimeStr, '-', endTimeStr);
                }
                
                // Move to next slot
                currentTime = endTimeObj;
            }
        }
        
        // Event listeners for date change
        dateInput.addEventListener('change', function() {
            console.log('Date changed to:', dateInput.value);
            if (dateInput.value) {
                generateTimeSlots(dateInput.value);
            }
        });
        
        // Generate time slots for the selected date
        if (dateInput.value) {
            console.log('Initial date value:', dateInput.value);
            generateTimeSlots(dateInput.value);
        } else {
            // If no date is set, use the date from the current appointment
            if (currentAppointmentDateTime) {
                const currentDate = new Date(currentAppointmentDateTime);
                const year = currentDate.getFullYear();
                const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
                const day = currentDate.getDate().toString().padStart(2, '0');
                const formattedDate = `${year}-${month}-${day}`;
                
                console.log('Setting date from current appointment:', formattedDate);
                dateInput.value = formattedDate;
                generateTimeSlots(formattedDate);
            } else {
                // Fallback to today's date
                const today = new Date();
                const year = today.getFullYear();
                const month = (today.getMonth() + 1).toString().padStart(2, '0');
                const day = today.getDate().toString().padStart(2, '0');
                const formattedDate = `${year}-${month}-${day}`;
                
                console.log('Setting default date to today:', formattedDate);
                dateInput.value = formattedDate;
                generateTimeSlots(formattedDate);
            }
        }
    });
</script>
