<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6 flex justify-between">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">STD Testing Consultation</h1>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">
                <?= format_date($consultation['started_at']) ?>
            </p>
        </div>
        <div>
            <a href="<?= base_url('std-testing/' . $consultation['id'] . '/edit') ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Edit
            </a>
            <a href="<?= base_url('std-testing') ?>" class="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Back to List
            </a>
        </div>
    </div>
    
    <div class="border-t border-gray-200">
        <dl>
            <!-- Patient Information -->
            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Patient</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <?= e($consultation['patient_first_name'] . ' ' . $consultation['patient_last_name']) ?>
                </dd>
            </div>
            
            <!-- Test Information -->
            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Test Type</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <?php 
                    $testTypes = [
                        'comprehensive' => 'Comprehensive STD Panel',
                        'basic' => 'Basic STD Panel',
                        'hiv' => 'HIV Test',
                        'chlamydia_gonorrhea' => 'Chlamydia & Gonorrhea',
                        'syphilis' => 'Syphilis Test',
                        'hepatitis' => 'Hepatitis Panel',
                        'herpes' => 'Herpes Test',
                        'custom' => 'Custom Panel'
                    ];
                    $testType = !empty($consultation['metadata']['test_type']) ? $consultation['metadata']['test_type'] : '';
                    echo isset($testTypes[$testType]) ? e($testTypes[$testType]) : 'N/A';
                    
                    if ($testType === 'custom' && !empty($consultation['metadata']['custom_panel'])) {
                        echo '<div class="mt-2"><span class="font-medium">Custom Panel Details:</span> ' . nl2br(e($consultation['metadata']['custom_panel'])) . '</div>';
                    }
                    ?>
                </dd>
            </div>
            
            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Sample Type</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <?php 
                    $sampleTypes = [
                        'blood' => 'Blood',
                        'urine' => 'Urine',
                        'swab' => 'Swab',
                        'multiple' => 'Multiple Samples'
                    ];
                    $sampleType = !empty($consultation['metadata']['sample_type']) ? $consultation['metadata']['sample_type'] : '';
                    echo isset($sampleTypes[$sampleType]) ? e($sampleTypes[$sampleType]) : 'N/A';
                    ?>
                </dd>
            </div>
            
            <!-- Risk Assessment -->
            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Risk Factors</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <?php if (!empty($consultation['metadata']['risk_factors']) && is_array($consultation['metadata']['risk_factors'])): ?>
                        <ul class="list-disc pl-5">
                            <?php 
                            $riskFactorLabels = [
                                'multiple_partners' => 'Multiple sexual partners',
                                'unprotected_sex' => 'Unprotected sexual contact',
                                'partner_with_std' => 'Partner with known STD',
                                'sex_work' => 'Sex work involvement',
                                'msm' => 'Men who have sex with men',
                                'iv_drug_use' => 'Intravenous drug use',
                                'previous_std' => 'Previous STD history'
                            ];
                            
                            foreach ($consultation['metadata']['risk_factors'] as $factor): 
                                $label = isset($riskFactorLabels[$factor]) ? $riskFactorLabels[$factor] : $factor;
                            ?>
                                <li><?= e($label) ?></li>
                            <?php endforeach; ?>
                        </ul>
                    <?php elseif (!empty($consultation['metadata']['risk_factors'])): ?>
                        <?= nl2br(e($consultation['metadata']['risk_factors'])) ?>
                    <?php else: ?>
                        None documented
                    <?php endif; ?>
                </dd>
            </div>
            
            <!-- AI Recommendations -->
            <?php if (!empty($consultation['metadata']['recommendations'])): ?>
            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">AI-Generated Recommendations</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <div class="bg-blue-50 p-4 rounded-md">
                        <h4 class="text-md font-medium text-blue-800 mb-2">Recommended Tests & Clinical Guidance</h4>
                        <ul class="list-disc pl-5 text-blue-700">
                            <?php foreach ($consultation['metadata']['recommendations'] as $recommendation): ?>
                                <li class="mb-1"><?= e($recommendation) ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <p class="mt-2 text-xs text-blue-500">Generated by AI based on patient risk factors and symptoms</p>
                    </div>
                </dd>
            </div>
            <?php endif; ?>
            
            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Current Symptoms</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <?= !empty($consultation['metadata']['symptoms']) ? nl2br(e($consultation['metadata']['symptoms'])) : 'None reported' ?>
                </dd>
            </div>
            
            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Previous Testing History</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <?= !empty($consultation['metadata']['previous_testing']) ? nl2br(e($consultation['metadata']['previous_testing'])) : 'None documented' ?>
                </dd>
            </div>
            
            <!-- Counseling -->
            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Pre-Test Counseling</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <?= !empty($consultation['metadata']['counseling_notes']) ? nl2br(e($consultation['metadata']['counseling_notes'])) : 'None documented' ?>
                    
                    <?php if (!empty($consultation['metadata']['consent_obtained'])): ?>
                        <div class="mt-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Informed consent obtained
                            </span>
                        </div>
                    <?php endif; ?>
                </dd>
            </div>
            
            <!-- Test Results (if available) -->
            <?php if ($consultation['status'] === 'completed' && !empty($consultation['metadata']['results'])): ?>
            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Test Results</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <?= nl2br(e($consultation['metadata']['results'])) ?>
                </dd>
            </div>
            
            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Result Date</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <?= !empty($consultation['metadata']['result_date']) ? format_date($consultation['metadata']['result_date']) : 'N/A' ?>
                </dd>
            </div>
            
            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Post-Test Counseling</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <?= !empty($consultation['metadata']['post_test_counseling']) ? nl2br(e($consultation['metadata']['post_test_counseling'])) : 'None documented' ?>
                </dd>
            </div>
            
            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Treatment Plan</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <?= !empty($consultation['metadata']['treatment_plan']) ? nl2br(e($consultation['metadata']['treatment_plan'])) : 'None documented' ?>
                </dd>
            </div>
            <?php else: ?>
            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Test Status</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $consultation['status'] === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800' ?>">
                        <?= e(ucfirst($consultation['status'])) ?>
                    </span>
                    <?php if ($consultation['status'] === 'pending'): ?>
                        <p class="mt-1 text-sm text-gray-500">Results are pending. Check back later or update when results are available.</p>
                    <?php endif; ?>
                </dd>
            </div>
            <?php endif; ?>
            
            <!-- Notes -->
            <div class="<?= $consultation['status'] === 'completed' ? 'bg-gray-50' : 'bg-white' ?> px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Additional Notes</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <?= !empty($consultation['notes']) ? nl2br(e($consultation['notes'])) : 'None' ?>
                </dd>
            </div>
        </dl>
    </div>
</div>
