<div class="form-card">
    <!-- CSS for enhanced medical UI -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="<?= site_url('resources/css/protocol-steps.css') ?>">
    <link rel="stylesheet" href="<?= site_url('resources/css/medical-ui-enhanced.css') ?>">
    
    <div class="form-card-header">
        <h1 class="text-2xl font-semibold text-gray-900">STD Testing Consultation</h1>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">Step 2 of 4: Risk Assessment</p>
    </div>
    
    <!-- Enhanced Step Progress Bar -->
    <div class="form-card-header border-t border-gray-200">
        <div class="step-progress">
            <?php foreach ($steps as $stepNumber => $stepInfo): ?>
                <div class="flex flex-col items-center">
                    <div class="step-circle <?= $stepNumber === $currentStep ? 'step-circle-active' : ($stepNumber < $currentStep ? 'step-circle-completed' : 'step-circle-inactive') ?>">
                        <?php if ($stepNumber < $currentStep): ?>
                            <i class="fas fa-check"></i>
                        <?php else: ?>
                            <?= $stepNumber ?>
                        <?php endif; ?>
                    </div>
                    <span class="step-title <?= $stepNumber === $currentStep ? 'step-title-active' : ($stepNumber < $currentStep ? 'step-title-completed' : 'step-title-inactive') ?>">
                        <?= $stepInfo['title'] ?>
                    </span>
                </div>
                
                <?php if ($stepNumber < count($steps)): ?>
                    <div class="step-line <?= $stepNumber < $currentStep ? 'step-line-completed' : 'step-line-inactive' ?>"></div>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
    </div>
    
    <div class="form-card-body">
        <form method="POST" action="<?= base_url('std-testing/processStep') ?>" class="space-y-8">
            <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
            <input type="hidden" name="current_step" value="<?= $currentStep ?>">
            <input type="hidden" name="next_step" value="<?= $currentStep + 1 ?>">
            
            <!-- Enhanced Symptoms Section -->
            <div class="medical-card">
                <div class="medical-card-header">
                    <div class="medical-card-icon" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);">
                        <i class="fas fa-stethoscope"></i>
                    </div>
                    <h3 class="medical-card-title">Current Symptoms</h3>
                </div>
                <div class="medical-card-body">
                    <div class="mb-4">
                        <label for="symptoms" class="form-label medical-heading-3">Symptom Description</label>
                        <textarea id="symptoms" name="symptoms" rows="4" 
                                  class="form-input form-control-lg" 
                                  placeholder="Describe any current symptoms the patient is experiencing (e.g., discharge, pain, lesions, burning sensation, etc.)"><?= e(isset($formData['symptoms']) ? $formData['symptoms'] : '') ?></textarea>
                        <small class="form-text text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Include onset, duration, severity, and any associated symptoms
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- Enhanced Exposure History Section -->
            <div class="medical-card">
                <div class="medical-card-header">
                    <div class="medical-card-icon" style="background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <h3 class="medical-card-title">Exposure History</h3>
                </div>
                <div class="medical-card-body">
                    <div class="mb-4">
                        <label for="exposure_history" class="form-label medical-heading-3">Exposure Details</label>
                        <textarea id="exposure_history" name="exposure_history" rows="4" 
                                  class="form-input form-control-lg"
                                  placeholder="Describe any known exposures, including timing, nature of exposure, and partner information (if relevant)"><?= e(isset($formData['exposure_history']) ? $formData['exposure_history'] : '') ?></textarea>
                        <small class="form-text text-muted">
                            <i class="fas fa-shield-alt me-1"></i>
                            Include timeframe of exposure and any protective measures used
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- Enhanced Risk Factors Section -->
            <div class="medical-card">
                <div class="medical-card-header">
                    <div class="medical-card-icon" style="background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h3 class="medical-card-title">Risk Factor Assessment</h3>
                </div>
                <div class="medical-card-body">
                    <fieldset>
                        <legend class="form-label medical-heading-3 mb-4">Select all risk factors that apply:</legend>
                        <div class="risk-factors-grid">
                            <?php 
                            $riskFactorCategories = [
                                'high' => ['multiple_partners', 'unprotected_sex', 'iv_drug_use'],
                                'moderate' => ['new_partner', 'partner_symptoms', 'previous_std'],
                                'low' => ['oral_sex', 'sex_work_exposure', 'travel_exposure']
                            ];
                            
                            foreach ($riskFactorCategories as $riskLevel => $factors): 
                                $riskClass = $riskLevel === 'high' ? 'risk-high' : ($riskLevel === 'moderate' ? 'risk-moderate' : 'risk-low');
                            ?>
                                <div class="risk-category-section <?= $riskClass ?>">
                                    <div class="risk-section-header">
                                        <div class="risk-section-icon">
                                            <?php if ($riskLevel === 'high'): ?>
                                                <i class="fas fa-exclamation-circle"></i>
                                            <?php elseif ($riskLevel === 'moderate'): ?>
                                                <i class="fas fa-exclamation-triangle"></i>
                                            <?php else: ?>
                                                <i class="fas fa-info-circle"></i>
                                            <?php endif; ?>
                                        </div>
                                        <h4 class="risk-section-title"><?= ucfirst($riskLevel) ?> Risk Factors</h4>
                                        <span class="severity-badge severity-<?= $riskLevel ?>"><?= ucfirst($riskLevel) ?></span>
                                    </div>
                                    
                                    <div class="risk-factors-list">
                                        <?php foreach ($riskFactorOptions as $value => $label): ?>
                                            <?php if (in_array($value, $factors)): ?>
                                                <div class="risk-factor-item">
                                                    <div class="flex items-center h-5">
                                                        <input id="risk_factor_<?= $value ?>" name="risk_factors[]" type="checkbox" value="<?= $value ?>" 
                                                            <?= (isset($formData['risk_factors']) && is_array($formData['risk_factors']) && in_array($value, $formData['risk_factors'])) ? 'checked' : '' ?>
                                                            class="form-checkbox h-5 w-5">
                                                    </div>
                                                    <div class="ml-3">
                                                        <label for="risk_factor_<?= $value ?>" class="risk-factor-label"><?= $label ?></label>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                            
                            <!-- Fallback for any remaining risk factors -->
                            <div class="risk-category-section risk-low">
                                <div class="risk-section-header">
                                    <div class="risk-section-icon">
                                        <i class="fas fa-list"></i>
                                    </div>
                                    <h4 class="risk-section-title">Other Risk Factors</h4>
                                </div>
                                
                                <div class="risk-factors-list">
                                    <?php foreach ($riskFactorOptions as $value => $label): ?>
                                        <?php if (!in_array($value, array_merge($riskFactorCategories['high'], $riskFactorCategories['moderate'], $riskFactorCategories['low']))): ?>
                                            <div class="risk-factor-item">
                                                <div class="flex items-center h-5">
                                                    <input id="risk_factor_<?= $value ?>" name="risk_factors[]" type="checkbox" value="<?= $value ?>" 
                                                        <?= (isset($formData['risk_factors']) && is_array($formData['risk_factors']) && in_array($value, $formData['risk_factors'])) ? 'checked' : '' ?>
                                                        class="form-checkbox h-5 w-5">
                                                </div>
                                                <div class="ml-3">
                                                    <label for="risk_factor_<?= $value ?>" class="risk-factor-label"><?= $label ?></label>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                </div>
            </div>

            <div class="pt-5">
                <div class="flex justify-between">
                    <button type="submit" name="next_step" value="1" class="btn btn-outline-secondary btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>Previous Step
                    </button>
                    <button type="submit" class="btn btn-primary btn-lg">
                        Next Step<i class="fas fa-arrow-right ms-2"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<style>
/* Enhanced STD Testing Styles */
.risk-factors-grid {
    display: grid;
    gap: 1.5rem;
    margin-top: 1rem;
}

.risk-factors-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 0.75rem;
    margin-top: 1rem;
}

.risk-factor-item {
    display: flex;
    align-items: start;
    padding: 0.75rem;
    background: rgba(255,255,255,0.7);
    border-radius: 8px;
    transition: all 0.2s ease;
}

.risk-factor-item:hover {
    background: rgba(255,255,255,0.9);
    transform: translateX(3px);
}

.risk-factor-label {
    font-weight: 500;
    color: #2c3e50;
    font-size: 0.95rem;
    line-height: 1.4;
}

.form-checkbox {
    border-radius: 4px;
    border: 2px solid #dee2e6;
    transition: all 0.2s ease;
}

.form-checkbox:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.form-checkbox:focus {
    ring: 2px;
    ring-color: rgba(13, 110, 253, 0.25);
    ring-offset: 2px;
}

/* Step progress enhancements */
.step-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.step-circle-active {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.3);
}

.step-circle-completed {
    background: linear-gradient(135deg, #198754 0%, #157347 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(25, 135, 84, 0.3);
}

.step-circle-inactive {
    background: #e9ecef;
    color: #6c757d;
    border: 2px solid #dee2e6;
}

@media (max-width: 768px) {
    .risk-factors-list {
        grid-template-columns: 1fr;
    }
    
    .risk-factor-item {
        padding: 0.5rem;
    }
}
</style>