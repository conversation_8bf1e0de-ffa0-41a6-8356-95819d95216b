<div class="form-card">
    <!-- CSS for enhanced medical UI -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="<?= site_url('resources/css/protocol-steps.css') ?>">
    <link rel="stylesheet" href="<?= site_url('resources/css/medical-ui-enhanced.css') ?>">
    
    <div class="form-card-header">
        <h1 class="text-2xl font-semibold text-gray-900">STD Testing Consultation</h1>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">Step 3 of 4: Testing Protocol</p>
    </div>
    
    <!-- Enhanced Step Progress Bar -->
    <div class="form-card-header border-t border-gray-200">
        <div class="step-progress">
            <?php foreach ($steps as $stepNumber => $stepInfo): ?>
                <div class="flex flex-col items-center">
                    <div class="step-circle <?= $stepNumber === $currentStep ? 'step-circle-active' : ($stepNumber < $currentStep ? 'step-circle-completed' : 'step-circle-inactive') ?>">
                        <?php if ($stepNumber < $currentStep): ?>
                            <i class="fas fa-check"></i>
                        <?php else: ?>
                            <?= $stepNumber ?>
                        <?php endif; ?>
                    </div>
                    <span class="step-title <?= $stepNumber === $currentStep ? 'step-title-active' : ($stepNumber < $currentStep ? 'step-title-completed' : 'step-title-inactive') ?>">
                        <?= $stepInfo['title'] ?>
                    </span>
                </div>
                
                <?php if ($stepNumber < count($steps)): ?>
                    <div class="step-line <?= $stepNumber < $currentStep ? 'step-line-completed' : 'step-line-inactive' ?>"></div>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
    </div>
    
    <div class="form-card-body">
        <form method="POST" action="<?= base_url('std-testing/processStep') ?>" class="space-y-8">
            <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
            <input type="hidden" name="current_step" value="<?= $currentStep ?>">
            <input type="hidden" name="next_step" value="<?= $currentStep + 1 ?>">
            
            <!-- Enhanced Patient Summary -->
            <div class="medical-card">
                <div class="medical-card-header">
                    <div class="medical-card-icon" style="background: linear-gradient(135deg, #198754 0%, #157347 100%);">
                        <i class="fas fa-user-md"></i>
                    </div>
                    <h3 class="medical-card-title">Patient Summary</h3>
                </div>
                <div class="medical-card-body">
                    <div class="patient-summary-grid">
                        <div class="summary-item">
                            <div class="summary-label">
                                <i class="fas fa-birthday-cake text-primary me-2"></i>
                                <strong>Age:</strong>
                            </div>
                            <div class="summary-value"><?= e(isset($formData['patient_age']) ? $formData['patient_age'] : '') ?> years</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">
                                <i class="fas fa-venus-mars text-info me-2"></i>
                                <strong>Gender:</strong>
                            </div>
                            <div class="summary-value"><?= isset($formData['patient_gender']) ? ucfirst(e($formData['patient_gender'])) : '' ?></div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">
                                <i class="fas fa-history text-warning me-2"></i>
                                <strong>Last Testing:</strong>
                            </div>
                            <div class="summary-value">
                                <?php
                                $lastTestingMap = [
                                    'never' => 'Never tested before',
                                    'less_than_3_months' => 'Less than 3 months ago',
                                    '3_to_6_months' => '3-6 months ago',
                                    '6_to_12_months' => '6-12 months ago',
                                    'more_than_12_months' => 'More than 12 months ago'
                                ];
                                echo isset($formData['last_testing']) ? ($lastTestingMap[$formData['last_testing']] ?? $formData['last_testing']) : 'Unknown';
                                ?>
                            </div>
                        </div>
                        <div class="summary-item summary-item-full">
                            <div class="summary-label">
                                <i class="fas fa-stethoscope text-danger me-2"></i>
                                <strong>Symptoms:</strong>
                            </div>
                            <div class="summary-value"><?= isset($formData['symptoms']) ? (e($formData['symptoms']) ?: 'None reported') : 'None reported' ?></div>
                        </div>
                        <div class="summary-item summary-item-full">
                            <div class="summary-label">
                                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                <strong>Risk Factors:</strong>
                            </div>
                            <div class="summary-value">
                                <?php if (!empty($formData['risk_factors'])): ?>
                                    <div class="risk-factors-summary">
                                        <?php foreach ($formData['risk_factors'] as $riskFactor): ?>
                                            <span class="risk-factor-tag"><?= e($riskFactorOptions[$riskFactor]) ?></span>
                                        <?php endforeach; ?>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted">None selected</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Enhanced AI Recommendations -->
            <?php if (isset($recommendations) && !empty($recommendations)): ?>
            <div class="medical-card">
                <div class="medical-card-header">
                    <div class="medical-card-icon" style="background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3 class="medical-card-title">AI-Generated Recommendations</h3>
                </div>
                <div class="medical-card-body">
                    <div class="ai-recommendations">
                        <h4 class="recommendations-title">
                            <i class="fas fa-lightbulb me-2"></i>
                            Recommended Tests Based on Risk Assessment
                        </h4>
                        <div class="recommendations-list">
                            <?php foreach ($recommendations as $recommendation): ?>
                                <div class="recommendation-item">
                                    <div class="recommendation-icon">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div class="recommendation-text"><?= e($recommendation) ?></div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="recommendations-note">
                            <i class="fas fa-info-circle me-2"></i>
                            These recommendations are generated based on the patient's risk factors, symptoms, and current clinical guidelines.
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- Enhanced Test Selection -->
            <div class="medical-card">
                <div class="medical-card-header">
                    <div class="medical-card-icon" style="background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);">
                        <i class="fas fa-vial"></i>
                    </div>
                    <h3 class="medical-card-title">Test Selection</h3>
                </div>
                <div class="medical-card-body">
                    <div class="test-selection-grid">
                        <div class="test-selection-item">
                            <label for="test_type" class="form-label medical-heading-3">Test Type</label>
                            <select id="test_type" name="test_type" class="form-select form-control-lg" required>
                                <option value="">Select test type</option>
                                <option value="comprehensive" <?= isset($formData['test_type']) && $formData['test_type'] === 'comprehensive' ? 'selected' : '' ?>>
                                    🔬 Comprehensive STD Panel
                                </option>
                                <option value="basic" <?= isset($formData['test_type']) && $formData['test_type'] === 'basic' ? 'selected' : '' ?>>
                                    🧪 Basic STD Panel
                                </option>
                                <option value="hiv" <?= isset($formData['test_type']) && $formData['test_type'] === 'hiv' ? 'selected' : '' ?>>
                                    🩸 HIV Test
                                </option>
                                <option value="chlamydia_gonorrhea" <?= isset($formData['test_type']) && $formData['test_type'] === 'chlamydia_gonorrhea' ? 'selected' : '' ?>>
                                    🦠 Chlamydia & Gonorrhea
                                </option>
                                <option value="syphilis" <?= isset($formData['test_type']) && $formData['test_type'] === 'syphilis' ? 'selected' : '' ?>>
                                    🔍 Syphilis Test
                                </option>
                                <option value="herpes" <?= isset($formData['test_type']) && $formData['test_type'] === 'herpes' ? 'selected' : '' ?>>
                                    💊 Herpes Test
                                </option>
                                <option value="custom" <?= isset($formData['test_type']) && $formData['test_type'] === 'custom' ? 'selected' : '' ?>>
                                    ⚙️ Custom Panel
                                </option>
                            </select>
                            <small class="form-text text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Choose the most appropriate test based on risk assessment
                            </small>
                        </div>
                        
                        <div class="test-selection-item">
                            <label for="sample_type" class="form-label medical-heading-3">Sample Type</label>
                            <select id="sample_type" name="sample_type" class="form-select form-control-lg" required>
                                <option value="">Select sample type</option>
                                <option value="blood" <?= isset($formData['sample_type']) && $formData['sample_type'] === 'blood' ? 'selected' : '' ?>>
                                    🩸 Blood Sample
                                </option>
                                <option value="urine" <?= isset($formData['sample_type']) && $formData['sample_type'] === 'urine' ? 'selected' : '' ?>>
                                    🧪 Urine Sample
                                </option>
                                <option value="swab" <?= isset($formData['sample_type']) && $formData['sample_type'] === 'swab' ? 'selected' : '' ?>>
                                    🧽 Swab Sample
                                </option>
                                <option value="multiple" <?= isset($formData['sample_type']) && $formData['sample_type'] === 'multiple' ? 'selected' : '' ?>>
                                    📋 Multiple Samples
                                </option>
                            </select>
                            <small class="form-text text-muted">
                                <i class="fas fa-flask me-1"></i>
                                Sample type depends on the tests being performed
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Enhanced Pre-Test Counseling -->
            <div class="medical-card">
                <div class="medical-card-header">
                    <div class="medical-card-icon" style="background: linear-gradient(135deg, #e83e8c 0%, #d91a72 100%);">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h3 class="medical-card-title">Pre-Test Counseling</h3>
                </div>
                <div class="medical-card-body">
                    <div class="counseling-section">
                        <label for="counseling_notes" class="form-label medical-heading-3">Counseling Notes</label>
                        <textarea id="counseling_notes" name="counseling_notes" rows="4" 
                                  class="form-input form-control-lg"
                                  placeholder="Document pre-test counseling provided, including information about testing, window periods, confidentiality, and next steps..."><?= e(isset($formData['counseling_notes']) ? $formData['counseling_notes'] : '') ?></textarea>
                        <small class="form-text text-muted">
                            <i class="fas fa-clipboard-list me-1"></i>
                            Include discussion of testing process, window periods, confidentiality, and follow-up procedures
                        </small>
                    </div>
                    
                    <div class="consent-section">
                        <div class="consent-checkbox">
                            <div class="flex items-center">
                                <input id="consent_obtained" name="consent_obtained" type="checkbox" value="1" 
                                       <?= isset($formData['consent_obtained']) && $formData['consent_obtained'] ? 'checked' : '' ?> 
                                       class="form-checkbox h-5 w-5">
                                <div class="ml-3">
                                    <label for="consent_obtained" class="consent-label">
                                        <i class="fas fa-shield-check me-2"></i>
                                        Informed consent obtained
                                    </label>
                                    <p class="consent-description">
                                        Patient has been informed about the testing process, confidentiality, window periods, and has provided informed consent for testing.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="pt-5">
                <div class="flex justify-between">
                    <button type="submit" name="next_step" value="2" class="btn btn-outline-secondary btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>Previous Step
                    </button>
                    <button type="submit" class="btn btn-primary btn-lg">
                        Next Step<i class="fas fa-arrow-right ms-2"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<style>
/* Enhanced STD Testing Step 3 Styles */
.patient-summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.summary-item {
    padding: 1rem;
    background: rgba(0,0,0,0.02);
    border-radius: 8px;
    border-left: 4px solid #dee2e6;
}

.summary-item-full {
    grid-column: 1 / -1;
}

.summary-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.summary-value {
    color: #2c3e50;
    font-size: 1.1rem;
    line-height: 1.4;
}

.risk-factors-summary {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.risk-factor-tag {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background: #e9ecef;
    color: #495057;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.ai-recommendations {
    background: linear-gradient(135deg, #f8f5ff 0%, #ffffff 100%);
    border-radius: 12px;
    padding: 1.5rem;
    border: 2px solid #e9ecef;
}

.recommendations-title {
    color: #6f42c1;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.recommendations-list {
    margin-bottom: 1rem;
}

.recommendation-item {
    display: flex;
    align-items: flex-start;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background: rgba(111, 66, 193, 0.1);
    border-radius: 8px;
    border-left: 4px solid #6f42c1;
}

.recommendation-icon {
    color: #6f42c1;
    margin-right: 0.75rem;
    margin-top: 0.125rem;
}

.recommendation-text {
    color: #2c3e50;
    font-weight: 500;
    line-height: 1.4;
}

.recommendations-note {
    color: #6c757d;
    font-size: 0.9rem;
    font-style: italic;
    padding: 0.75rem;
    background: rgba(111, 66, 193, 0.05);
    border-radius: 6px;
}

.test-selection-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.test-selection-item {
    background: rgba(0,0,0,0.02);
    padding: 1.25rem;
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.2s ease;
}

.test-selection-item:hover {
    border-color: #0d6efd;
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.1);
}

.counseling-section {
    margin-bottom: 1.5rem;
}

.consent-section {
    background: rgba(232, 62, 140, 0.05);
    padding: 1.25rem;
    border-radius: 10px;
    border: 2px solid rgba(232, 62, 140, 0.2);
}

.consent-checkbox {
    display: flex;
    align-items: start;
}

.consent-label {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    display: block;
}

.consent-description {
    color: #6c757d;
    font-size: 0.95rem;
    line-height: 1.4;
    margin: 0;
}

/* Step progress enhancements */
.step-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.step-circle-active {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.3);
}

.step-circle-completed {
    background: linear-gradient(135deg, #198754 0%, #157347 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(25, 135, 84, 0.3);
}

.step-circle-inactive {
    background: #e9ecef;
    color: #6c757d;
    border: 2px solid #dee2e6;
}

@media (max-width: 768px) {
    .patient-summary-grid {
        grid-template-columns: 1fr;
    }
    
    .test-selection-grid {
        grid-template-columns: 1fr;
    }
    
    .summary-item-full {
        grid-column: 1;
    }
}
</style>