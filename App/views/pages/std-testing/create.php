<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <h1 class="text-2xl font-semibold text-gray-900">Create New STD Testing Consultation</h1>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">Record a new STD testing consultation</p>
    </div>
    
    <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
        <form method="POST" action="<?= base_url('std-testing/store') ?>" class="space-y-8">
            <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
            <input type="hidden" name="consultation_type" value="std_testing">
            
            <!-- Patient Selection -->
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Basic Information</h3>
                <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="patient_id" class="block text-sm font-medium text-gray-700">Patient</label>
                        <div class="mt-1">
                            <select id="patient_id" name="patient_id" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" required>
                                <option value="">Select patient</option>
                                <?php foreach ($patients as $patient): ?>
                                    <option value="<?= e($patient['id']) ?>" <?= isset($formData['patient_id']) && $formData['patient_id'] === $patient['id'] ? 'selected' : '' ?>>
                                        <?= e($patient['first_name'] . ' ' . $patient['last_name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="sm:col-span-3">
                        <label for="started_at" class="block text-sm font-medium text-gray-700">Date & Time</label>
                        <div class="mt-1">
                            <input type="datetime-local" id="started_at" name="started_at" 
                                   value="<?= isset($formData['started_at']) ? date('Y-m-d\TH:i', strtotime($formData['started_at'])) : date('Y-m-d\TH:i') ?>" 
                                   class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" 
                                   required>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Test Information -->
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Test Information</h3>
                <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="test_type" class="block text-sm font-medium text-gray-700">Test Type</label>
                        <div class="mt-1">
                            <select id="test_type" name="test_type" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" required>
                                <option value="">Select test type</option>
                                <option value="comprehensive" <?= isset($formData['test_type']) && $formData['test_type'] === 'comprehensive' ? 'selected' : '' ?>>Comprehensive STD Panel</option>
                                <option value="basic" <?= isset($formData['test_type']) && $formData['test_type'] === 'basic' ? 'selected' : '' ?>>Basic STD Panel</option>
                                <option value="hiv" <?= isset($formData['test_type']) && $formData['test_type'] === 'hiv' ? 'selected' : '' ?>>HIV Test</option>
                                <option value="chlamydia_gonorrhea" <?= isset($formData['test_type']) && $formData['test_type'] === 'chlamydia_gonorrhea' ? 'selected' : '' ?>>Chlamydia & Gonorrhea</option>
                                <option value="syphilis" <?= isset($formData['test_type']) && $formData['test_type'] === 'syphilis' ? 'selected' : '' ?>>Syphilis Test</option>
                                <option value="hepatitis" <?= isset($formData['test_type']) && $formData['test_type'] === 'hepatitis' ? 'selected' : '' ?>>Hepatitis Panel</option>
                                <option value="herpes" <?= isset($formData['test_type']) && $formData['test_type'] === 'herpes' ? 'selected' : '' ?>>Herpes Test</option>
                                <option value="custom" <?= isset($formData['test_type']) && $formData['test_type'] === 'custom' ? 'selected' : '' ?>>Custom Panel</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="sm:col-span-3">
                        <label for="sample_type" class="block text-sm font-medium text-gray-700">Sample Type</label>
                        <div class="mt-1">
                            <select id="sample_type" name="sample_type" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" required>
                                <option value="">Select sample type</option>
                                <option value="blood" <?= isset($formData['sample_type']) && $formData['sample_type'] === 'blood' ? 'selected' : '' ?>>Blood</option>
                                <option value="urine" <?= isset($formData['sample_type']) && $formData['sample_type'] === 'urine' ? 'selected' : '' ?>>Urine</option>
                                <option value="swab" <?= isset($formData['sample_type']) && $formData['sample_type'] === 'swab' ? 'selected' : '' ?>>Swab</option>
                                <option value="multiple" <?= isset($formData['sample_type']) && $formData['sample_type'] === 'multiple' ? 'selected' : '' ?>>Multiple Samples</option>
                            </select>
                        </div>
                    </div>
                    
                    <div id="custom_panel_container" class="sm:col-span-6 <?= isset($formData['test_type']) && $formData['test_type'] === 'custom' ? '' : 'hidden' ?>">
                        <label for="custom_panel" class="block text-sm font-medium text-gray-700">Custom Panel Details</label>
                        <div class="mt-1">
                            <textarea id="custom_panel" name="custom_panel" rows="3" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['custom_panel']) ? e($formData['custom_panel']) : '' ?></textarea>
                        </div>
                        <p class="mt-2 text-sm text-gray-500">Specify which tests to include in the custom panel.</p>
                    </div>
                </div>
            </div>
            
            <!-- Risk Assessment -->
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Risk Assessment</h3>
                <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-6">
                        <label for="risk_factors" class="block text-sm font-medium text-gray-700">Risk Factors</label>
                        <div class="mt-1">
                            <textarea id="risk_factors" name="risk_factors" rows="3" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['risk_factors']) ? e($formData['risk_factors']) : '' ?></textarea>
                        </div>
                        <p class="mt-2 text-sm text-gray-500">Document any relevant risk factors or exposures.</p>
                    </div>
                    
                    <div class="sm:col-span-6">
                        <label for="symptoms" class="block text-sm font-medium text-gray-700">Current Symptoms</label>
                        <div class="mt-1">
                            <textarea id="symptoms" name="symptoms" rows="3" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['symptoms']) ? e($formData['symptoms']) : '' ?></textarea>
                        </div>
                    </div>
                    
                    <div class="sm:col-span-6">
                        <label for="previous_testing" class="block text-sm font-medium text-gray-700">Previous STD Testing History</label>
                        <div class="mt-1">
                            <textarea id="previous_testing" name="previous_testing" rows="2" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['previous_testing']) ? e($formData['previous_testing']) : '' ?></textarea>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Counseling -->
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Pre-Test Counseling</h3>
                <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-6">
                        <label for="counseling_notes" class="block text-sm font-medium text-gray-700">Counseling Notes</label>
                        <div class="mt-1">
                            <textarea id="counseling_notes" name="counseling_notes" rows="4" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['counseling_notes']) ? e($formData['counseling_notes']) : '' ?></textarea>
                        </div>
                        <p class="mt-2 text-sm text-gray-500">Document pre-test counseling provided, including information about testing, window periods, and confidentiality.</p>
                    </div>
                    
                    <div class="sm:col-span-6">
                        <div class="relative flex items-start">
                            <div class="flex items-center h-5">
                                <input id="consent_obtained" name="consent_obtained" type="checkbox" value="1" <?= isset($formData['consent_obtained']) && $formData['consent_obtained'] ? 'checked' : '' ?> class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="consent_obtained" class="font-medium text-gray-700">Informed consent obtained</label>
                                <p class="text-gray-500">Patient has been informed about the testing process and has provided consent.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Notes -->
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Additional Notes</h3>
                <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-6">
                        <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                        <div class="mt-1">
                            <textarea id="notes" name="notes" rows="4" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['notes']) ? e($formData['notes']) : '' ?></textarea>
                        </div>
                    </div>
                </div>
            </div>

            <div class="pt-5">
                <div class="flex justify-end">
                    <a href="<?= base_url('std-testing') ?>" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Cancel
                    </a>
                    <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Create Consultation
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const testTypeSelect = document.getElementById('test_type');
    const customPanelContainer = document.getElementById('custom_panel_container');
    
    testTypeSelect.addEventListener('change', function() {
        if (this.value === 'custom') {
            customPanelContainer.classList.remove('hidden');
        } else {
            customPanelContainer.classList.add('hidden');
        }
    });
});
</script>
