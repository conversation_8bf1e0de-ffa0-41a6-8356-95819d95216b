<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <h1 class="text-2xl font-semibold text-gray-900">Edit Patient</h1>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">Update patient information for <?= e($patient['first_name'] . ' ' . $patient['last_name']) ?></p>
    </div>
    
    <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
        <form method="POST" action="<?= base_url('patients/' . $patient['id'] . '/edit') ?>" class="space-y-8">
            <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
            
            <!-- Personal Information -->
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Personal Information</h3>
                <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="first_name" class="block text-sm font-medium text-gray-700">First name</label>
                        <div class="mt-1">
                            <input type="text" name="first_name" id="first_name" value="<?= isset($formData['first_name']) ? e($formData['first_name']) : '' ?>" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" required>
                        </div>
                    </div>

                    <div class="sm:col-span-3">
                        <label for="last_name" class="block text-sm font-medium text-gray-700">Last name</label>
                        <div class="mt-1">
                            <input type="text" name="last_name" id="last_name" value="<?= isset($formData['last_name']) ? e($formData['last_name']) : '' ?>" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" required>
                        </div>
                    </div>

                    <div class="sm:col-span-3">
                        <label for="date_of_birth" class="block text-sm font-medium text-gray-700">Date of birth</label>
                        <div class="mt-1">
                            <input type="date" name="date_of_birth" id="date_of_birth" value="<?= isset($formData['date_of_birth']) ? e($formData['date_of_birth']) : '' ?>" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" required>
                        </div>
                    </div>

                    <div class="sm:col-span-3">
                        <label for="gender" class="block text-sm font-medium text-gray-700">Gender</label>
                        <div class="mt-1">
                            <select id="gender" name="gender" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" required>
                                <option value="">Select gender</option>
                                <option value="male" <?= (isset($formData['gender']) && $formData['gender'] === 'male') ? 'selected' : '' ?>>Male</option>
                                <option value="female" <?= (isset($formData['gender']) && $formData['gender'] === 'female') ? 'selected' : '' ?>>Female</option>
                                <option value="other" <?= (isset($formData['gender']) && $formData['gender'] === 'other') ? 'selected' : '' ?>>Other</option>
                                <option value="prefer_not_to_say" <?= (isset($formData['gender']) && $formData['gender'] === 'prefer_not_to_say') ? 'selected' : '' ?>>Prefer not to say</option>
                            </select>
                        </div>
                    </div>

                    <div class="sm:col-span-3">
                        <label for="email" class="block text-sm font-medium text-gray-700">Email address</label>
                        <div class="mt-1">
                            <input type="email" name="email" id="email" value="<?= isset($formData['email']) ? e($formData['email']) : '' ?>" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                    </div>

                    <div class="sm:col-span-3">
                        <label for="phone" class="block text-sm font-medium text-gray-700">Phone number</label>
                        <div class="mt-1">
                            <input type="text" name="phone" id="phone" value="<?= isset($formData['phone']) ? e($formData['phone']) : '' ?>" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Address -->
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Address</h3>
                <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-6">
                        <label for="address" class="block text-sm font-medium text-gray-700">Street address</label>
                        <div class="mt-1">
                            <input type="text" name="address" id="address" value="<?= isset($formData['address']) ? e($formData['address']) : '' ?>" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                    </div>

                    <div class="sm:col-span-2">
                        <label for="city" class="block text-sm font-medium text-gray-700">City</label>
                        <div class="mt-1">
                            <input type="text" name="city" id="city" value="<?= isset($formData['city']) ? e($formData['city']) : '' ?>" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                    </div>

                    <div class="sm:col-span-2">
                        <label for="state" class="block text-sm font-medium text-gray-700">State / Province</label>
                        <div class="mt-1">
                            <input type="text" name="state" id="state" value="<?= isset($formData['state']) ? e($formData['state']) : '' ?>" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                    </div>

                    <div class="sm:col-span-2">
                        <label for="postal_code" class="block text-sm font-medium text-gray-700">ZIP / Postal code</label>
                        <div class="mt-1">
                            <input type="text" name="postal_code" id="postal_code" value="<?= isset($formData['postal_code']) ? e($formData['postal_code']) : '' ?>" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                    </div>

                    <div class="sm:col-span-3">
                        <label for="country" class="block text-sm font-medium text-gray-700">Country</label>
                        <div class="mt-1">
                            <input type="text" name="country" id="country" value="<?= isset($formData['country']) ? e($formData['country']) : '' ?>" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Emergency Contact -->
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Emergency Contact</h3>
                <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="emergency_contact_name" class="block text-sm font-medium text-gray-700">Contact name</label>
                        <div class="mt-1">
                            <input type="text" name="emergency_contact_name" id="emergency_contact_name" value="<?= isset($formData['emergency_contact_name']) ? e($formData['emergency_contact_name']) : '' ?>" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                    </div>

                    <div class="sm:col-span-3">
                        <label for="emergency_contact_phone" class="block text-sm font-medium text-gray-700">Contact phone</label>
                        <div class="mt-1">
                            <input type="text" name="emergency_contact_phone" id="emergency_contact_phone" value="<?= isset($formData['emergency_contact_phone']) ? e($formData['emergency_contact_phone']) : '' ?>" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Insurance Information -->
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Insurance Information</h3>
                <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="insurance_provider" class="block text-sm font-medium text-gray-700">Insurance provider</label>
                        <div class="mt-1">
                            <input type="text" name="insurance_provider" id="insurance_provider" value="<?= isset($formData['insurance_provider']) ? e($formData['insurance_provider']) : '' ?>" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                    </div>

                    <div class="sm:col-span-3">
                        <label for="insurance_policy_number" class="block text-sm font-medium text-gray-700">Policy number</label>
                        <div class="mt-1">
                            <input type="text" name="insurance_policy_number" id="insurance_policy_number" value="<?= isset($formData['insurance_policy_number']) ? e($formData['insurance_policy_number']) : '' ?>" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Medical History -->
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Medical History</h3>
                <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-6">
                        <label for="allergies" class="block text-sm font-medium text-gray-700">Allergies</label>
                        <div class="mt-1">
                            <textarea id="allergies" name="allergies" rows="3" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['medical_history']['allergies']) ? e($formData['medical_history']['allergies']) : '' ?></textarea>
                        </div>
                        <p class="mt-2 text-sm text-gray-500">List any known allergies, including medications, foods, or environmental factors.</p>
                    </div>

                    <div class="sm:col-span-6">
                        <label for="current_medications" class="block text-sm font-medium text-gray-700">Current medications</label>
                        <div class="mt-1">
                            <textarea id="current_medications" name="current_medications" rows="3" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['medical_history']['current_medications']) ? e($formData['medical_history']['current_medications']) : '' ?></textarea>
                        </div>
                        <p class="mt-2 text-sm text-gray-500">List all current medications, including dosage and frequency.</p>
                    </div>

                    <div class="sm:col-span-6">
                        <label for="past_surgeries" class="block text-sm font-medium text-gray-700">Past surgeries</label>
                        <div class="mt-1">
                            <textarea id="past_surgeries" name="past_surgeries" rows="3" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['medical_history']['past_surgeries']) ? e($formData['medical_history']['past_surgeries']) : '' ?></textarea>
                        </div>
                        <p class="mt-2 text-sm text-gray-500">List any previous surgeries with dates if known.</p>
                    </div>

                    <div class="sm:col-span-6">
                        <label for="chronic_conditions" class="block text-sm font-medium text-gray-700">Chronic conditions</label>
                        <div class="mt-1">
                            <textarea id="chronic_conditions" name="chronic_conditions" rows="3" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['medical_history']['chronic_conditions']) ? e($formData['medical_history']['chronic_conditions']) : '' ?></textarea>
                        </div>
                        <p class="mt-2 text-sm text-gray-500">List any chronic health conditions.</p>
                    </div>

                    <div class="sm:col-span-6">
                        <label for="family_history" class="block text-sm font-medium text-gray-700">Family history</label>
                        <div class="mt-1">
                            <textarea id="family_history" name="family_history" rows="3" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"><?= isset($formData['medical_history']['family_history']) ? e($formData['medical_history']['family_history']) : '' ?></textarea>
                        </div>
                        <p class="mt-2 text-sm text-gray-500">List any relevant family medical history.</p>
                    </div>

                    <div class="sm:col-span-3">
                        <label for="smoking_status" class="block text-sm font-medium text-gray-700">Smoking status</label>
                        <div class="mt-1">
                            <select id="smoking_status" name="smoking_status" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                <option value="">Select status</option>
                                <option value="never" <?= (isset($formData['medical_history']['smoking_status']) && $formData['medical_history']['smoking_status'] === 'never') ? 'selected' : '' ?>>Never smoked</option>
                                <option value="former" <?= (isset($formData['medical_history']['smoking_status']) && $formData['medical_history']['smoking_status'] === 'former') ? 'selected' : '' ?>>Former smoker</option>
                                <option value="current" <?= (isset($formData['medical_history']['smoking_status']) && $formData['medical_history']['smoking_status'] === 'current') ? 'selected' : '' ?>>Current smoker</option>
                            </select>
                        </div>
                    </div>

                    <div class="sm:col-span-3">
                        <label for="alcohol_consumption" class="block text-sm font-medium text-gray-700">Alcohol consumption</label>
                        <div class="mt-1">
                            <select id="alcohol_consumption" name="alcohol_consumption" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                <option value="">Select consumption</option>
                                <option value="none" <?= (isset($formData['medical_history']['alcohol_consumption']) && $formData['medical_history']['alcohol_consumption'] === 'none') ? 'selected' : '' ?>>None</option>
                                <option value="occasional" <?= (isset($formData['medical_history']['alcohol_consumption']) && $formData['medical_history']['alcohol_consumption'] === 'occasional') ? 'selected' : '' ?>>Occasional</option>
                                <option value="moderate" <?= (isset($formData['medical_history']['alcohol_consumption']) && $formData['medical_history']['alcohol_consumption'] === 'moderate') ? 'selected' : '' ?>>Moderate</option>
                                <option value="heavy" <?= (isset($formData['medical_history']['alcohol_consumption']) && $formData['medical_history']['alcohol_consumption'] === 'heavy') ? 'selected' : '' ?>>Heavy</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="pt-5">
                <div class="flex justify-end">
                    <a href="<?= base_url('patients/' . $patient['id']) ?>" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Cancel
                    </a>
                    <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Save
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
