<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900"><?= e($patient['first_name'] . ' ' . $patient['last_name']) ?></h1>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">Patient ID: <?= substr($patient['id'], 0, 8) ?></p>
        </div>
        <div class="flex space-x-3">
            <a href="<?= base_url('patients/' . $patient['id'] . '/edit') ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
                Edit
            </a>
            <a href="<?= base_url('appointments/create?patient_id=' . $patient['id']) ?>" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                New Appointment
            </a>
        </div>
    </div>
    
    <!-- Tabs -->
    <div class="border-b border-gray-200">
        <nav class="-mb-px flex" aria-label="Tabs">
            <a href="#overview" class="tab-link border-blue-500 text-blue-600 whitespace-nowrap py-4 px-6 border-b-2 font-medium text-sm" data-tab="overview">
                Overview
            </a>
            <a href="#appointments" class="tab-link border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-6 border-b-2 font-medium text-sm" data-tab="appointments">
                Appointments
            </a>
            <a href="#consultations" class="tab-link border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-6 border-b-2 font-medium text-sm" data-tab="consultations">
                Consultations
            </a>
            <a href="#documents" class="tab-link border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-6 border-b-2 font-medium text-sm" data-tab="documents">
                Documents
            </a>
        </nav>
    </div>
    
    <!-- Tab Content -->
    <div class="tab-content" id="overview">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Patient Information</h3>
        </div>
        <div class="border-t border-gray-200">
            <dl>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Full name</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2"><?= e($patient['first_name'] . ' ' . $patient['last_name']) ?></dd>
                </div>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Date of birth</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        <?= $patient['date_of_birth'] ? format_date($patient['date_of_birth'], 'd M Y') . ' (' . calculate_age($patient['date_of_birth']) . ' years)' : 'N/A' ?>
                    </dd>
                </div>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Gender</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2"><?= ucfirst($patient['gender'] ?: 'N/A') ?></dd>
                </div>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Email address</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2"><?= e($patient['email'] ?: 'N/A') ?></dd>
                </div>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Phone number</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2"><?= e($patient['phone'] ?: 'N/A') ?></dd>
                </div>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Address</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        <?php
                        $addressParts = [];
                        if (!empty($patient['address'])) $addressParts[] = $patient['address'];
                        if (!empty($patient['city'])) $addressParts[] = $patient['city'];
                        if (!empty($patient['state'])) $addressParts[] = $patient['state'];
                        if (!empty($patient['postal_code'])) $addressParts[] = $patient['postal_code'];
                        if (!empty($patient['country'])) $addressParts[] = $patient['country'];
                        
                        echo !empty($addressParts) ? e(implode(', ', $addressParts)) : 'N/A';
                        ?>
                    </dd>
                </div>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Emergency contact</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        <?php if (!empty($patient['emergency_contact_name']) || !empty($patient['emergency_contact_phone'])): ?>
                            <?= e($patient['emergency_contact_name'] ?: 'N/A') ?> - <?= e($patient['emergency_contact_phone'] ?: 'N/A') ?>
                        <?php else: ?>
                            N/A
                        <?php endif; ?>
                    </dd>
                </div>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Insurance</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        <?php if (!empty($patient['insurance_provider']) || !empty($patient['insurance_policy_number'])): ?>
                            <?= e($patient['insurance_provider'] ?: 'N/A') ?> - Policy #: <?= e($patient['insurance_policy_number'] ?: 'N/A') ?>
                        <?php else: ?>
                            N/A
                        <?php endif; ?>
                    </dd>
                </div>
            </dl>
        </div>
        
        <!-- Medical History -->
        <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Medical History</h3>
        </div>
        <div class="border-t border-gray-200">
            <dl>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Allergies</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        <?= !empty($patient['medical_history']['allergies']) ? nl2br(e($patient['medical_history']['allergies'])) : 'None reported' ?>
                    </dd>
                </div>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Current medications</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        <?= !empty($patient['medical_history']['current_medications']) ? nl2br(e($patient['medical_history']['current_medications'])) : 'None reported' ?>
                    </dd>
                </div>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Past surgeries</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        <?= !empty($patient['medical_history']['past_surgeries']) ? nl2br(e($patient['medical_history']['past_surgeries'])) : 'None reported' ?>
                    </dd>
                </div>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Chronic conditions</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        <?= !empty($patient['medical_history']['chronic_conditions']) ? nl2br(e($patient['medical_history']['chronic_conditions'])) : 'None reported' ?>
                    </dd>
                </div>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Family history</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        <?= !empty($patient['medical_history']['family_history']) ? nl2br(e($patient['medical_history']['family_history'])) : 'None reported' ?>
                    </dd>
                </div>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Smoking status</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        <?= !empty($patient['medical_history']['smoking_status']) ? ucfirst(e($patient['medical_history']['smoking_status'])) : 'Not specified' ?>
                    </dd>
                </div>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Alcohol consumption</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        <?= !empty($patient['medical_history']['alcohol_consumption']) ? ucfirst(e($patient['medical_history']['alcohol_consumption'])) : 'Not specified' ?>
                    </dd>
                </div>
            </dl>
        </div>
    </div>
    
    <div class="tab-content hidden" id="appointments">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Appointments</h3>
        </div>
        <?php if (empty($appointments)): ?>
            <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
                <p class="text-center text-gray-500 py-8">No appointments found for this patient.</p>
            </div>
        <?php else: ?>
            <div class="flex flex-col">
                <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                    <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                        <div class="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Date & Time
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Doctor
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Reason
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Status
                                        </th>
                                        <th scope="col" class="relative px-6 py-3">
                                            <span class="sr-only">Actions</span>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php foreach ($appointments as $appointment): ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900"><?= format_date($appointment['appointment_date'], 'd M Y') ?></div>
                                                <div class="text-sm text-gray-500"><?= format_date($appointment['appointment_date'], 'H:i') ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900">
                                                    <?= isset($appointment['doctor_first_name'], $appointment['doctor_last_name']) ? e($appointment['doctor_first_name'] . ' ' . $appointment['doctor_last_name']) : 'N/A' ?>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="text-sm text-gray-900"><?= e($appointment['reason_for_visit']) ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php
                                                $statusClass = 'bg-gray-100 text-gray-800';
                                                if ($appointment['status'] === 'scheduled') $statusClass = 'bg-green-100 text-green-800';
                                                if ($appointment['status'] === 'completed') $statusClass = 'bg-blue-100 text-blue-800';
                                                if ($appointment['status'] === 'cancelled') $statusClass = 'bg-red-100 text-red-800';
                                                if ($appointment['status'] === 'no_show') $statusClass = 'bg-yellow-100 text-yellow-800';
                                                ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?= $statusClass ?>">
                                                    <?= ucfirst($appointment['status']) ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <a href="<?= base_url('appointments/' . $appointment['id']) ?>" class="text-blue-600 hover:text-blue-900">View</a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="tab-content hidden" id="consultations">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Consultations</h3>
        </div>
        <?php if (empty($consultations)): ?>
            <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
                <p class="text-center text-gray-500 py-8">No consultations found for this patient.</p>
            </div>
        <?php else: ?>
            <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <?php foreach ($consultations as $consultation): ?>
                        <div class="bg-white shadow overflow-hidden sm:rounded-lg border border-gray-200">
                            <div class="px-4 py-4 sm:px-6">
                                <div class="flex items-center justify-between">
                                    <h3 class="text-lg leading-6 font-medium text-gray-900">
                                        <?php
                                        $typeLabel = 'Consultation';
                                        $typeColor = 'text-gray-800';
                                        if ($consultation['consultation_type'] === 'travel_medicine') {
                                            $typeLabel = 'Travel Medicine';
                                            $typeColor = 'text-blue-800';
                                        } elseif ($consultation['consultation_type'] === 'std_testing') {
                                            $typeLabel = 'STD Testing';
                                            $typeColor = 'text-green-800';
                                        } elseif ($consultation['consultation_type'] === 'general_qa') {
                                            $typeLabel = 'Clinical Q&A';
                                            $typeColor = 'text-purple-800';
                                        }
                                        ?>
                                        <span class="<?= $typeColor ?>"><?= $typeLabel ?></span>
                                    </h3>
                                    <p class="text-sm text-gray-500">
                                        <?= format_date($consultation['created_at'], 'd M Y') ?>
                                    </p>
                                </div>
                                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                                    Doctor: <?= isset($consultation['doctor_first_name'], $consultation['doctor_last_name']) ? e($consultation['doctor_first_name'] . ' ' . $consultation['doctor_last_name']) : 'N/A' ?>
                                </p>
                            </div>
                            <div class="border-t border-gray-200 px-4 py-4 sm:px-6">
                                <div class="flex justify-between">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $consultation['status'] === 'completed' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800' ?>">
                                        <?= ucfirst($consultation['status']) ?>
                                    </span>
                                    <a href="<?= base_url('consultations/' . $consultation['id']) ?>" class="text-sm font-medium text-blue-600 hover:text-blue-500">
                                        View details
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="tab-content hidden" id="documents">
        <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Documents</h3>
            <a href="<?= base_url('documents/create?patient_id=' . $patient['id']) ?>" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Upload Document
            </a>
        </div>
        <?php if (empty($documents)): ?>
            <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
                <p class="text-center text-gray-500 py-8">No documents found for this patient.</p>
            </div>
        <?php else: ?>
            <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
                <ul role="list" class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2 sm:gap-x-6 lg:grid-cols-3 xl:gap-x-8">
                    <?php foreach ($documents as $document): ?>
                        <li class="relative">
                            <div class="group block w-full aspect-w-10 aspect-h-7 rounded-lg bg-gray-100 overflow-hidden">
                                <?php
                                $fileExtension = pathinfo($document['file_path'], PATHINFO_EXTENSION);
                                $isImage = in_array(strtolower($fileExtension), ['jpg', 'jpeg', 'png', 'gif']);
                                ?>
                                
                                <?php if ($isImage): ?>
                                    <img src="<?= base_url('uploads/' . $document['file_path']) ?>" alt="<?= e($document['title']) ?>" class="object-cover pointer-events-none">
                                <?php else: ?>
                                    <div class="flex items-center justify-center h-full">
                                        <svg class="h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                        </svg>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="absolute inset-0 focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                    <a href="<?= base_url('uploads/' . $document['file_path']) ?>" target="_blank" class="absolute inset-0 focus:outline-none">
                                        <span class="sr-only">View <?= e($document['title']) ?></span>
                                    </a>
                                </div>
                            </div>
                            <p class="mt-2 block text-sm font-medium text-gray-900 truncate pointer-events-none"><?= e($document['title']) ?></p>
                            <p class="block text-sm font-medium text-gray-500 pointer-events-none"><?= format_date($document['created_at'], 'd M Y') ?></p>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab functionality
    const tabLinks = document.querySelectorAll('.tab-link');
    const tabContents = document.querySelectorAll('.tab-content');
    
    function showTab(tabId) {
        // Hide all tabs
        tabContents.forEach(tab => {
            tab.classList.add('hidden');
        });
        
        // Remove active class from all tab links
        tabLinks.forEach(link => {
            link.classList.remove('border-blue-500', 'text-blue-600');
            link.classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
        });
        
        // Show the selected tab
        document.getElementById(tabId).classList.remove('hidden');
        
        // Add active class to the clicked tab link
        document.querySelector(`[data-tab="${tabId}"]`).classList.add('border-blue-500', 'text-blue-600');
        document.querySelector(`[data-tab="${tabId}"]`).classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
    }
    
    // Add click event to tab links
    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const tabId = this.getAttribute('data-tab');
            showTab(tabId);
            
            // Update URL hash
            window.location.hash = tabId;
        });
    });
    
    // Check for hash in URL
    if (window.location.hash) {
        const tabId = window.location.hash.substring(1);
        if (document.getElementById(tabId)) {
            showTab(tabId);
        }
    }
});
</script>
