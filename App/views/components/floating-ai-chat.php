<?php
/**
 * Floating AI Chat Component
 * This component adds a floating AI chat icon that expands into a chat interface
 * similar to the Clinical Q&A but available on all pages.
 */
?>

<!-- Floating AI Chat Button -->
<div id="floatingAiChat" class="fixed bottom-6 right-6 z-50">
    <!-- Chat Icon Button -->
    <button id="aiChatButton" class="ai-chat-button flex items-center justify-center rounded-full shadow-lg transition-all duration-300 ease-in-out">
        <i class="fas fa-robot text-white text-xl"></i>
    </button>
    
    <!-- Chat Panel (Hidden by default) -->
    <div id="aiChatPanel" class="ai-chat-panel hidden">
        <!-- Chat Header -->
        <div class="ai-chat-header">
            <div class="flex items-center">
                <div class="ai-chat-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-white font-medium">AI Clinical Assistant</h3>
                    <p class="text-blue-100 text-xs">Ask me anything about medical protocols</p>
                    <span class="bg-yellow-500 text-xs text-black px-2 py-0.5 rounded-full font-medium">Concise Mode</span>
                </div>
            </div>
            <button id="aiChatClose" class="text-white hover:text-blue-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <!-- Chat Messages -->
        <div id="aiChatMessages" class="ai-chat-messages">
            <!-- Welcome Message -->
            <div class="message assistant-message">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <div class="message-text">
                        Hello! I'm your AI clinical assistant. Ask me any questions about Dutch medical protocols or guidelines. I'll provide concise, direct answers to help you quickly.
                    </div>
                    <div class="message-time">
                        <?= date('g:i A') ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Chat Input -->
        <div class="ai-chat-input">
            <form id="aiChatForm" class="flex items-end">
                <textarea id="aiQuestionInput" 
                          placeholder="Type your clinical question..." 
                          class="ai-input-field"
                          rows="2"
                          required></textarea>
                <button type="submit" class="ai-send-btn" id="aiSendBtn">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </form>
        </div>
    </div>
</div>

<style>
/* Floating AI Chat Styles */
.ai-chat-button {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
    box-shadow: 0 4px 12px rgba(111, 66, 193, 0.4);
    transform-origin: bottom right;
    transition: transform 0.3s ease;
}

.ai-chat-button:hover {
    transform: scale(1.1);
}

.ai-chat-button.active {
    transform: scale(0.9);
}

.ai-chat-panel {
    position: absolute;
    bottom: 75px;
    right: 0;
    width: 350px;
    height: 500px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: all 0.3s ease;
}

.ai-chat-panel.hidden {
    display: none !important;
}

.ai-chat-header {
    background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ai-chat-avatar {
    width: 36px;
    height: 36px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
}

.ai-chat-messages {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
    background: #f8f9fa;
}

.ai-chat-input {
    padding: 10px 15px;
    border-top: 1px solid #e9ecef;
    background: white;
}

.ai-input-field {
    flex: 1;
    border: 1px solid #e9ecef;
    border-radius: 18px;
    padding: 10px 15px;
    font-size: 14px;
    resize: none;
    max-height: 100px;
    min-height: 40px;
    margin-right: 10px;
}

.ai-input-field:focus {
    outline: none;
    border-color: #6f42c1;
    box-shadow: 0 0 0 2px rgba(111, 66, 193, 0.1);
}

.ai-send-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.ai-send-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(111, 66, 193, 0.3);
}

.ai-send-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Message Styles */
.message {
    display: flex;
    margin-bottom: 15px;
    animation: fadeInUp 0.3s ease;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    flex-shrink: 0;
}

.assistant-message .message-avatar {
    background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
    color: white;
}

.user-message {
    flex-direction: row-reverse;
}

.user-message .message-avatar {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    color: white;
    margin-right: 0;
    margin-left: 10px;
}

.message-content {
    flex: 1;
    max-width: 80%;
}

.user-message .message-content {
    text-align: right;
}

.message-text {
    background: white;
    padding: 10px 15px;
    border-radius: 18px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.1);
    color: #2c3e50;
    line-height: 1.4;
    font-size: 14px;
    margin-bottom: 4px;
}

.user-message .message-text {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    color: white;
}

.message-time {
    font-size: 11px;
    color: #6c757d;
    padding: 0 5px;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 10px 15px;
    background: white;
    border-radius: 18px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.1);
}

.typing-dots {
    display: flex;
    gap: 3px;
}

.typing-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #6c757d;
    animation: typingDot 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typingDot {
    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .ai-chat-panel {
        width: calc(100vw - 24px);
        right: -12px;
        height: 60vh;
    }
}
</style>
