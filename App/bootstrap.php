<?php
/**
 * AI Doctor - Clinical Decision Support System
 * Bootstrap file for application initialization
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', DEBUG_MODE ? 1 : 0);

// Session start
session_start();

// Autoloader function
spl_autoload_register(function ($className) {
    // Convert namespace to file path
    $className = str_replace('\\', DIRECTORY_SEPARATOR, $className);
    
    $file = APP_ROOT . DIRECTORY_SEPARATOR . $className . '.php';
    
    if (file_exists($file)) {
        require_once $file;
    }
});

// Load helper functions
require_once APP_ROOT . '/App/helpers.php';

// Initialize database connection
\App\Core\Database::getInstance();
