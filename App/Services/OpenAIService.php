<?php

namespace App\Services;

/**
 * OpenAI Service for generating AI-powered recommendations
 */
class OpenAIService
{
    private $apiKey;
    private $apiUrl = 'https://api.openai.com/v1/chat/completions';

    /**
     * Constructor
     */
    public function __construct()
    {
        // Get API key from environment or config
        $this->apiKey = getenv('OPENAI_API_KEY') ?: (defined('OPENAI_API_KEY') ? OPENAI_API_KEY : '');

        if (empty($this->apiKey)) {
            // Log warning but don't throw error to allow fallback behavior
            error_log('Warning: OpenAI API key not set. AI recommendations will use fallback mode.');
        }
    }

    /**
     * Generate STD testing recommendations based on patient data
     * 
     * @param array $patientData Patient data including age, gender, symptoms, etc.
     * @return array Array of recommendations
     */
    public function generateSTDTestingRecommendations($patientData)
    {
        // If no API key, use fallback recommendations
        if (empty($this->apiKey)) {
            return $this->getFallbackRecommendations($patientData);
        }

        try {
            // Construct prompt for OpenAI
            $prompt = $this->constructSTDTestingPrompt($patientData);

            // Call OpenAI API
            $response = $this->callOpenAI($prompt, [
                'model' => 'gpt-3.5-turbo',
                'temperature' => 0.3,
                'max_tokens' => 500
            ]);

            // Parse recommendations from response
            return $this->parseRecommendations($response);
        } catch (\Exception $e) {
            error_log('Error generating AI recommendations: ' . $e->getMessage());
            return $this->getFallbackRecommendations($patientData);
        }
    }

    /**
     * Construct a prompt for STD testing recommendations
     * 
     * @param array $patientData Patient data
     * @return string Constructed prompt
     */
    private function constructSTDTestingPrompt($patientData)
    {
        // Extract patient data
        $age = $patientData['patient_age'] ?? 'unknown';
        $gender = $patientData['patient_gender'] ?? 'unknown';
        $lastTesting = $patientData['last_testing'] ?? 'unknown';
        $symptoms = $patientData['symptoms'] ?? 'none';
        $exposureHistory = $patientData['exposure_history'] ?? 'none';

        // Format risk factors
        $riskFactors = 'none';
        if (!empty($patientData['risk_factors'])) {
            $riskFactorLabels = [
                'multiple_partners' => 'Multiple sexual partners',
                'unprotected_sex' => 'Unprotected sexual contact',
                'partner_with_std' => 'Partner with known STD',
                'sex_work' => 'Sex work involvement',
                'msm' => 'Men who have sex with men',
                'iv_drug_use' => 'Intravenous drug use',
                'previous_std' => 'Previous STD history'
            ];

            $formattedRiskFactors = [];
            foreach ($patientData['risk_factors'] as $factor) {
                if (isset($riskFactorLabels[$factor])) {
                    $formattedRiskFactors[] = $riskFactorLabels[$factor];
                }
            }

            if (!empty($formattedRiskFactors)) {
                $riskFactors = implode(', ', $formattedRiskFactors);
            }
        }

        // Construct the prompt
        return "You are a clinical decision support system for STD testing. " .
            "Based on the following patient information, provide a list of recommended STD tests and clinical guidance. " .
            "Format your response as a numbered list of specific recommendations.\n\n" .
            "Patient Information:\n" .
            "- Age: {$age}\n" .
            "- Gender: {$gender}\n" .
            "- Last STD Testing: {$lastTesting}\n" .
            "- Symptoms: {$symptoms}\n" .
            "- Exposure History: {$exposureHistory}\n" .
            "- Risk Factors: {$riskFactors}\n\n" .
            "Provide 3-7 specific recommendations for STD testing and clinical management:";
    }



    /**
     * Parse recommendations from OpenAI response
     * 
     * @param string $response JSON response from OpenAI
     * @return array Array of recommendations
     */
    private function parseRecommendations($response)
    {
        $decoded = json_decode($response, true);

        if (!isset($decoded['choices'][0]['message']['content'])) {
            throw new \Exception("Invalid response format from OpenAI");
        }

        $content = $decoded['choices'][0]['message']['content'];

        // Extract numbered list items
        $recommendations = [];
        $lines = explode("\n", $content);

        foreach ($lines as $line) {
            // Match numbered list items (1. 2. etc.) or bullet points
            if (preg_match('/^(\d+\.|\*|-)\s+(.+)$/', trim($line), $matches)) {
                $recommendations[] = trim($matches[2]);
            }
        }

        // If no recommendations were found, use the whole content
        if (empty($recommendations)) {
            // Split by sentences as a fallback
            $sentences = preg_split('/(?<=[.!?])\s+/', $content);
            foreach ($sentences as $sentence) {
                $sentence = trim($sentence);
                if (!empty($sentence)) {
                    $recommendations[] = $sentence;
                }
            }
        }

        return $recommendations;
    }

    /**
     * Get fallback recommendations when API is not available
     * 
     * @param array $patientData Patient data
     * @return array Array of recommendations
     */
    private function getFallbackRecommendations($patientData)
    {
        // Basic recommendations that apply to most cases
        $recommendations = [
            'Complete STD panel including HIV, syphilis, gonorrhea, and chlamydia testing'
        ];

        // Add specific recommendations based on risk factors
        if (!empty($patientData['risk_factors'])) {
            if (in_array('msm', $patientData['risk_factors'])) {
                $recommendations[] = 'Throat and rectal swabs for gonorrhea and chlamydia';
                $recommendations[] = 'Consider PrEP evaluation for HIV prevention';
            }

            if (in_array('multiple_partners', $patientData['risk_factors'])) {
                $recommendations[] = 'Hepatitis B and C screening';
            }

            if (in_array('previous_std', $patientData['risk_factors'])) {
                $recommendations[] = 'Follow-up testing in 3 months to confirm clearance';
            }

            if (in_array('partner_with_std', $patientData['risk_factors'])) {
                $recommendations[] = 'Expedited partner therapy consideration';
            }
        }

        // Add recommendations based on symptoms
        if (!empty($patientData['symptoms'])) {
            $recommendations[] = 'Expedited testing and treatment based on symptomatic presentation';
        }

        // Add recommendations based on last testing
        if (isset($patientData['last_testing']) && ($patientData['last_testing'] === 'never' || $patientData['last_testing'] === 'more_than_12_months')) {
            $recommendations[] = 'Full comprehensive panel recommended due to extended time since last testing';
        }

        return $recommendations;
    }

    /**
     * Generate travel medicine recommendations based on destination and patient data
     * 
     * @param array $travelData Travel and patient data
     * @return array Array with risk assessment and vaccination recommendations
     */
    public function generateTravelMedicineRecommendations($travelData)
    {
        // If no API key, use fallback recommendations
        if (empty($this->apiKey)) {
            return $this->getFallbackTravelRecommendations($travelData);
        }

        try {
            // Construct prompt for travel medicine
            $prompt = $this->constructTravelMedicinePrompt($travelData);

            // Call OpenAI API
            $response = $this->callOpenAI($prompt, [
                'model' => 'gpt-4',
                'temperature' => 0.2,
                'max_tokens' => 1200
            ]);

            // Parse travel medicine response
            return $this->parseTravelMedicineResponse($response, $travelData);
        } catch (\Exception $e) {
            error_log('Error generating travel medicine recommendations: ' . $e->getMessage());
            return $this->getFallbackTravelRecommendations($travelData);
        }
    }

    /**
     * Construct a prompt for travel medicine recommendations
     * 
     * @param array $travelData Travel and patient data
     * @return string Constructed prompt
     */
    private function constructTravelMedicinePrompt($travelData)
    {
        $destination = $travelData['destination'] ?? 'unknown';
        $duration = $travelData['duration'] ?? 'unknown';
        $travelType = $travelData['travel_type'] ?? 'tourism';
        $departureDate = $travelData['departure_date'] ?? 'unknown';
        $age = $travelData['age'] ?? 'unknown';
        $gender = $travelData['gender'] ?? 'unknown';
        $pregnancyStatus = $travelData['pregnancy_status'] ?? 'no';
        $allergies = $travelData['allergies'] ?? 'none';

        // Fix pregnancy status logic - only include if female
        $pregnancyInfo = '';
        if (strtolower($gender) === 'female') {
            $pregnancyInfo = "- Pregnancy Status: {$pregnancyStatus}\n";
        }

        return "You are a clinical decision support system for Dutch travel medicine following LCR guidelines. " .
            "Provide comprehensive travel health recommendations for the following trip:\n\n" .
            "Travel Information:\n" .
            "- Destination: {$destination}\n" .
            "- Duration: {$duration}\n" .
            "- Travel Type: {$travelType}\n" .
            "- Departure Date: {$departureDate}\n\n" .
            "Patient Information:\n" .
            "- Age: {$age}\n" .
            "- Gender: {$gender}\n" .
            $pregnancyInfo .
            "- Allergies: {$allergies}\n\n" .
            "Please provide:\n" .
            "1. **Risk Assessment**: Health risks specific to this destination\n" .
            "2. **Required Vaccinations**: Mandatory vaccines for entry\n" .
            "3. **Recommended Vaccinations**: Additional vaccines based on risk\n" .
            "4. **Malaria Prevention**: If applicable to destination\n" .
            "5. **General Health Advice**: Food safety, water precautions, etc.\n" .
            "6. **Special Considerations**: Based on patient demographics\n\n" .
            "Base recommendations on current LCR (Dutch Travel Health Guidelines) and include specific references.";
    }

    /**
     * Parse travel medicine response from OpenAI
     * 
     * @param string $response JSON response from OpenAI
     * @param array $travelData Original travel data
     * @return array Parsed travel recommendations
     */
    private function parseTravelMedicineResponse($response, $travelData)
    {
        $decoded = json_decode($response, true);

        if (!isset($decoded['choices'][0]['message']['content'])) {
            throw new \Exception("Invalid response format from OpenAI");
        }

        $content = $decoded['choices'][0]['message']['content'];

        // Debug logging
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('OpenAI Travel Response Content: ' . $content);
        }

        // Try multiple extraction approaches
        $sections = [
            'risk_assessment' => $this->extractSection($content, 'Risk Assessment'),
            'required_vaccinations' => $this->extractSection($content, 'Required Vaccinations'),
            'recommended_vaccinations' => $this->extractSection($content, 'Recommended Vaccinations'),
            'malaria_prevention' => $this->extractSection($content, 'Malaria Prevention'),
            'general_advice' => $this->extractSection($content, 'General Health Advice'),
            'special_considerations' => $this->extractSection($content, 'Special Considerations')
        ];

        // If extraction failed, try alternative section names
        if ($sections['risk_assessment'] === 'Information not available in AI response') {
            $sections['risk_assessment'] = $this->extractSection($content, 'Health Risks');
        }
        if ($sections['general_advice'] === 'Information not available in AI response') {
            $sections['general_advice'] = $this->extractSection($content, 'General Advice');
        }

        // If still no good sections, try to split by numbered points
        if (array_filter($sections, function ($s) {
            return $s !== 'Information not available in AI response';
        }) < 2) {
            $numberedSections = $this->extractNumberedSections($content);
            if (!empty($numberedSections)) {
                $sections = array_merge($sections, $numberedSections);
            }
        }

        return [
            'destination' => $travelData['destination'],
            'risk_assessment' => $sections['risk_assessment'],
            'required_vaccinations' => $sections['required_vaccinations'],
            'recommended_vaccinations' => $sections['recommended_vaccinations'],
            'malaria_prevention' => $sections['malaria_prevention'],
            'general_advice' => $sections['general_advice'],
            'special_considerations' => $sections['special_considerations'],
            'full_response' => $content,
            'timestamp' => date('Y-m-d H:i:s'),
            'model_used' => $decoded['model'] ?? 'unknown',
            'sources' => $this->extractSources($content)
        ];
    }

    /**
     * Extract numbered sections from content
     * 
     * @param string $content Full response content
     * @return array Extracted sections
     */
    private function extractNumberedSections($content)
    {
        $sections = [];

        // Split by numbered points (1. 2. 3. etc.)
        if (preg_match_all('/(\d+)\.\s*\*\*([^*]+)\*\*:?\s*([^0-9]+?)(?=\d+\.\s*\*\*|$)/s', $content, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                $sectionTitle = trim($match[2]);
                $sectionContent = trim($match[3]);

                // Map section titles to our expected keys
                if (stripos($sectionTitle, 'Risk') !== false || stripos($sectionTitle, 'Health Risk') !== false) {
                    $sections['risk_assessment'] = $sectionContent;
                } elseif (stripos($sectionTitle, 'Required') !== false && stripos($sectionTitle, 'Vaccination') !== false) {
                    $sections['required_vaccinations'] = $sectionContent;
                } elseif (stripos($sectionTitle, 'Recommended') !== false && stripos($sectionTitle, 'Vaccination') !== false) {
                    $sections['recommended_vaccinations'] = $sectionContent;
                } elseif (stripos($sectionTitle, 'Malaria') !== false) {
                    $sections['malaria_prevention'] = $sectionContent;
                } elseif (stripos($sectionTitle, 'General') !== false || stripos($sectionTitle, 'Health Advice') !== false) {
                    $sections['general_advice'] = $sectionContent;
                } elseif (stripos($sectionTitle, 'Special') !== false || stripos($sectionTitle, 'Consideration') !== false) {
                    $sections['special_considerations'] = $sectionContent;
                }
            }
        }

        return $sections;
    }

    /**
     * Extract a specific section from the AI response
     * 
     * @param string $content Full response content
     * @param string $sectionName Section to extract
     * @return string Extracted section content
     */
    private function extractSection($content, $sectionName)
    {
        // Look for section headers with various formats
        $patterns = [
            // Pattern 1: **Section Name**: content
            '/\*\*' . preg_quote($sectionName, '/') . '\*\*:?\s*(.*?)(?=\d+\.\s*\*\*|\*\*[A-Z]|$)/s',
            // Pattern 2: 1. **Section Name**: content
            '/\d+\.\s*\*\*' . preg_quote($sectionName, '/') . '\*\*:?\s*(.*?)(?=\d+\.\s*\*\*|$)/s',
            // Pattern 3: Section Name: content (without asterisks)
            '/' . preg_quote($sectionName, '/') . ':?\s*(.*?)(?=\n[A-Z][a-z]+:|$)/s',
            // Pattern 4: More flexible pattern for numbered sections
            '/\d+\.\s*\*\*' . preg_quote($sectionName, '/') . '\*\*:?\s*(.*?)(?=\d+\.\s*\*\*|Please note|$)/s'
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $content, $matches)) {
                $extracted = trim($matches[1]);
                // Clean up the extracted content
                $extracted = preg_replace('/^\s*:\s*/', '', $extracted); // Remove leading colon
                $extracted = trim($extracted);

                if (!empty($extracted) && strlen($extracted) > 10) { // Ensure we got meaningful content
                    return $extracted;
                }
            }
        }

        // If no specific section found, try to find content that mentions the section name
        if (stripos($content, $sectionName) !== false) {
            // Find the paragraph that contains the section name
            $sentences = preg_split('/(?<=[.!?])\s+/', $content);
            $relevantSentences = [];

            foreach ($sentences as $sentence) {
                if (stripos($sentence, $sectionName) !== false) {
                    $relevantSentences[] = trim($sentence);
                    // Get the next few sentences as well
                    $currentIndex = array_search($sentence, $sentences);
                    for ($i = 1; $i <= 3; $i++) {
                        if (isset($sentences[$currentIndex + $i])) {
                            $relevantSentences[] = trim($sentences[$currentIndex + $i]);
                        }
                    }
                    break;
                }
            }

            if (!empty($relevantSentences)) {
                return implode(' ', $relevantSentences);
            }
        }

        return 'Information not available in AI response';
    }

    /**
     * Get fallback travel recommendations when API is not available
     * 
     * @param array $travelData Travel data
     * @return array Fallback recommendations
     */
    private function getFallbackTravelRecommendations($travelData)
    {
        $destination = $travelData['destination'] ?? 'unknown destination';

        return [
            'destination' => $destination,
            'risk_assessment' => "Please consult current LCR guidelines for {$destination} for up-to-date risk assessment.",
            'required_vaccinations' => 'Check LCR requirements for mandatory vaccinations for this destination.',
            'recommended_vaccinations' => 'Standard travel vaccines may include Hepatitis A, Typhoid, and destination-specific vaccines.',
            'malaria_prevention' => 'Consult LCR guidelines for malaria risk and prevention measures if applicable.',
            'general_advice' => 'Follow standard travel health precautions: safe food and water practices, insect bite prevention.',
            'special_considerations' => 'Consider patient age, pregnancy status, and medical history when planning vaccinations.',
            'full_response' => "AI recommendations are currently unavailable. Please consult the latest LCR (Dutch Travel Health Guidelines) for comprehensive travel health advice for {$destination}.",
            'timestamp' => date('Y-m-d H:i:s'),
            'model_used' => 'fallback',
            'sources' => ['LCR Guidelines - Manual consultation recommended']
        ];
    }

    /**
     * Generate clinical Q&A response based on user question
     * 
     * @param string $question The clinical question
     * @param array $context Additional context (optional)
     * @return array Response with answer and sources
     */
    public function generateClinicalQAResponse($question, $context = [])
    {
        // If no API key, use fallback response
        if (empty($this->apiKey)) {
            return $this->getFallbackClinicalResponse($question);
        }

        try {
            // Construct prompt for clinical Q&A
            $prompt = $this->constructClinicalQAPrompt($question, $context);

            // Call OpenAI API
            $response = $this->callOpenAI($prompt, [
                'model' => 'gpt-4',
                'temperature' => 0.2,
                'max_tokens' => 1000
            ]);

            // Parse clinical response
            return $this->parseClinicalResponse($response, $question);
        } catch (\Exception $e) {
            error_log('Error generating clinical Q&A response: ' . $e->getMessage());
            return $this->getFallbackClinicalResponse($question);
        }
    }

    /**
     * Construct a prompt for clinical Q&A
     * 
     * @param string $question The clinical question
     * @param array $context Additional context
     * @return string Constructed prompt
     */
    private function constructClinicalQAPrompt($question, $context = [])
    {
        $systemPrompt = "You are a clinical decision support system for Dutch healthcare providers. " .
            "You provide evidence-based answers following Dutch medical guidelines (LCR, RIVM, NNAAC, NHG). " .
            "Always include relevant guideline references and emphasize that your responses should not replace clinical judgment.";

        $userPrompt = "Clinical Question: {$question}\n\n";

        // Add context if provided
        if (!empty($context['patient_age'])) {
            $userPrompt .= "Patient Age: {$context['patient_age']}\n";
        }
        if (!empty($context['patient_gender'])) {
            $userPrompt .= "Patient Gender: {$context['patient_gender']}\n";
        }
        if (!empty($context['symptoms'])) {
            $userPrompt .= "Symptoms: {$context['symptoms']}\n";
        }
        if (!empty($context['medical_history'])) {
            $userPrompt .= "Medical History: {$context['medical_history']}\n";
        }

        $userPrompt .= "\nPlease provide:\n" .
            "1. A clear, evidence-based answer\n" .
            "2. Relevant Dutch medical guideline references (LCR, RIVM, NNAAC, NHG)\n" .
            "3. Any important clinical considerations\n" .
            "4. When to refer or seek additional consultation\n\n" .
            "Format your response in a structured, professional manner suitable for healthcare providers.";

        return $userPrompt;
    }

    /**
     * Call OpenAI API with custom parameters
     * 
     * @param string $prompt Prompt to send to OpenAI
     * @param array $options Custom options for the API call
     * @return string Response from OpenAI
     */
    private function callOpenAI($prompt, $options = [])
    {
        $defaultOptions = [
            'model' => 'gpt-3.5-turbo',
            'temperature' => 0.3,
            'max_tokens' => 500
        ];

        $options = array_merge($defaultOptions, $options);

        $data = [
            'model' => $options['model'],
            'messages' => [
                [
                    'role' => 'system',
                    'content' => 'You are a clinical decision support system for Dutch healthcare providers. You provide evidence-based answers following Dutch medical guidelines (LCR, RIVM, NNAAC, NHG).'
                ],
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'temperature' => $options['temperature'],
            'max_tokens' => $options['max_tokens']
        ];

        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey
        ];

        $ch = curl_init($this->apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch) || $httpCode !== 200) {
            $error = curl_error($ch);
            curl_close($ch);
            throw new \Exception("API request failed: $error (HTTP code: $httpCode)");
        }

        curl_close($ch);
        return $response;
    }

    /**
     * Parse clinical response from OpenAI
     * 
     * @param string $response JSON response from OpenAI
     * @param string $question Original question
     * @return array Parsed response
     */
    private function parseClinicalResponse($response, $question)
    {
        $decoded = json_decode($response, true);

        if (!isset($decoded['choices'][0]['message']['content'])) {
            throw new \Exception("Invalid response format from OpenAI");
        }

        $content = $decoded['choices'][0]['message']['content'];

        return [
            'question' => $question,
            'answer' => $content,
            'timestamp' => date('Y-m-d H:i:s'),
            'model_used' => $decoded['model'] ?? 'unknown',
            'sources' => $this->extractSources($content)
        ];
    }

    /**
     * Extract guideline sources from the response
     * 
     * @param string $content Response content
     * @return array Array of sources
     */
    private function extractSources($content)
    {
        $sources = [];

        // Look for common Dutch medical guideline abbreviations
        $guidelines = ['LCR', 'RIVM', 'NNAAC', 'NHG', 'CBO', 'NVOG', 'NIV', 'NVALT'];

        foreach ($guidelines as $guideline) {
            if (stripos($content, $guideline) !== false) {
                $sources[] = $guideline;
            }
        }

        return array_unique($sources);
    }

    /**
     * Get fallback clinical response when API is not available
     * 
     * @param string $question The clinical question
     * @return array Fallback response
     */
    private function getFallbackClinicalResponse($question)
    {
        return [
            'answer' => 'Based on Dutch medical guidelines, I can provide general information on this topic, but please consult official medical resources for the most current and specific guidance.',
            'sources' => ['Dutch Medical Guidelines (general reference)']
        ];
    }
    
    /**
     * Generate concise clinical Q&A response for floating chat
     * 
     * @param string $question The clinical question
     * @param array $context Additional context (optional)
     * @return array Response with concise answer
     */
    public function generateConciseClinicalResponse($question, $context = [])
    {
        // If no API key, use fallback response
        if (empty($this->apiKey)) {
            return $this->getFallbackConciseResponse($question);
        }

        try {
            // Construct prompt for concise clinical Q&A
            $prompt = $this->constructConciseClinicalPrompt($question, $context);

            // Call OpenAI API
            $response = $this->callOpenAI($prompt, [
                'model' => 'gpt-4',
                'temperature' => 0.2,
                'max_tokens' => 300 // Shorter response
            ]);

            // Parse clinical response
            return $this->parseClinicalResponse($response, $question);
        } catch (\Exception $e) {
            error_log('Error generating concise clinical response: ' . $e->getMessage());
            return $this->getFallbackConciseResponse($question);
        }
    }
    
    /**
     * Construct a prompt for concise clinical Q&A
     * 
     * @param string $question The clinical question
     * @param array $context Additional context
     * @return string Constructed prompt
     */
    private function constructConciseClinicalPrompt($question, $context = [])
    {
        $systemPrompt = "You are a clinical decision support system for Dutch healthcare providers. " .
            "You provide evidence-based answers following Dutch medical guidelines (LCR, RIVM, NNAAC, NHG). " .
            "Your responses must be extremely concise and direct - no more than 1-2 sentences.";

        $userPrompt = "Clinical Question: {$question}\n\n";

        // Add minimal context if provided
        if (!empty($context['patient_age'])) {
            $userPrompt .= "Patient Age: {$context['patient_age']}\n";
        }
        if (!empty($context['patient_gender'])) {
            $userPrompt .= "Patient Gender: {$context['patient_gender']}\n";
        }

        $userPrompt .= "\nProvide the most direct, concise answer possible in 1-2 sentences. " .
            "Focus only on the most essential clinical information without elaboration.";

        return $userPrompt;
    }
    
    /**
     * Get fallback concise response when API is unavailable
     * 
     * @param string $question The clinical question
     * @return array Fallback response
     */
    private function getFallbackConciseResponse($question)
    {
        return [
            'answer' => 'Per Dutch guidelines, this requires clinical judgment. Please consult official medical resources.',
            'sources' => ['Dutch Medical Guidelines']
        ];
    }
}
