<?php
namespace App\Models;

use App\Core\Model;

/**
 * Patient Model
 */
class Patient extends Model
{
    protected $table = 'patients';
    
    /**
     * Get all patients with pagination
     * 
     * @param int $page Current page number
     * @param int $perPage Items per page
     * @param string $search Search term
     * @return array Array containing patients and pagination data
     */
    public function getAllPaginated($page = 1, $perPage = 10, $search = '')
    {
        $offset = ($page - 1) * $perPage;
        $params = [];
        
        $countSql = "SELECT COUNT(*) as total FROM {$this->table}";
        $sql = "SELECT * FROM {$this->table}";
        
        // Add search condition if provided
        if (!empty($search)) {
            $searchCondition = " WHERE first_name LIKE ? OR last_name LIKE ? OR email LIKE ? OR phone LIKE ?";
            $countSql .= $searchCondition;
            $sql .= $searchCondition;
            
            $searchTerm = "%{$search}%";
            $params = [$searchTerm, $searchTerm, $searchTerm, $searchTerm];
        }
        
        // Add sorting and pagination
        $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
        $params[] = $perPage;
        $params[] = $offset;
        
        // Create a copy of params for the count query (without pagination parameters)
        $countParams = !empty($search) ? [$searchTerm, $searchTerm, $searchTerm, $searchTerm] : [];
        
        // Get total count
        $totalResult = $this->db->fetchOne($countSql, $countParams);
        $total = $totalResult ? $totalResult['total'] : 0;
        
        // Get patients
        $patients = $this->db->fetchAll($sql, $params);
        
        // Calculate pagination data
        $totalPages = ceil($total / $perPage);
        $hasNextPage = $page < $totalPages;
        $hasPrevPage = $page > 1;
        
        return [
            'patients' => $patients,
            'pagination' => [
                'total' => $total,
                'perPage' => $perPage,
                'currentPage' => $page,
                'totalPages' => $totalPages,
                'hasNextPage' => $hasNextPage,
                'hasPrevPage' => $hasPrevPage
            ]
        ];
    }
    
    /**
     * Get patient with medical history
     * 
     * @param string $id Patient ID
     * @return array Patient data with medical history
     */
    public function getWithMedicalHistory($id)
    {
        // Get patient
        $patient = $this->findById($id);
        
        if (!$patient) {
            return null;
        }
        
        // Get medical history
        $sql = "SELECT * FROM medical_history WHERE patient_id = ?";
        $medicalHistory = $this->db->fetchOne($sql, [$id]);
        
        // Combine data
        $patient['medical_history'] = $medicalHistory ?: [];
        
        return $patient;
    }
    
    /**
     * Create medical history for a patient
     * 
     * @param array $data Medical history data
     * @return string|false Medical history ID or false on failure
     */
    public function createMedicalHistory($data)
    {
        $id = $this->generateUuid();
        $data['id'] = $id;
        
        $result = $this->db->insert('medical_history', $data);
        return $result ? $id : false;
    }
    
    /**
     * Update medical history for a patient
     * 
     * @param string $patientId Patient ID
     * @param array $data Medical history data
     * @return bool Success status
     */
    public function updateMedicalHistory($patientId, $data)
    {
        // Check if medical history exists
        $existing = $this->db->fetchOne("SELECT id FROM medical_history WHERE patient_id = ?", [$patientId]);
        
        if ($existing) {
            // Update existing record
            return $this->db->update('medical_history', $data, "patient_id = ?", [$patientId]);
        } else {
            // Create new record
            $data['patient_id'] = $patientId;
            return $this->createMedicalHistory($data) !== false;
        }
    }
    
    /**
     * Get patient appointments
     * 
     * @param string $id Patient ID
     * @return array Patient appointments
     */
    public function getAppointments($id)
    {
        $sql = "SELECT * FROM appointments 
                WHERE patient_id = ? 
                ORDER BY appointment_date DESC";
        
        return $this->db->fetchAll($sql, [$id]);
    }
    
    /**
     * Get patient consultations
     * 
     * @param string $id Patient ID
     * @return array Patient consultations
     */
    public function getConsultations($id)
    {
        $sql = "SELECT * FROM consultations 
                WHERE patient_id = ? 
                ORDER BY created_at DESC";
        
        return $this->db->fetchAll($sql, [$id]);
    }
    
    /**
     * Get patient documents
     * 
     * @param string $id Patient ID
     * @return array Patient documents
     */
    public function getDocuments($id)
    {
        $sql = "SELECT * FROM documents WHERE patient_id = ? ORDER BY created_at DESC";
        return $this->db->fetchAll($sql, [$id]);
    }
    
    /**
     * Get all patients without pagination
     * 
     * @return array All patients
     */
    public function getAll()
    {
        $sql = "SELECT * FROM {$this->table} ORDER BY last_name, first_name";
        return $this->db->fetchAll($sql);
    }
    
    /**
     * Find a patient by ID
     * 
     * @param string $id Patient ID
     * @return array|null Patient data or null if not found
     */
    public function findById($id)
    {
        return $this->find($id);
    }
}
