<?php

namespace App\Models;

use App\Core\Model;
use PDO;

class Consultation extends Model
{
    protected $table = 'consultations';
    
    /**
     * Get all consultations with pagination
     * 
     * @param int $page Current page number
     * @param int $perPage Items per page
     * @param array $filters Optional filters
     * @return array Array with consultations and pagination data
     */
    public function getAllWithPagination($page = 1, $perPage = 10, $filters = [])
    {
        $offset = ($page - 1) * $perPage;
        
        $sql = "SELECT c.*, 
                p.first_name, p.last_name, p.email
                FROM {$this->table} c
                LEFT JOIN patients p ON c.patient_id = p.id
                WHERE 1=1";
        
        $params = [];
        
        // Apply filters
        if (!empty($filters['search'])) {
            $search = "%{$filters['search']}%";
            $sql .= " AND (p.first_name LIKE ? OR p.last_name LIKE ? OR c.consultation_type LIKE ?)";
            $params[] = $search;
            $params[] = $search;
            $params[] = $search;
        }
        
        if (!empty($filters['date'])) {
            $sql .= " AND DATE(c.started_at) = ?";
            $params[] = $filters['date'];
        }
        
        // Get total count for pagination
        $countSql = "SELECT COUNT(*) as total FROM ({$sql}) as filtered_results";
        $stmt = $this->db->getConnection()->prepare($countSql);
        $stmt->execute($params);
        $totalCount = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // Add sorting and pagination
        $sql .= " ORDER BY c.started_at DESC LIMIT {$perPage} OFFSET {$offset}";
        
        $stmt = $this->db->getConnection()->prepare($sql);
        $stmt->execute($params);
        $consultations = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return [
            'consultations' => $consultations,
            'pagination' => [
                'total' => $totalCount,
                'per_page' => $perPage,
                'current_page' => $page,
                'last_page' => ceil($totalCount / $perPage),
                'from' => $offset + 1,
                'to' => min($offset + $perPage, $totalCount)
            ]
        ];
    }
    
    /**
     * Get consultations for a specific patient
     * 
     * @param string $patientId Patient ID
     * @return array Array of consultations
     */
    public function getByPatientId($patientId)
    {
        $sql = "SELECT c.* 
                FROM {$this->table} c
                WHERE c.patient_id = ?
                ORDER BY c.started_at DESC";
        
        $stmt = $this->db->getConnection()->prepare($sql);
        $stmt->execute([$patientId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get consultations for a specific doctor
     * 
     * Note: This method is currently not functional as doctor_id doesn't exist in the consultations table.
     * It returns an empty array until the database schema is updated.
     * 
     * @param string $doctorId Doctor ID
     * @param string|null $date Optional date filter (Y-m-d)
     * @return array Array of consultations
     */
    public function getByDoctorId($doctorId, $date = null)
    {
        // Since doctor_id doesn't exist in the consultations table,
        // we return an empty array for now
        return [];
    }
    
    /**
     * Get recent consultations
     * 
     * @param int $limit Number of consultations to return
     * @return array Array of recent consultations
     */
    public function getRecent($limit = 5)
    {
        $sql = "SELECT c.*, 
                p.first_name, p.last_name
                FROM {$this->table} c
                LEFT JOIN patients p ON c.patient_id = p.id
                ORDER BY c.started_at DESC
                LIMIT ?";
        
        $stmt = $this->db->getConnection()->prepare($sql);
        $stmt->execute([$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get consultation by ID with related data
     * 
     * @param string $id Consultation ID
     * @return array|false Consultation data or false if not found
     */
    public function getById($id)
    {
        $sql = "SELECT c.*, 
                p.first_name, p.last_name, p.date_of_birth, p.gender, p.email, p.phone
                FROM {$this->table} c
                LEFT JOIN patients p ON c.patient_id = p.id
                WHERE c.id = ?";
        
        $stmt = $this->db->getConnection()->prepare($sql);
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Create a new consultation
     * 
     * @param array $data Consultation data
     * @return string|false New consultation ID or false on failure
     */
    public function create($data)
    {
        $id = $this->generateUuid();
        
        $sql = "INSERT INTO {$this->table} (
                id, patient_id, appointment_id, consultation_type, status,
                started_at, completed_at, ai_recommendations, doctor_notes,
                created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, NOW(), NULL, ?, ?, NOW(), NOW()
            )";
        
        $stmt = $this->db->getConnection()->prepare($sql);
        $result = $stmt->execute([
            $id,
            $data['patient_id'],
            $data['appointment_id'] ?? null,
            $data['consultation_type'] ?? 'general_qa',
            $data['status'] ?? 'in_progress',
            $data['ai_recommendations'] ?? null,
            $data['notes'] ?? null // Using notes as doctor_notes
        ]);
        
        return $result ? $id : false;
    }
    
    /**
     * Update a consultation
     * 
     * @param string $id Consultation ID
     * @param array $data Updated consultation data
     * @return bool Success status
     */
    public function update($id, $data)
    {
        $sql = "UPDATE {$this->table} SET
                patient_id = ?,
                status = ?,
                ai_recommendations = ?,
                doctor_notes = ?,
                updated_at = NOW()
                WHERE id = ?";
        
        $stmt = $this->db->getConnection()->prepare($sql);
        return $stmt->execute([
            $data['patient_id'],
            $data['status'] ?? 'in_progress',
            $data['ai_recommendations'] ?? null,
            $data['notes'] ?? null, // Using notes as doctor_notes
            $id
        ]);
    }
    
    /**
     * Get consultations linked to an appointment
     * 
     * @param string $appointmentId Appointment ID
     * @return array Array of consultations
     */
    public function getByAppointmentId($appointmentId)
    {
        $sql = "SELECT c.*, 
                p.first_name, p.last_name
                FROM {$this->table} c
                LEFT JOIN patients p ON c.patient_id = p.id
                WHERE c.appointment_id = ?
                ORDER BY c.started_at DESC";
        
        $stmt = $this->db->getConnection()->prepare($sql);
        $stmt->execute([$appointmentId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Create a consultation from an appointment
     * 
     * @param array $appointmentData Appointment data
     * @return string|false New consultation ID or false on failure
     */
    public function createFromAppointment($appointmentData)
    {
        $consultationData = [
            'patient_id' => $appointmentData['patient_id'],
            'appointment_id' => $appointmentData['id'],
            'consultation_type' => 'general_qa', // Default type
            'doctor_notes' => $appointmentData['notes'] ?? null
        ];
        
        return $this->create($consultationData);
    }
    
    /**
     * Get statistics for consultations
     * 
     * @param string $period Period to get statistics for (day, week, month, year)
     * @return array Array of statistics
     */
    public function getStatistics($period = 'month')
    {
        $dateFormat = '';
        $dateCondition = '';
        
        switch ($period) {
            case 'day':
                $dateFormat = '%Y-%m-%d';
                $dateCondition = 'DATE(started_at) = CURDATE()';
                break;
            case 'week':
                $dateFormat = '%Y-%u';
                $dateCondition = 'YEARWEEK(started_at) = YEARWEEK(CURDATE())';
                break;
            case 'month':
                $dateFormat = '%Y-%m';
                $dateCondition = 'YEAR(started_at) = YEAR(CURDATE()) AND MONTH(started_at) = MONTH(CURDATE())';
                break;
            case 'year':
                $dateFormat = '%Y';
                $dateCondition = 'YEAR(started_at) = YEAR(CURDATE())';
                break;
            default:
                $dateFormat = '%Y-%m';
                $dateCondition = 'YEAR(started_at) = YEAR(CURDATE()) AND MONTH(started_at) = MONTH(CURDATE())';
        }
        
        // Get total consultations for the period
        $sql = "SELECT COUNT(*) as total FROM {$this->table} WHERE {$dateCondition}";
        $stmt = $this->db->getConnection()->prepare($sql);
        $stmt->execute();
        $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // Get consultations by day/week/month
        $sql = "SELECT 
                DATE_FORMAT(started_at, '{$dateFormat}') as period,
                COUNT(*) as count
                FROM {$this->table}
                WHERE started_at >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)
                GROUP BY period
                ORDER BY period DESC
                LIMIT 12";
        $stmt = $this->db->getConnection()->prepare($sql);
        $stmt->execute();
        $byPeriod = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get top consultation types
        $sql = "SELECT 
                consultation_type,
                COUNT(*) as count
                FROM {$this->table}
                WHERE {$dateCondition}
                GROUP BY consultation_type
                ORDER BY count DESC
                LIMIT 5";
        $stmt = $this->db->getConnection()->prepare($sql);
        $stmt->execute();
        $topDiagnoses = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return [
            'total' => $total,
            'by_period' => $byPeriod,
            'top_consultation_types' => $topDiagnoses
        ];
    }
}
