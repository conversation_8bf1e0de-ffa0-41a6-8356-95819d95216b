<?php
namespace App\Models;

use App\Core\Model;

/**
 * User Model
 */
class User extends Model
{
    protected $table = 'users';
    
    /**
     * Find a user by username
     * 
     * @param string $username The username to find
     * @return array|false The user or false if not found
     */
    public function findByUsername($username)
    {
        $sql = "SELECT * FROM {$this->table} WHERE username = ? AND is_active = 1";
        return $this->db->fetchOne($sql, [$username]);
    }
    
    /**
     * Find a user by email
     * 
     * @param string $email The email to find
     * @return array|false The user or false if not found
     */
    public function findByEmail($email)
    {
        $sql = "SELECT * FROM {$this->table} WHERE email = ? AND is_active = 1";
        return $this->db->fetchOne($sql, [$email]);
    }
    
    /**
     * Create a new user
     * 
     * @param array $data The user data
     * @return string The ID of the new user
     */
    public function create($data)
    {
        // Hash password if provided
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        
        return parent::create($data);
    }
    
    /**
     * Update a user
     * 
     * @param string $id The ID of the user to update
     * @param array $data The data to update
     * @return int The number of affected rows
     */
    public function update($id, $data)
    {
        // Hash password if provided
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        
        return parent::update($id, $data);
    }
    
    /**
     * Get all doctors
     * 
     * @return array Array of doctors
     */
    public function getDoctors()
    {
        $sql = "SELECT * FROM {$this->table} WHERE role = 'doctor' AND is_active = 1 ORDER BY last_name, first_name";
        return $this->db->fetchAll($sql);
    }
}
