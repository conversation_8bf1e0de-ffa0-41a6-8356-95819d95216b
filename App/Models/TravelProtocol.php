<?php

namespace App\Models;

use App\Core\Model;

class TravelProtocol extends Model
{
    protected $table = 'travel_consultations';
    protected $primaryKey = 'id';
    
    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
    }
    
    /**
     * Generate a UUID v4
     * 
     * @return string UUID
     */
    protected function generateUuid()
    {
        // Generate 16 bytes (128 bits) of random data
        $data = random_bytes(16);
        
        // Set version to 0100
        $data[6] = chr(ord($data[6]) & 0x0f | 0x40);
        // Set bits 6-7 to 10
        $data[8] = chr(ord($data[8]) & 0x3f | 0x80);
        
        // Output the 36 character UUID
        return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
    }
    
    /**
     * Create a new travel protocol
     * 
     * @param array $data Travel protocol data
     * @return string The ID of the new travel protocol
     */
    public function createProtocol($data)
    {
        // First create the consultation record
        $consultationId = $this->generateUuid();
        $consultationData = [
            'id' => $consultationId,
            'patient_id' => $data['patient_id'],
            'appointment_id' => $data['appointment_id'] ?? null,
            'consultation_type' => 'travel_medicine',
            'status' => 'in_progress'
        ];
        
        $this->db->insert('consultations', $consultationData);
        
        // Then create the travel consultation record
        $travelData = [
            'id' => $this->generateUuid(),
            'consultation_id' => $consultationId,
            'destination' => $data['destination'] ?? '',
            'travel_type' => $data['travel_type'] ?? null,
            'duration' => $data['duration'] ?? '',
            'departure_date' => $data['departure_date'] ?? null,
            'age' => $data['age'] ?? null,
            'gender' => $data['gender'] ?? null,
            'pregnancy_status' => $data['pregnancy_status'] ?? null,
            'allergies' => $data['allergies'] ?? null,
            'previous_vaccinations' => $data['previous_vaccinations'] ?? '',
            'risk_assessment' => $data['risk_assessment'] ?? '',
            'vaccination_plan' => $data['vaccination_plan'] ?? '',
            'doctor_id' => $data['doctor_id'] ?? null,
            'notes' => $data['notes'] ?? '',
            'status' => 'completed',
            'step' => 5
        ];
        
        return $this->db->insert($this->table, $travelData);
    }
    
    /**
     * Update an existing travel protocol
     * 
     * @param string $id Travel protocol ID
     * @param array $data Travel protocol data
     * @return int The number of affected rows
     */
    public function updateProtocol($id, $data)
    {
        return $this->db->update($this->table, $data, "{$this->primaryKey} = ?", [$id]);
    }
    
    /**
     * Get travel protocol by ID
     * 
     * @param string $id Travel protocol ID
     * @return array|false The travel protocol or false if not found
     */
    public function getProtocolById($id)
    {
        $sql = "SELECT tc.*, c.patient_id, c.status as consultation_status, c.started_at, c.completed_at, 
                p.first_name, p.last_name, p.date_of_birth, p.gender as patient_gender,
                u.first_name as doctor_first_name, u.last_name as doctor_last_name
                FROM {$this->table} tc
                JOIN consultations c ON tc.consultation_id = c.id
                JOIN patients p ON c.patient_id = p.id
                LEFT JOIN users u ON tc.doctor_id = u.id
                WHERE tc.{$this->primaryKey} = ?";
        
        return $this->db->fetchOne($sql, [$id]);
    }
    
    /**
     * Get travel protocol by consultation ID
     * 
     * @param string $consultationId Consultation ID
     * @return array|false The travel protocol or false if not found
     */
    public function getProtocolByConsultationId($consultationId)
    {
        $sql = "SELECT tc.*, c.patient_id, c.status as consultation_status, c.started_at, c.completed_at,
                u.first_name as doctor_first_name, u.last_name as doctor_last_name
                FROM {$this->table} tc
                JOIN consultations c ON tc.consultation_id = c.id
                LEFT JOIN users u ON tc.doctor_id = u.id
                WHERE tc.consultation_id = ?";
        
        return $this->db->fetchOne($sql, [$consultationId]);
    }
    
    /**
     * Get all travel protocols with pagination
     * 
     * @param int $page Current page number
     * @param int $perPage Items per page
     * @param array $filters Optional filters
     * @return array Array with protocols and pagination info
     */
    public function getAllWithPagination($page = 1, $perPage = 10, $filters = [])
    {
        $offset = ($page - 1) * $perPage;
        $params = [];
        
        $sql = "SELECT tc.*, c.patient_id, c.status as consultation_status, c.started_at, c.completed_at, 
                p.first_name, p.last_name,
                u.first_name as doctor_first_name, u.last_name as doctor_last_name
                FROM {$this->table} tc
                JOIN consultations c ON tc.consultation_id = c.id
                JOIN patients p ON c.patient_id = p.id
                LEFT JOIN users u ON tc.doctor_id = u.id
                WHERE 1=1";
        
        $countSql = "SELECT COUNT(*) as count 
                    FROM {$this->table} tc
                    JOIN consultations c ON tc.consultation_id = c.id
                    JOIN patients p ON c.patient_id = p.id
                    WHERE 1=1";
        
        // Apply filters
        if (!empty($filters['search'])) {
            $searchTerm = '%' . $filters['search'] . '%';
            $sql .= " AND (p.first_name LIKE ? OR p.last_name LIKE ? OR tc.destination LIKE ?)";
            $countSql .= " AND (p.first_name LIKE ? OR p.last_name LIKE ? OR tc.destination LIKE ?)";
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
        }
        
        if (!empty($filters['destination'])) {
            $sql .= " AND tc.destination = ?";
            $countSql .= " AND tc.destination = ?";
            $params[] = $filters['destination'];
        }
        
        if (!empty($filters['travel_type'])) {
            $sql .= " AND tc.travel_type = ?";
            $countSql .= " AND tc.travel_type = ?";
            $params[] = $filters['travel_type'];
        }
        
        // Add order by
        $sql .= " ORDER BY c.started_at DESC";
        
        // Add limit
        $sql .= " LIMIT {$perPage} OFFSET {$offset}";
        
        // Get protocols
        $protocols = $this->db->fetchAll($sql, $params);
        
        // Get total count
        $countResult = $this->db->fetchOne($countSql, $params);
        $totalCount = $countResult['count'];
        
        // Calculate pagination
        $totalPages = ceil($totalCount / $perPage);
        
        return [
            'protocols' => $protocols,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total_count' => $totalCount,
                'total_pages' => $totalPages
            ]
        ];
    }
    
    /**
     * Complete a travel protocol
     * 
     * @param string $consultationId Consultation ID
     * @param array $data Additional data to update
     * @return bool Success status
     */
    public function completeProtocol($consultationId, $data = [])
    {
        // Update the consultation status
        $consultationUpdate = [
            'status' => 'completed',
            'completed_at' => date('Y-m-d H:i:s')
        ];
        
        $this->db->update('consultations', $consultationUpdate, "id = ?", [$consultationId]);
        
        // Update the travel consultation with any additional data
        if (!empty($data)) {
            $protocol = $this->getProtocolByConsultationId($consultationId);
            if ($protocol) {
                // Add status update to completed
                $data['status'] = 'completed';
                return $this->db->update($this->table, $data, "id = ?", [$protocol['id']]);
            }
        } else {
            // Just update the status if no other data provided
            $protocol = $this->getProtocolByConsultationId($consultationId);
            if ($protocol) {
                return $this->db->update($this->table, ['status' => 'completed'], "id = ?", [$protocol['id']]);
            }
        }
        
        return true;
    }
    
    /**
     * Generate risk assessment based on destination and patient data
     * 
     * @param string $destination Travel destination
     * @param array $patientData Patient data including age, gender, etc.
     * @return string Risk assessment text
     */
    public function generateRiskAssessment($destination, $patientData)
    {
        // In a real application, this would use the OpenAI API or another service
        // to generate a risk assessment based on the destination and patient data
        
        return "Risk assessment for travel to {$destination} based on patient profile and current health guidelines.";
    }
    
    /**
     * Generate vaccination plan based on destination and patient data
     * 
     * @param string $destination Travel destination
     * @param array $patientData Patient data including age, gender, etc.
     * @return array Vaccination plan with required and recommended vaccinations
     */
    public function generateVaccinationPlan($destination, $patientData)
    {
        // In a real application, this would use the OpenAI API or another service
        // to generate a vaccination plan based on the destination and patient data
        
        // Sample vaccination plan structure
        return [
            'required' => [
                [
                    'name' => 'Hepatitis A',
                    'reason' => 'Required according to CDC guidelines',
                    'condition' => 'If not previously vaccinated'
                ],
                [
                    'name' => 'DTP booster',
                    'reason' => 'Required according to CDC guidelines',
                    'condition' => 'If >10 years since last dose'
                ]
            ],
            'recommended' => [
                [
                    'name' => 'Typhoid',
                    'reason' => 'Consider based on specific travel circumstances',
                    'condition' => 'For high-risk areas'
                ],
                [
                    'name' => 'Rabies',
                    'reason' => 'Consider based on specific travel circumstances',
                    'condition' => 'If animal exposure likely'
                ],
                [
                    'name' => 'Japanese Encephalitis',
                    'reason' => 'Consider based on specific travel circumstances',
                    'condition' => 'Rural areas, >30 days'
                ]
            ]
        ];
    }
    
    /**
     * Save protocol progress during multi-step form
     * 
     * @param array $data Protocol data
     * @param int $step Current step number
     * @return string|bool Protocol ID if created, true if updated, false on failure
     */
    public function saveProgress($data, $step)
    {
        // Check if we already have a protocol in progress
        if (!empty($data['id'])) {
            // Update existing protocol
            $updateData = [
                'step' => $step
            ];
            
            // Add step-specific data
            switch ($step) {
                case 1:
                    $updateData['destination'] = $data['destination'] ?? null;
                    $updateData['travel_type'] = $data['travel_type'] ?? null;
                    $updateData['duration'] = $data['duration'] ?? null;
                    $updateData['departure_date'] = $data['departure_date'] ?? null;
                    break;
                    
                case 2:
                    $updateData['age'] = $data['age'] ?? null;
                    $updateData['gender'] = $data['gender'] ?? null;
                    $updateData['pregnancy_status'] = $data['pregnancy_status'] ?? null;
                    $updateData['allergies'] = $data['allergies'] ?? null;
                    break;
                    
                case 3:
                    $updateData['risk_assessment'] = $data['risk_assessment'] ?? null;
                    break;
                    
                case 4:
                    $updateData['vaccination_plan'] = $data['vaccination_plan'] ?? null;
                    break;
                    
                case 5:
                    $updateData['doctor_id'] = $data['doctor_id'] ?? null;
                    $updateData['notes'] = $data['notes'] ?? null;
                    $updateData['status'] = 'completed';
                    break;
            }
            
            return $this->db->update($this->table, $updateData, "{$this->primaryKey} = ?", [$data['id']]);
        } else if ($step === 1) {
            // Create new protocol for first step
            $consultationId = $this->generateUuid();
            $consultationData = [
                'id' => $consultationId,
                'patient_id' => $data['patient_id'] ?? null,
                'consultation_type' => 'travel_medicine',
                'status' => 'in_progress'
            ];
            
            $this->db->insert('consultations', $consultationData);
            
            $protocolData = [
                'id' => $this->generateUuid(),
                'consultation_id' => $consultationId,
                'destination' => $data['destination'] ?? null,
                'travel_type' => $data['travel_type'] ?? null,
                'duration' => $data['duration'] ?? null,
                'departure_date' => $data['departure_date'] ?? null,
                'status' => 'in_progress',
                'step' => 1
            ];
            
            return $this->db->insert($this->table, $protocolData);
        }
        
        return false;
    }
}
