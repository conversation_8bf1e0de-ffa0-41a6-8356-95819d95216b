<?php

namespace App\Models;

use App\Core\Model;
use PDO;

class Appointment extends Model
{
    protected $table = 'appointments';
    
    /**
     * Get all appointments with pagination
     * 
     * @param int $page Current page number
     * @param int $perPage Items per page
     * @param array $filters Optional filters
     * @return array Array with appointments and pagination data
     */
    public function getAllWithPagination($page = 1, $perPage = 10, $filters = [])
    {
        $offset = ($page - 1) * $perPage;
        
        $sql = "SELECT a.*, 
                p.first_name, p.last_name 
                FROM {$this->table} a
                LEFT JOIN patients p ON a.patient_id = p.id
                WHERE 1=1";
        
        $params = [];
        
        // Apply filters
        if (!empty($filters['search'])) {
            $search = "%{$filters['search']}%";
            $sql .= " AND (p.first_name LIKE ? OR p.last_name LIKE ? OR a.reason LIKE ?)";
            $params[] = $search;
            $params[] = $search;
            $params[] = $search;
        }
        
        if (!empty($filters['date'])) {
            $sql .= " AND DATE(a.appointment_date) = ?";
            $params[] = $filters['date'];
        }
        
        if (!empty($filters['status'])) {
            $sql .= " AND a.status = ?";
            $params[] = $filters['status'];
        }
        
        // Doctor ID filter removed as the column doesn't exist
        
        // Get total count for pagination
        $countSql = "SELECT COUNT(*) as total FROM ({$sql}) as filtered_results";
        $totalResult = $this->db->fetchOne($countSql, $params);
        $totalCount = $totalResult ? $totalResult['total'] : 0;
        
        // Add sorting and pagination
        $sql .= " ORDER BY a.appointment_date DESC LIMIT {$perPage} OFFSET {$offset}";
        
        $appointments = $this->db->fetchAll($sql, $params);
        
        return [
            'appointments' => $appointments,
            'pagination' => [
                'total' => $totalCount,
                'per_page' => $perPage,
                'current_page' => $page,
                'last_page' => ceil($totalCount / $perPage),
                'from' => $offset + 1,
                'to' => min($offset + $perPage, $totalCount)
            ]
        ];
    }
    
    /**
     * Get appointments for a specific patient
     * 
     * @param string $patientId Patient ID
     * @return array Array of appointments
     */
    public function getByPatientId($patientId)
    {
        $sql = "SELECT a.*, 
                u.first_name as doctor_first_name, u.last_name as doctor_last_name 
                FROM {$this->table} a
                LEFT JOIN users u ON a.doctor_id = u.id
                WHERE a.patient_id = ?
                ORDER BY a.appointment_date DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$patientId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get appointments for a specific doctor
     * 
     * @param string $doctorId Doctor ID
     * @param string|null $date Optional date filter (Y-m-d)
     * @return array Array of appointments
     */
    public function getByDoctorId($doctorId, $date = null)
    {
        // Since appointments table doesn't have doctor_id column,
        // return appointments for the specified date to check time conflicts
        $sql = "SELECT a.*, 
                p.first_name, p.last_name 
                FROM {$this->table} a
                LEFT JOIN patients p ON a.patient_id = p.id";
        
        $params = [];
        
        if ($date) {
            $sql .= " WHERE DATE(a.appointment_date) = ?";
            $params[] = $date;
        }
        
        $sql .= " ORDER BY a.appointment_date ASC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get upcoming appointments
     * 
     * @param int $limit Number of appointments to return
     * @return array Array of upcoming appointments
     */
    public function getUpcoming($limit = 5)
    {
        $sql = "SELECT a.*, 
                p.first_name, p.last_name, 
                u.first_name as doctor_first_name, u.last_name as doctor_last_name 
                FROM {$this->table} a
                LEFT JOIN patients p ON a.patient_id = p.id
                LEFT JOIN users u ON a.doctor_id = u.id
                WHERE a.appointment_date >= NOW()
                AND a.status = 'scheduled'
                ORDER BY a.appointment_date ASC
                LIMIT ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get today's appointments
     * 
     * @return array Array of today's appointments
     */
    public function getTodaysAppointments()
    {
        $sql = "SELECT a.*, 
                p.first_name, p.last_name, p.email
                FROM {$this->table} a
                LEFT JOIN patients p ON a.patient_id = p.id
                WHERE DATE(a.appointment_date) = CURDATE()
                ORDER BY a.appointment_date ASC";
        
        $stmt = $this->db->getConnection()->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get appointment by ID with related data
     * 
     * @param string $id Appointment ID
     * @return array|false Appointment data or false if not found
     */
    public function getById($id)
    {
        $sql = "SELECT a.*, 
                p.first_name, p.last_name, p.date_of_birth, p.gender, p.email, p.phone
                FROM {$this->table} a
                LEFT JOIN patients p ON a.patient_id = p.id
                WHERE a.id = ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Create a new appointment
     * 
     * @param array $data Appointment data
     * @return string|false New appointment ID or false on failure
     */
    public function create($data)
    {
        $id = $this->generateUuid();
        
        $sql = "INSERT INTO {$this->table} (
                id, patient_id, appointment_date, duration_minutes,
                reason_for_visit, status, notes, created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, NOW(), NOW()
            )";
        
        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute([
            $id,
            $data['patient_id'],
            $data['appointment_date'],
            $data['duration_minutes'] ?? 30,
            $data['reason_for_visit'],
            $data['status'] ?? 'scheduled',
            $data['notes'] ?? null
        ]);
        
        return $result ? $id : false;
    }
    
    /**
     * Update an appointment
     * 
     * @param string $id Appointment ID
     * @param array $data Updated appointment data
     * @return bool Success status
     */
    public function update($id, $data)
    {
        $sql = "UPDATE {$this->table} SET
                patient_id = ?,
                appointment_date = ?,
                duration_minutes = ?,
                reason_for_visit = ?,
                status = ?,
                notes = ?,
                updated_at = NOW()
                WHERE id = ?";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            $data['patient_id'],
            $data['appointment_date'],
            $data['duration_minutes'] ?? 30,
            $data['reason_for_visit'],
            $data['status'],
            $data['notes'] ?? null,
            $id
        ]);
    }
    
    /**
     * Update appointment status
     * 
     * @param string $id Appointment ID
     * @param string $status New status
     * @return bool Success status
     */
    public function updateStatus($id, $status)
    {
        $sql = "UPDATE {$this->table} SET
                status = ?,
                updated_at = NOW()
                WHERE id = ?";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$status, $id]);
    }
    
    /**
     * Get appointments by date range for calendar view
     * 
     * @param string $startDate Start date (Y-m-d)
     * @param string $endDate End date (Y-m-d)
     * @return array Array of appointments
     */
    public function getAppointmentsByDateRange($startDate, $endDate)
    {
        $sql = "SELECT a.*, 
                p.first_name, p.last_name 
                FROM {$this->table} a
                LEFT JOIN patients p ON a.patient_id = p.id
                WHERE DATE(a.appointment_date) BETWEEN ? AND ?
                ORDER BY a.appointment_date ASC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$startDate, $endDate]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get appointments for a specific date
     * 
     * @param string $date Date (Y-m-d)
     * @return array Array of appointments
     */
    public function getAppointmentsByDate($date)
    {
        $sql = "SELECT a.*, 
                p.first_name, p.last_name 
                FROM {$this->table} a
                LEFT JOIN patients p ON a.patient_id = p.id
                WHERE DATE(a.appointment_date) = ?
                ORDER BY a.appointment_date ASC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$date]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Check if a time slot is available
     * 
     * @param string|null $doctorId Doctor ID (not used, kept for backward compatibility)
     * @param string $appointmentDate Appointment date and time
     * @param int $durationMinutes Duration in minutes
     * @param string|null $excludeAppointmentId Optional appointment ID to exclude (for updates)
     * @return bool True if available, false if conflicting
     */
    public function isTimeSlotAvailable($doctorId, $appointmentDate, $durationMinutes = 30, $excludeAppointmentId = null)
    {
        $appointmentDateTime = new \DateTime($appointmentDate);
        $endDateTime = clone $appointmentDateTime;
        $endDateTime->modify("+{$durationMinutes} minutes");
        
        // Check for any time conflicts across all appointments
        $sql = "SELECT COUNT(*) as conflict_count FROM {$this->table}
                WHERE status != 'cancelled'
                AND (
                    (appointment_date <= ? AND DATE_ADD(appointment_date, INTERVAL duration_minutes MINUTE) > ?)
                    OR
                    (appointment_date < ? AND DATE_ADD(appointment_date, INTERVAL duration_minutes MINUTE) >= ?)
                )";
        
        $params = [
            $endDateTime->format('Y-m-d H:i:s'),
            $appointmentDateTime->format('Y-m-d H:i:s'),
            $endDateTime->format('Y-m-d H:i:s'),
            $appointmentDateTime->format('Y-m-d H:i:s')
        ];
        
        if ($excludeAppointmentId) {
            $sql .= " AND id != ?";
            $params[] = $excludeAppointmentId;
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $result['conflict_count'] == 0;
    }
}
