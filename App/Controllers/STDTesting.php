<?php

namespace App\Controllers;

use App\Core\Controller;
use App\Models\Consultation;
use App\Models\Patient;
use App\Services\OpenAIService;

class STDTesting extends Controller
{
    private $consultationModel;
    private $patientModel;
    private $openAIService;
    
    // Risk factor options for STD testing
    private $riskFactorOptions = [
        'multiple_partners' => 'Multiple sexual partners',
        'unprotected_sex' => 'Unprotected sexual contact',
        'partner_with_std' => 'Partner with known STD',
        'sex_work' => 'Sex work involvement',
        'msm' => 'Men who have sex with men',
        'iv_drug_use' => 'Intravenous drug use',
        'previous_std' => 'Previous STD history'
    ];
    
    public function __construct()
    {
        parent::__construct();
        $this->consultationModel = new Consultation();
        $this->patientModel = new Patient();
        $this->openAIService = new OpenAIService();
    }
    
    /**
     * Display list of STD testing consultations
     */
    public function index()
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            redirect('/login');
        }
        
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $perPage = 10;
        
        // Get filters from query parameters
        $filters = [
            'search' => $_GET['search'] ?? '',
            'consultation_type' => 'std_testing'
        ];
        
        // Get consultations with pagination
        $result = $this->consultationModel->getAllWithPagination($page, $perPage, $filters);
        
        $data = [
            'title' => 'STD Testing Consultations',
            'consultations' => $result['consultations'],
            'pagination' => $result['pagination'],
            'filters' => $filters
        ];
        
        $this->render('pages/std-testing/index', $data);
    }
    
    /**
     * Display step-by-step form to create a new STD testing consultation
     * 
     * @param int $step Current step (1-4)
     */
    public function create($step = 1)
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            redirect('/login');
        }
        
        // Validate step parameter
        $step = (int)$step;
        if ($step < 1 || $step > 4) {
            $step = 1;
        }
        
        // Get all patients for dropdown
        $patients = $this->patientModel->getAll();
        
        // Get form data from session if exists
        $formData = $_SESSION['std_testing_form_data'] ?? [
            'patient_id' => '',
            'patient_age' => '',
            'patient_gender' => '',
            'last_testing' => '',
            'symptoms' => '',
            'exposure_history' => '',
            'risk_factors' => [],
            'test_type' => '',
            'sample_type' => '',
            'counseling_notes' => '',
            'consent_obtained' => false,
            'notes' => ''
        ];
        
        // Step titles and descriptions
        $steps = [
            1 => ['title' => 'Patient Information', 'description' => 'Basic demographics and history'],
            2 => ['title' => 'Risk Assessment', 'description' => 'Symptoms and exposure evaluation'],
            3 => ['title' => 'Testing Protocol', 'description' => 'Recommended tests based on guidelines'],
            4 => ['title' => 'Documentation', 'description' => 'Lab orders and follow-up plan']
        ];
        
        $data = [
            'title' => 'New STD Testing Consultation',
            'patients' => $patients,
            'formData' => $formData,
            'currentStep' => $step,
            'steps' => $steps,
            'riskFactorOptions' => $this->riskFactorOptions
        ];
        
        // If step 3, we may need to generate AI recommendations
        if ($step == 3 && !empty($_SESSION['generate_recommendations'])) {
            $data['recommendations'] = $this->generateRecommendations($formData);
            unset($_SESSION['generate_recommendations']);
        }
        
        $this->render('pages/std-testing/step' . $step, $data);
    }
    
    /**
     * Process form data for each step
     */
    public function processStep()
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            redirect('/login');
        }
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect('/std-testing/create');
        }
        
        // Get current step and form data
        $currentStep = isset($_POST['current_step']) ? (int)$_POST['current_step'] : 1;
        $nextStep = isset($_POST['next_step']) ? (int)$_POST['next_step'] : $currentStep + 1;
        
        // Get existing form data from session
        $formData = $_SESSION['std_testing_form_data'] ?? [];
        
        // Process form data based on current step
        switch ($currentStep) {
            case 1:
                $formData['patient_id'] = $_POST['patient_id'] ?? '';
                $formData['patient_age'] = $_POST['patient_age'] ?? '';
                $formData['patient_gender'] = $_POST['patient_gender'] ?? '';
                $formData['last_testing'] = $_POST['last_testing'] ?? '';
                break;
                
            case 2:
                $formData['symptoms'] = $_POST['symptoms'] ?? '';
                $formData['exposure_history'] = $_POST['exposure_history'] ?? '';
                $formData['risk_factors'] = $_POST['risk_factors'] ?? [];
                
                // Set flag to generate recommendations in step 3
                $_SESSION['generate_recommendations'] = true;
                break;
                
            case 3:
                $formData['test_type'] = $_POST['test_type'] ?? '';
                $formData['sample_type'] = $_POST['sample_type'] ?? '';
                $formData['counseling_notes'] = $_POST['counseling_notes'] ?? '';
                $formData['consent_obtained'] = isset($_POST['consent_obtained']);
                break;
                
            case 4:
                $formData['notes'] = $_POST['notes'] ?? '';
                
                // Final step - save consultation
                if (isset($_POST['save_consultation'])) {
                    return $this->saveConsultation($formData);
                }
                break;
        }
        
        // Save form data to session
        $_SESSION['std_testing_form_data'] = $formData;
        
        // Redirect to next step
        redirect('/std-testing/create/' . $nextStep);
    }
    
    /**
     * Generate AI recommendations based on patient data
     * 
     * @param array $formData Form data
     * @return array Array of recommendations
     */
    private function generateRecommendations($formData)
    {
        // Use the OpenAI service to generate recommendations
        return $this->openAIService->generateSTDTestingRecommendations($formData);
    }
    
    /**
     * Save consultation data to database
     * 
     * @param array $formData Form data
     */
    private function saveConsultation($formData)
    {
        // Prepare metadata
        $metadata = [
            'patient_age' => $formData['patient_age'],
            'patient_gender' => $formData['patient_gender'],
            'last_testing' => $formData['last_testing'],
            'symptoms' => $formData['symptoms'],
            'exposure_history' => $formData['exposure_history'],
            'risk_factors' => $formData['risk_factors'],
            'test_type' => $formData['test_type'],
            'sample_type' => $formData['sample_type'],
            'counseling_notes' => $formData['counseling_notes'],
            'consent_obtained' => $formData['consent_obtained']
        ];
        
        // Create consultation
        $consultationId = $this->consultationModel->create([
            'patient_id' => $formData['patient_id'],
            'consultation_type' => 'std_testing',
            'status' => 'in_progress',
            'notes' => $formData['notes'],
            'ai_recommendations' => json_encode($metadata) // Store metadata as AI recommendations
        ]);
        
        if ($consultationId) {
            // Clear session data
            unset($_SESSION['std_testing_form_data']);
            
            flash('success', 'STD testing consultation created successfully');
            redirect('/std-testing/' . $consultationId);
        } else {
            flash('error', 'Failed to create STD testing consultation');
            redirect('/std-testing/create/4');
        }
    }
    
    /**
     * Display STD testing consultation details
     * 
     * @param string $id Consultation ID
     */
    public function show($id)
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            redirect('/login');
        }
        
        // Get consultation details
        $consultation = $this->consultationModel->getById($id);
        
        if (!$consultation || $consultation['consultation_type'] !== 'std_testing') {
            flash('error', 'STD testing consultation not found');
            redirect('/std-testing');
        }
        
        $data = [
            'title' => 'STD Testing Consultation',
            'consultation' => $consultation
        ];
        
        $this->render('pages/std-testing/view', $data);
    }
}
