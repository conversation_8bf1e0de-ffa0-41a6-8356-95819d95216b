<?php

namespace App\Controllers;

use App\Core\Controller;
use App\Models\Consultation;
use App\Models\Patient;
use App\Models\User;
use App\Models\Appointment;

class Consultations extends Controller
{
    private $consultationModel;
    private $patientModel;
    private $userModel;
    private $appointmentModel;
    
    public function __construct()
    {
        parent::__construct();
        
        // Check if user is logged in
        if (!is_logged_in()) {
            redirect('login');
        }
        
        $this->consultationModel = new Consultation();
        $this->patientModel = new Patient();
        $this->userModel = new User();
        $this->appointmentModel = new Appointment();
    }
    
    /**
     * List all consultations with pagination and filters
     */
    public function index()
    {
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $perPage = 10;
        
        // Get filters from query parameters
        $filters = [
            'search' => $_GET['search'] ?? '',
            'date' => $_GET['date'] ?? ''
            // doctor_id filter removed as it doesn't exist in consultations table
        ];
        
        // Get consultations with pagination
        $result = $this->consultationModel->getAllWithPagination($page, $perPage, $filters);
        
        // Format consultation dates
        foreach ($result['consultations'] as &$consultation) {
            // Use started_at instead of consultation_date and handle null values
            $consultation['formatted_date'] = isset($consultation['started_at']) && $consultation['started_at'] 
                ? date('M d, Y h:i A', strtotime($consultation['started_at'])) 
                : 'N/A';
                
            // Ensure patient name is constructed safely
            $firstName = $consultation['first_name'] ?? '';
            $lastName = $consultation['last_name'] ?? '';
            $consultation['patient_name'] = !empty(trim($firstName . ' ' . $lastName)) 
                ? trim($firstName . ' ' . $lastName) 
                : 'Not specified';
            
            // Since doctor_id doesn't exist in consultations table, set doctor name to Not specified
            $consultation['doctor_name'] = 'Not specified';
            
            // Ensure chief_complaint and diagnosis have default values if not set
            $consultation['chief_complaint'] = $consultation['chief_complaint'] ?? 'Not specified';
            $consultation['diagnosis'] = $consultation['diagnosis'] ?? 'Not specified';
        }
        
        // Get all doctors for filter dropdown
        $doctors = $this->userModel->getDoctors();
        
        $this->render('pages/consultations/index', [
            'consultations' => $result['consultations'],
            'pagination' => $result['pagination'],
            'filters' => $filters,
            'doctors' => $doctors
        ]);
    }
    
    /**
     * Show a specific consultation (route handler)
     * 
     * @param string $id Consultation ID
     */
    public function show($id)
    {
        // Call the view method that displays consultation details
        return $this->viewConsultation($id);
    }
    
    /**
     * View a specific consultation
     * 
     * @param string $id Consultation ID
     */
    public function viewConsultation($id)
    {
        $consultation = $this->consultationModel->getById($id);
        
        if (!$consultation) {
            flash('error', 'Consultation not found');
            redirect('consultations');
        }
        
        // Format consultation date
        $consultation['formatted_date'] = date('M d, Y h:i A', strtotime($consultation['started_at']));
        $consultation['patient_name'] = $consultation['first_name'] . ' ' . $consultation['last_name'];
        
        // Add doctor name (if doctor_id exists)
        if (!empty($consultation['doctor_id'])) {
            // Fetch doctor name from users table
            $doctorQuery = "SELECT CONCAT(first_name, ' ', last_name) as doctor_name FROM users WHERE id = ?";
            $stmt = $this->db->getConnection()->prepare($doctorQuery);
            $stmt->execute([$consultation['doctor_id']]);
            $doctor = $stmt->fetch(\PDO::FETCH_ASSOC);
            $consultation['doctor_name'] = $doctor ? $doctor['doctor_name'] : 'Unknown Doctor';
        } else {
            $consultation['doctor_name'] = 'Not Assigned';
        }
        
        // Ensure chief_complaint exists
        if (!isset($consultation['chief_complaint'])) {
            $consultation['chief_complaint'] = '';
        }
        
        // Get patient's age
        if (!empty($consultation['date_of_birth'])) {
            $dob = new \DateTime($consultation['date_of_birth']);
            $now = new \DateTime();
            $interval = $now->diff($dob);
            $consultation['patient_age'] = $interval->y;
        } else {
            $consultation['patient_age'] = 'Unknown';
        }
        
        $this->render('pages/consultations/view', [
            'consultation' => $consultation
        ]);
    }
    
    /**
     * Show form to create a new consultation
     * 
     * @param string|null $appointmentId Optional appointment ID to pre-fill form
     */
    public function create($appointmentId = null)
    {
        // Get all patients and doctors for dropdowns
        $patients = $this->patientModel->getAll();
        $doctors = $this->userModel->getDoctors();
        
        $formData = [];
        $preselectedPatientId = '';
        $preselectedDoctorId = '';
        
        // If creating from appointment, pre-fill form data
        if ($appointmentId) {
            $appointment = $this->appointmentModel->getById($appointmentId);
            
            if ($appointment) {
                $formData = [
                    'patient_id' => $appointment['patient_id'],
                    'doctor_id' => $appointment['doctor_id'],
                    'started_at' => $appointment['appointment_date'],
                    'chief_complaint' => $appointment['reason'],
                    'notes' => $appointment['notes'],
                    'appointment_id' => $appointmentId
                ];
                
                $preselectedPatientId = $appointment['patient_id'];
                $preselectedDoctorId = $appointment['doctor_id'];
            }
        }
        
        // If form was submitted but had errors, restore previous input
        if (isset($_SESSION['form_data'])) {
            $formData = $_SESSION['form_data'];
            unset($_SESSION['form_data']);
        }
        
        $this->render('pages/consultations/create', [
            'patients' => $patients,
            'doctors' => $doctors,
            'formData' => $formData,
            'preselectedPatientId' => $preselectedPatientId,
            'preselectedDoctorId' => $preselectedDoctorId
        ]);
    }
    
    /**
     * Process the creation of a new consultation
     */
    public function store()
    {
        // Check CSRF token
        if (!csrf_check($_POST['csrf_token'])) {
            flash('error', 'Invalid form submission');
            redirect('consultations/create');
        }
        
        // Validate required fields
        $requiredFields = ['patient_id', 'doctor_id', 'started_at', 'chief_complaint'];
        $errors = [];
        
        foreach ($requiredFields as $field) {
            if (empty($_POST[$field])) {
                $errors[] = ucfirst(str_replace('_', ' ', $field)) . ' is required';
            }
        }
        
        if (!empty($errors)) {
            flash('error', implode('<br>', $errors));
            $_SESSION['form_data'] = $_POST;
            redirect('consultations/create');
        }
        
        // Create consultation
        $consultationData = [
            'patient_id' => $_POST['patient_id'],
            'doctor_id' => $_POST['doctor_id'],
            'started_at' => $_POST['started_at'],
            'chief_complaint' => $_POST['chief_complaint'],
            'history_of_present_illness' => $_POST['history_of_present_illness'] ?? null,
            'past_medical_history' => $_POST['past_medical_history'] ?? null,
            'medications' => $_POST['medications'] ?? null,
            'allergies' => $_POST['allergies'] ?? null,
            'physical_examination' => $_POST['physical_examination'] ?? null,
            'assessment' => $_POST['assessment'] ?? null,
            'diagnosis' => $_POST['diagnosis'] ?? null,
            'treatment_plan' => $_POST['treatment_plan'] ?? null,
            'follow_up' => $_POST['follow_up'] ?? null,
            'notes' => $_POST['notes'] ?? null,
            'appointment_id' => $_POST['appointment_id'] ?? null
        ];
        
        $consultationId = $this->consultationModel->create($consultationData);
        
        if ($consultationId) {
            // If consultation was created from an appointment, update appointment status to completed
            if (!empty($_POST['appointment_id'])) {
                $this->appointmentModel->update($_POST['appointment_id'], ['status' => 'completed']);
            }
            
            flash('success', 'Consultation created successfully');
            redirect('consultations/viewConsultation/' . $consultationId);
        } else {
            flash('error', 'Failed to create consultation');
            $_SESSION['form_data'] = $_POST;
            redirect('consultations/create');
        }
    }
    
    /**
     * Show form to edit a consultation
     * 
     * @param string $id Consultation ID
     */
    public function edit($id)
    {
        $consultation = $this->consultationModel->getById($id);
        
        if (!$consultation) {
            flash('error', 'Consultation not found');
            redirect('consultations');
        }
        
        // Get all patients and doctors for dropdowns
        $patients = $this->patientModel->getAll();
        $doctors = $this->userModel->getDoctors();
        
        // If form was submitted but had errors, restore previous input
        if (isset($_SESSION['form_data'])) {
            $formData = $_SESSION['form_data'];
            unset($_SESSION['form_data']);
        } else {
            $formData = $consultation;
        }
        
        $this->render('pages/consultations/edit', [
            'consultation' => $consultation,
            'patients' => $patients,
            'doctors' => $doctors,
            'formData' => $formData
        ]);
    }
    
    /**
     * Process the update of a consultation
     * 
     * @param string $id Consultation ID
     */
    public function update($id)
    {
        // Check CSRF token
        if (!csrf_check($_POST['csrf_token'])) {
            flash('error', 'Invalid form submission');
            redirect('consultations/edit/' . $id);
        }
        
        // Validate required fields
        $requiredFields = ['patient_id', 'doctor_id', 'started_at', 'chief_complaint'];
        $errors = [];
        
        foreach ($requiredFields as $field) {
            if (empty($_POST[$field])) {
                $errors[] = ucfirst(str_replace('_', ' ', $field)) . ' is required';
            }
        }
        
        if (!empty($errors)) {
            flash('error', implode('<br>', $errors));
            $_SESSION['form_data'] = $_POST;
            redirect('consultations/edit/' . $id);
        }
        
        // Update consultation
        $consultationData = [
            'patient_id' => $_POST['patient_id'],
            'doctor_id' => $_POST['doctor_id'],
            'started_at' => $_POST['started_at'],
            'chief_complaint' => $_POST['chief_complaint'],
            'history_of_present_illness' => $_POST['history_of_present_illness'] ?? null,
            'past_medical_history' => $_POST['past_medical_history'] ?? null,
            'medications' => $_POST['medications'] ?? null,
            'allergies' => $_POST['allergies'] ?? null,
            'physical_examination' => $_POST['physical_examination'] ?? null,
            'assessment' => $_POST['assessment'] ?? null,
            'diagnosis' => $_POST['diagnosis'] ?? null,
            'treatment_plan' => $_POST['treatment_plan'] ?? null,
            'follow_up' => $_POST['follow_up'] ?? null,
            'notes' => $_POST['notes'] ?? null
        ];
        
        $success = $this->consultationModel->update($id, $consultationData);
        
        if ($success) {
            flash('success', 'Consultation updated successfully');
            redirect('consultations/viewConsultation/' . $id);
        } else {
            flash('error', 'Failed to update consultation');
            $_SESSION['form_data'] = $_POST;
            redirect('consultations/edit/' . $id);
        }
    }
    
    /**
     * Delete a consultation
     * 
     * @param string $id Consultation ID
     */
    public function delete($id)
    {
        // Check if user is admin
        if (!isAdmin()) {
            flash('error', 'You do not have permission to delete consultations');
            redirect('consultations');
        }
        
        // Check CSRF token
        if (!csrf_check($_POST['csrf_token'])) {
            flash('error', 'Invalid form submission');
            redirect('consultations');
        }
        
        $consultation = $this->consultationModel->getById($id);
        
        if (!$consultation) {
            flash('error', 'Consultation not found');
            redirect('consultations');
        }
        
        $success = $this->consultationModel->delete($id);
        
        if ($success) {
            flash('success', 'Consultation deleted successfully');
        } else {
            flash('error', 'Failed to delete consultation');
        }
        
        redirect('consultations');
    }
    
    /**
     * Get consultations for a specific patient
     * 
     * @param string $patientId Patient ID
     */
    public function getByPatient($patientId)
    {
        $patient = $this->patientModel->getById($patientId);
        
        if (!$patient) {
            flash('error', 'Patient not found');
            redirect('patients');
        }
        
        $consultations = $this->consultationModel->getByPatientId($patientId);
        
        // Format consultation dates
        foreach ($consultations as &$consultation) {
            // Use started_at instead of consultation_date and handle null values
            $consultation['formatted_date'] = isset($consultation['started_at']) && $consultation['started_at'] 
                ? date('M d, Y h:i A', strtotime($consultation['started_at'])) 
                : 'N/A';
                
            // Ensure doctor name is constructed safely
            $doctorFirstName = $consultation['doctor_first_name'] ?? '';
            $doctorLastName = $consultation['doctor_last_name'] ?? '';
            $consultation['doctor_name'] = trim($doctorFirstName . ' ' . $doctorLastName);
        }
        
        $this->render('pages/consultations/patient', [
            'patient' => $patient,
            'consultations' => $consultations
        ]);
    }
    
    /**
     * Get consultations for a specific doctor
     * 
     * @param string $doctorId Doctor ID
     */
    public function getByDoctor($doctorId)
    {
        $doctor = $this->userModel->getById($doctorId);
        
        if (!$doctor || $doctor['role'] !== 'doctor') {
            flash('error', 'Doctor not found');
            redirect('users');
        }
        
        $date = $_GET['date'] ?? null;
        $consultations = $this->consultationModel->getByDoctorId($doctorId, $date);
        
        // Format consultation dates
        foreach ($consultations as &$consultation) {
            // Use started_at instead of consultation_date and handle null values
            $consultation['formatted_date'] = isset($consultation['started_at']) && $consultation['started_at'] 
                ? date('M d, Y h:i A', strtotime($consultation['started_at'])) 
                : 'N/A';
                
            // Ensure patient name is constructed safely
            $firstName = $consultation['first_name'] ?? '';
            $lastName = $consultation['last_name'] ?? '';
            $consultation['patient_name'] = trim($firstName . ' ' . $lastName);
        }
        
        $this->render('pages/consultations/doctor', [
            'doctor' => $doctor,
            'consultations' => $consultations,
            'date' => $date
        ]);
    }
    
    /**
     * Create a consultation from an appointment
     * 
     * @param string $appointmentId Appointment ID
     */
    public function createFromAppointment($appointmentId)
    {
        $appointment = $this->appointmentModel->getById($appointmentId);
        
        if (!$appointment) {
            flash('error', 'Appointment not found');
            redirect('appointments');
        }
        
        // Check if consultation already exists for this appointment
        $existingConsultations = $this->consultationModel->getByAppointmentId($appointmentId);
        
        if (!empty($existingConsultations)) {
            flash('info', 'A consultation already exists for this appointment');
            redirect('consultations/viewConsultation/' . $existingConsultations[0]['id']);
        }
        
        // Create consultation from appointment
        $consultationId = $this->consultationModel->createFromAppointment($appointment);
        
        if ($consultationId) {
            // Update appointment status to completed
            $this->appointmentModel->update($appointmentId, ['status' => 'completed']);
            
            flash('success', 'Consultation created successfully');
            redirect('consultations/edit/' . $consultationId);
        } else {
            flash('error', 'Failed to create consultation');
            redirect('appointments/view/' . $appointmentId);
        }
    }
    
    /**
     * Get statistics for consultations
     */
    public function statistics()
    {
        // Check if user is admin or doctor
        if (!isAdmin() && !isDoctor()) {
            flash('error', 'You do not have permission to view statistics');
            redirect('dashboard');
        }
        
        $period = $_GET['period'] ?? 'month';
        $stats = $this->consultationModel->getStatistics($period);
        
        $this->render('pages/consultations/statistics', [
            'stats' => $stats,
            'period' => $period
        ]);
    }
}
