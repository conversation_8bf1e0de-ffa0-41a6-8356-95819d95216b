<?php

namespace App\Controllers;

use App\Core\Controller;
use App\Models\Consultation;
use App\Models\Patient;
use App\Services\OpenAIService;

class ClinicalQA extends Controller
{
    private $consultationModel;
    private $patientModel;
    private $openAIService;
    
    public function __construct($params = [])
    {
        parent::__construct($params);
        $this->consultationModel = new Consultation();
        $this->patientModel = new Patient();
        $this->openAIService = new OpenAIService();
    }
    
    /**
     * Display list of clinical Q&A consultations
     */
    public function index()
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            redirect('login');
        }
        
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $perPage = 10;
        
        // Get filters from query parameters
        $filters = [
            'search' => $_GET['search'] ?? '',
            'consultation_type' => 'general_qa'
        ];
        
        // Get consultations with pagination
        $result = $this->consultationModel->getAllWithPagination($page, $perPage, $filters);
        
        $data = [
            'title' => 'Clinical Q&A Consultations',
            'consultations' => $result['consultations'],
            'pagination' => $result['pagination'],
            'filters' => $filters
        ];
        
        $this->render('pages/clinical-qa/index', $data);
    }
    
    /**
     * Display form to create a new clinical Q&A consultation
     */
    public function create()
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            redirect('login');
        }
        
        // Get all patients for dropdown
        $patients = $this->patientModel->getAll();
        
        $data = [
            'title' => 'New Clinical Q&A Consultation',
            'patients' => $patients
        ];
        
        $this->render('pages/clinical-qa/create', $data);
    }
    
    /**
     * Display clinical Q&A consultation details
     * 
     * @param string $id Consultation ID
     */
    public function show($id)
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            redirect('login');
        }
        
        // Get consultation details
        $consultation = $this->consultationModel->getById($id);
        
        if (!$consultation || $consultation['consultation_type'] !== 'general_qa') {
            flash('error', 'Clinical Q&A consultation not found');
            redirect('clinical-qa');
        }
        
        $data = [
            'title' => 'Clinical Q&A Consultation',
            'consultation' => $consultation
        ];
        
        $this->render('pages/clinical-qa/view', $data);
    }
    
    /**
     * Process a clinical question and generate AI response
     */
    public function askQuestion()
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            redirect('login');
        }
        
        // Handle POST request
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $question = trim($_POST['question'] ?? '');
            $patientId = $_POST['patient_id'] ?? null;
            
            if (empty($question)) {
                flash('error', 'Please enter a clinical question');
                redirect('clinical-qa/create');
            }
            
            try {
                // Get patient context if provided
                $context = [];
                if ($patientId) {
                    $patient = $this->patientModel->getById($patientId);
                    if ($patient) {
                        $context = [
                            'patient_age' => $patient['age'],
                            'patient_gender' => $patient['gender'],
                            'medical_history' => $patient['medical_history'] ?? ''
                        ];
                    }
                }
                
                // Generate AI response
                $aiResponse = $this->openAIService->generateClinicalQAResponse($question, $context);
                
                // Create consultation record
                $consultationData = [
                    'patient_id' => $patientId,
                    'appointment_id' => null,
                    'consultation_type' => 'general_qa',
                    'status' => 'completed',
                    'ai_recommendations' => json_encode($aiResponse),
                    'notes' => json_encode([
                        'question' => $question,
                        'context' => $context
                    ])
                ];
                
                $consultationId = $this->consultationModel->create($consultationData);
                
                if ($consultationId) {
                    flash('success', 'Clinical question processed successfully');
                    redirect('clinical-qa/' . $consultationId);
                } else {
                    flash('error', 'Failed to save consultation');
                    redirect('clinical-qa/create');
                }
                
            } catch (\Exception $e) {
                error_log('Error processing clinical question: ' . $e->getMessage());
                flash('error', 'An error occurred while processing your question. Please try again.');
                redirect('clinical-qa/create');
            }
        }
        
        // If not POST, redirect to create form
        redirect('clinical-qa/create');
    }
    
    /**
     * Handle AJAX requests for real-time Q&A
     */
    public function ajaxAsk()
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            http_response_code(401);
            echo json_encode(['error' => 'Unauthorized']);
            return;
        }
        
        // Only handle POST requests
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }
        
        // Get JSON input
        $input = json_decode(file_get_contents('php://input'), true);
        $question = trim($input['question'] ?? '');
        
        if (empty($question)) {
            http_response_code(400);
            echo json_encode(['error' => 'Question is required']);
            return;
        }
        
        try {
            // Generate AI response
            $aiResponse = $this->openAIService->generateClinicalQAResponse($question);
            
            // Return JSON response
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'response' => $aiResponse
            ]);
            
        } catch (\Exception $e) {
            error_log('Error in AJAX clinical Q&A: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'error' => 'An error occurred while processing your question',
                'fallback' => $this->openAIService->generateClinicalQAResponse($question)['answer']
            ]);
        }
    }
    
    /**
     * Handle AJAX requests for concise real-time Q&A (floating chat)
     */
    public function ajaxAskConcise()
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            http_response_code(401);
            echo json_encode(['error' => 'Unauthorized']);
            return;
        }
        
        // Only handle POST requests
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }
        
        // Get JSON input
        $input = json_decode(file_get_contents('php://input'), true);
        $question = trim($input['question'] ?? '');
        
        if (empty($question)) {
            http_response_code(400);
            echo json_encode(['error' => 'Question is required']);
            return;
        }
        
        try {
            // Generate concise AI response
            $aiResponse = $this->openAIService->generateConciseClinicalResponse($question);
            
            // Return JSON response
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'response' => $aiResponse
            ]);
            
        } catch (\Exception $e) {
            error_log('Error in AJAX concise clinical Q&A: ' . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'error' => 'An error occurred while processing your question',
                'fallback' => $this->openAIService->getFallbackConciseResponse($question)['answer']
            ]);
        }
    }
}
