<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Models\User;

/**
 * Auth Controller
 * Handles user authentication
 */
class Auth extends Controller
{
    /**
     * Display login form
     */
    public function login()
    {
        // If already logged in, redirect to dashboard
        if (is_logged_in()) {
            return $this->redirect(base_url());
        }
        
        $data = [
            'title' => 'Login',
            'layout' => 'auth' // Use a different layout for auth pages
        ];
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $username = $this->post('username');
            $password = $this->post('password');
            
            // Validate input
            if (empty($username) || empty($password)) {
                flash('error', 'Please enter both username and password');
                return $this->render('pages/login', $data);
            }
            
            // Check credentials
            $userModel = new User();
            $user = $userModel->findByUsername($username);
            
            if ($user && password_verify($password, $user['password'])) {
                // Login successful
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['user_role'] = $user['role'];
                
                // Update last login time
                $userModel->update($user['id'], [
                    'last_login' => date('Y-m-d H:i:s')
                ]);
                
                flash('success', 'Welcome back, ' . $user['first_name'] . '!');
                return $this->redirect(base_url());
            } else {
                // Login failed
                flash('error', 'Invalid username or password');
            }
        }
        
        $this->render('pages/login', $data);
    }
    
    /**
     * Log out the user
     */
    public function logout()
    {
        // Clear session data
        unset($_SESSION['user_id']);
        unset($_SESSION['username']);
        unset($_SESSION['user_role']);
        
        // Destroy the session
        session_destroy();
        
        // Redirect to login page
        $this->redirect(base_url('login'));
    }
}
