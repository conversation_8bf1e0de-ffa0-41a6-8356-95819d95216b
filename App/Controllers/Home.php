<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Models\Patient;
use App\Models\Appointment;
use App\Models\Consultation;

/**
 * Home Controller
 * Handles the main dashboard and home page
 */
class Home extends Controller
{
    /**
     * Display the dashboard
     */
    public function index()
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            return $this->redirect(base_url('login'));
        }
        
        // Get counts for dashboard
        $patientModel = new Patient();
        $appointmentModel = new Appointment();
        $consultationModel = new Consultation();
        
        $recentConsultations = $consultationModel->getRecent(5);
        
        // Format patient names for recent consultations
        foreach ($recentConsultations as &$consultation) {
            $consultation['patient_name'] = isset($consultation['first_name'], $consultation['last_name']) ? 
                trim($consultation['first_name'] . ' ' . $consultation['last_name']) : 
                'Not specified';
        }
        
        $data = [
            'title' => 'Dashboard',
            'patientCount' => $patientModel->count(),
            'appointmentCount' => $appointmentModel->count("status = 'scheduled'"),
            'todayAppointments' => $appointmentModel->getTodaysAppointments(),
            'recentConsultations' => $recentConsultations
        ];
        
        $this->render('pages/home', $data);
    }
}
