<?php

namespace App\Controllers;

use App\Core\Controller;
use App\Models\Consultation;
use App\Models\Patient;
use App\Models\TravelProtocol;
use App\Models\User;
use App\Services\OpenAIService;

class TravelMedicine extends Controller
{
    private $consultationModel;
    private $patientModel;
    private $travelProtocolModel;
    private $userModel;
    private $openAIService;
    
    public function __construct()
    {
        parent::__construct();
        $this->consultationModel = new Consultation();
        $this->patientModel = new Patient();
        $this->travelProtocolModel = new TravelProtocol();
        $this->userModel = new User();
        $this->openAIService = new OpenAIService();
    }
    
    /**
     * Display list of travel medicine consultations
     */
    public function index()
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            redirect('login');
        }
        
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $perPage = 10;
        
        // Get filters from query parameters
        $filters = [
            'search' => $_GET['search'] ?? '',
            'consultation_type' => 'travel_medicine'
        ];
        
        // Get consultations with pagination
        $result = $this->consultationModel->getAllWithPagination($page, $perPage, $filters);
        
        $data = [
            'title' => 'Travel Medicine Consultations',
            'consultations' => $result['consultations'],
            'pagination' => $result['pagination'],
            'filters' => $filters
        ];
        
        $this->render('pages/travel-medicine/index', $data);
    }
    
    /**
     * Display form to create a new travel medicine consultation
     * 
     * @param string $step Optional step parameter
     */
    public function create($step = '1')
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            redirect('login');
        }
        
        // Get protocol data from session if exists
        $protocolData = $_SESSION['travel_protocol_data'] ?? [];
        
        // Process based on step
        switch ($step) {
            case '1': // Travel Information
                $data = [
                    'title' => 'Travel Protocol - Travel Information',
                    'step' => 1,
                    'total_steps' => 5,
                    'protocol_data' => $protocolData
                ];
                $this->render('pages/travel-medicine/steps/step1', $data);
                break;
                
            case '2': // Patient Assessment
                // Validate that step 1 was completed
                if (empty($protocolData['destination'])) {
                    flash('error', 'Please complete the travel information first');
                    redirect('travel-medicine/create');
                }
                
                $data = [
                    'title' => 'Travel Protocol - Patient Assessment',
                    'step' => 2,
                    'total_steps' => 5,
                    'protocol_data' => $protocolData,
                    'patients' => $this->patientModel->all()
                ];
                $this->render('pages/travel-medicine/steps/step2', $data);
                break;
                
            case '3': // Risk Analysis
                // Validate that step 2 was completed
                if (empty($protocolData['patient_id'])) {
                    flash('error', 'Please complete the patient assessment first');
                    redirect('travel-medicine/create/2');
                }
                
                // Load patient data
                $patient = $this->patientModel->find($protocolData['patient_id']);
                
                // Don't generate AI recommendations here - load page immediately
                $data = [
                    'title' => 'Travel Protocol - Risk Analysis',
                    'step' => 3,
                    'total_steps' => 5,
                    'protocol_data' => $protocolData,
                    'destination' => $protocolData['destination'],
                    'patient' => $patient
                ];
                $this->render('pages/travel-medicine/steps/step3_new', $data);
                break;
                
            case '4': // Vaccination Plan
                // Validate that step 3 was completed
                if (empty($protocolData['risk_assessment_completed'])) {
                    flash('error', 'Please complete the risk analysis first');
                    redirect('travel-medicine/create/3');
                }
                
                // Use AI recommendations from session (generated in step 3)
                $aiRecommendations = $protocolData['ai_recommendations'] ?? null;
                
                // Get selected vaccinations from session
                $selectedVaccinations = $protocolData['selected_vaccinations'] ?? [];
                
                $data = [
                    'title' => 'Travel Protocol - Vaccination Plan',
                    'step' => 4,
                    'total_steps' => 5,
                    'protocol_data' => $protocolData,
                    'ai_recommendations' => $aiRecommendations,
                    'selected_vaccinations' => $selectedVaccinations,
                    'destination' => $protocolData['destination'],
                    'duration' => $protocolData['duration']
                ];
                $this->render('pages/travel-medicine/steps/step4_new', $data);
                break;
                
            case '5': // Documentation
                // Validate that step 4 was completed
                if (empty($protocolData['vaccination_plan_completed'])) {
                    flash('error', 'Please complete the vaccination plan first');
                    redirect('travel-medicine/create/4');
                }
                
                $data = [
                    'title' => 'Travel Protocol - Documentation',
                    'step' => 5,
                    'total_steps' => 5,
                    'protocol_data' => $protocolData,
                    'doctors' => $this->userModel->getDoctors()
                ];
                $this->render('pages/travel-medicine/steps/step5_new', $data);
                break;
                
            default:
                redirect('travel-medicine/create');
        }
    }
    
    /**
     * Display travel medicine consultation details
     * 
     * @param string $id Consultation ID
     */
    public function show($id)
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            redirect('login');
        }
        
        // Get consultation details
        $consultation = $this->consultationModel->getById($id);
        
        if (!$consultation || $consultation['consultation_type'] !== 'travel_medicine') {
            flash('error', 'Travel medicine consultation not found');
            redirect('travel-medicine');
        }
        
        // Get travel protocol details
        $protocol = $this->travelProtocolModel->getProtocolByConsultationId($consultation['id']);
        
        $data = [
            'title' => 'Travel Medicine Consultation',
            'consultation' => $consultation,
            'protocol' => $protocol
        ];
        
        $this->render('pages/travel-medicine/view', $data);
    }
    
    /**
     * Process step form submission
     */
    public function processStep()
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            redirect('login');
        }
        
        // Check for CSRF token
        if (!isset($_POST['csrf_token']) || !csrf_check($_POST['csrf_token'])) {
            flash('error', 'Invalid form submission');
            redirect('travel-medicine/create');
        }
        
        // Get current protocol data from session
        $protocolData = $_SESSION['travel_protocol_data'] ?? [];
        
        // Get current step
        $step = $this->post('step', '1');
        
        // Process based on step
        switch ($step) {
            case '1': // Travel Information
                $protocolData['destination'] = $this->post('destination');
                $protocolData['travel_type'] = $this->post('travel_type');
                $protocolData['duration'] = $this->post('duration');
                $protocolData['departure_date'] = $this->post('departure_date');
                
                // Validate required fields
                if (empty($protocolData['destination'])) {
                    flash('error', 'Destination is required');
                    redirect('travel-medicine/create');
                }
                
                // Save to session and proceed to next step
                $_SESSION['travel_protocol_data'] = $protocolData;
                redirect('travel-medicine/create/2');
                break;
                
            case '2': // Patient Assessment
                $protocolData['patient_id'] = $this->post('patient_id');
                $protocolData['age'] = $this->post('age');
                $protocolData['gender'] = $this->post('gender');
                $protocolData['pregnancy_status'] = $this->post('pregnancy_status');
                $protocolData['allergies'] = $this->post('allergies');
                
                // Validate required fields
                if (empty($protocolData['patient_id'])) {
                    flash('error', 'Patient selection is required');
                    redirect('travel-medicine/create/2');
                }
                
                // Save to session and proceed to next step
                $_SESSION['travel_protocol_data'] = $protocolData;
                redirect('travel-medicine/create/3');
                break;
                
            case '3': // Risk Analysis
                $protocolData['risk_assessment'] = $this->post('risk_assessment');
                $protocolData['risk_assessment_completed'] = true;
                
                // Store AI recommendations for use in step 4
                if (isset($_POST['ai_recommendations'])) {
                    $protocolData['ai_recommendations'] = json_decode($_POST['ai_recommendations'], true);
                }
                
                // Store selected vaccinations for use in step 4
                if (isset($_POST['selected_vaccinations']) && !empty($_POST['selected_vaccinations'])) {
                    $protocolData['selected_vaccinations'] = json_decode($_POST['selected_vaccinations'], true);
                }
                
                // Save to session and proceed to next step
                $_SESSION['travel_protocol_data'] = $protocolData;
                redirect('travel-medicine/create/4');
                break;
                
            case '4': // Vaccination Plan
                $protocolData['vaccination_plan'] = $this->post('vaccination_plan');
                $protocolData['vaccination_plan_completed'] = true;
                
                // Save to session and proceed to next step
                $_SESSION['travel_protocol_data'] = $protocolData;
                redirect('travel-medicine/create/5');
                break;
                
            case '5': // Documentation
                $protocolData['doctor_id'] = $this->post('doctor_id');
                $protocolData['notes'] = $this->post('notes');
                
                // Create the travel protocol in the database
                try {
                    $id = $this->travelProtocolModel->createProtocol($protocolData);
                    
                    // Clear session data
                    unset($_SESSION['travel_protocol_data']);
                    
                    flash('success', 'Travel protocol created successfully');
                    redirect('travel-medicine/' . $id);
                } catch (\Exception $e) {
                    flash('error', 'Error creating travel protocol: ' . $e->getMessage());
                    redirect('travel-medicine/create/5');
                }
                break;
                
            default:
                redirect('travel-medicine/create');
        }
    }
    
    /**
     * Go back to previous step
     * 
     * @param string $currentStep Current step
     */
    public function previousStep($currentStep)
    {
        $prevStep = max(1, (int)$currentStep - 1);
        redirect('travel-medicine/create/' . $prevStep);
    }
    
    /**
     * AJAX endpoint to generate AI recommendations
     */
    public function generateAIRecommendations()
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            http_response_code(401);
            echo json_encode(['error' => 'Unauthorized']);
            return;
        }
        
        // Only handle POST requests
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }
        
        // Get protocol data from session
        $protocolData = $_SESSION['travel_protocol_data'] ?? [];
        
        if (empty($protocolData['patient_id']) || empty($protocolData['destination'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Missing required protocol data']);
            return;
        }
        
        try {
            // Generate AI recommendations
            $patient = $this->patientModel->find($protocolData['patient_id']);
            $travelData = array_merge($protocolData, [
                'gender' => $patient['gender'] ?? null
            ]);
            
            $aiRecommendations = $this->openAIService->generateTravelMedicineRecommendations($travelData);
            
            // Store recommendations in session for step 4
            $_SESSION['travel_protocol_data']['ai_recommendations'] = $aiRecommendations;
            
            // Return JSON response
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'recommendations' => $aiRecommendations
            ]);
            
        } catch (\Exception $e) {
            error_log('Error generating AI recommendations: ' . $e->getMessage());
            
            // Return fallback recommendations
            $fallbackRecommendations = [
                'destination' => $protocolData['destination'],
                'risk_assessment' => 'Unable to generate AI recommendations at this time. Please consult current LCR guidelines for ' . $protocolData['destination'] . '.',
                'required_vaccinations' => 'Please check LCR requirements for mandatory vaccinations for this destination.',
                'recommended_vaccinations' => 'Standard travel vaccines may be recommended based on destination risk profile.',
                'malaria_prevention' => 'Consult LCR guidelines for malaria risk assessment if applicable.',
                'general_advice' => 'Follow standard travel health precautions including food and water safety.',
                'special_considerations' => 'Consider patient-specific factors and medical history.',
                'full_response' => 'AI recommendations are temporarily unavailable. Please consult the latest LCR (Dutch Travel Health Guidelines) for comprehensive travel health advice.',
                'sources' => ['LCR Guidelines'],
                'model_used' => 'fallback',
                'timestamp' => date('Y-m-d H:i:s')
            ];
            
            // Store fallback in session
            $_SESSION['travel_protocol_data']['ai_recommendations'] = $fallbackRecommendations;
            
            http_response_code(200); // Still return success with fallback
            echo json_encode([
                'success' => true,
                'recommendations' => $fallbackRecommendations,
                'fallback' => true
            ]);
        }
    }
}
