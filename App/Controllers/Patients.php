<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Models\Patient;

/**
 * Patients Controller
 * Handles patient management
 */
class Patients extends Controller
{
    /**
     * Display list of patients
     */
    public function index()
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            return $this->redirect(base_url('login'));
        }
        
        // Get pagination parameters
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $perPage = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 10;
        $search = isset($_GET['search']) ? $_GET['search'] : '';
        
        // Get patients
        $patientModel = new Patient();
        $result = $patientModel->getAllPaginated($page, $perPage, $search);
        
        $data = [
            'title' => 'Patients',
            'patients' => $result['patients'],
            'pagination' => $result['pagination'],
            'search' => $search
        ];
        
        $this->render('pages/patients/index', $data);
    }
    
    /**
     * Display patient details
     * 
     * @param string $id Patient ID
     */
    public function view($id)
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            return $this->redirect(base_url('login'));
        }
        
        // Get patient
        $patientModel = new Patient();
        $patient = $patientModel->getWithMedicalHistory($id);
        
        if (!$patient) {
            flash('error', 'Patient not found');
            return $this->redirect(base_url('patients'));
        }
        
        // Get patient appointments, consultations, and documents
        $appointments = $patientModel->getAppointments($id);
        $consultations = $patientModel->getConsultations($id);
        $documents = $patientModel->getDocuments($id);
        
        $data = [
            'title' => 'Patient: ' . $patient['first_name'] . ' ' . $patient['last_name'],
            'patient' => $patient,
            'appointments' => $appointments,
            'consultations' => $consultations,
            'documents' => $documents
        ];
        
        $this->render('pages/patients/view', $data);
    }
    
    /**
     * Display form to create a new patient
     */
    public function create()
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            return $this->redirect(base_url('login'));
        }
        
        $data = [
            'title' => 'Add New Patient'
        ];
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate CSRF token
            if (!csrf_check($_POST['csrf_token'] ?? '')) {
                flash('error', 'Invalid form submission');
                return $this->render('pages/patients/create', $data);
            }
            
            // Get form data
            $patientData = [
                'first_name' => $this->post('first_name'),
                'last_name' => $this->post('last_name'),
                'email' => !empty($this->post('email')) ? $this->post('email') : null,
                'phone' => $this->post('phone'),
                'date_of_birth' => $this->post('date_of_birth'),
                'gender' => $this->post('gender'),
                'address' => $this->post('address'),
                'emergency_contact_name' => $this->post('emergency_contact_name'),
                'emergency_contact_phone' => $this->post('emergency_contact_phone'),
                'insurance_provider' => $this->post('insurance_provider'),
                'insurance_policy_number' => $this->post('insurance_policy_number')
            ];
            
            // Validate required fields
            $requiredFields = ['first_name', 'last_name', 'date_of_birth', 'gender'];
            $errors = [];
            
            foreach ($requiredFields as $field) {
                if (empty($patientData[$field])) {
                    $errors[] = ucfirst(str_replace('_', ' ', $field)) . ' is required';
                }
            }
            
            // Validate email if provided
            if (!empty($patientData['email']) && !filter_var($patientData['email'], FILTER_VALIDATE_EMAIL)) {
                $errors[] = 'Invalid email format';
            }
            
            if (!empty($errors)) {
                flash('error', implode('<br>', $errors));
                $data['formData'] = $patientData;
                return $this->render('pages/patients/create', $data);
            }
            
            // Create patient
            $patientModel = new Patient();
            $patientId = $patientModel->create($patientData);
            
            if (!$patientId) {
                flash('error', 'Failed to create patient. Please try again.');
                $data['formData'] = $patientData;
                return $this->render('pages/patients/create', $data);
            }
            
            // Create medical history if provided
            $medicalHistoryData = [
                'patient_id' => $patientId,
                'allergies' => $this->post('allergies'),
                'current_medications' => $this->post('current_medications'),
                'previous_surgeries' => $this->post('past_surgeries'),
                'chronic_conditions' => $this->post('chronic_conditions'),
                'family_history' => $this->post('family_history'),
                'smoking_status' => $this->post('smoking_status'),
                'alcohol_consumption' => $this->post('alcohol_consumption')
            ];
            
            $medicalHistoryResult = $patientModel->createMedicalHistory($medicalHistoryData);
            if (!$medicalHistoryResult) {
                flash('warning', 'Patient created but medical history could not be saved.');
            }
            
            flash('success', 'Patient created successfully');
            return $this->redirect(base_url('patients/' . $patientId));
        }
        
        $this->render('pages/patients/create', $data);
    }
    
    /**
     * Display form to edit a patient
     * 
     * @param string $id Patient ID
     */
    public function edit($id)
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            return $this->redirect(base_url('login'));
        }
        
        // Get patient
        $patientModel = new Patient();
        $patient = $patientModel->getWithMedicalHistory($id);
        
        if (!$patient) {
            flash('error', 'Patient not found');
            return $this->redirect(base_url('patients'));
        }
        
        $data = [
            'title' => 'Edit Patient: ' . $patient['first_name'] . ' ' . $patient['last_name'],
            'patient' => $patient,
            'formData' => $patient
        ];
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate CSRF token
            if (!csrf_check($_POST['csrf_token'] ?? '')) {
                flash('error', 'Invalid form submission');
                return $this->render('pages/patients/edit', $data);
            }
            
            // Get form data
            $patientData = [
                'first_name' => $this->post('first_name'),
                'last_name' => $this->post('last_name'),
                'email' => !empty($this->post('email')) ? $this->post('email') : null,
                'phone' => $this->post('phone'),
                'date_of_birth' => $this->post('date_of_birth'),
                'gender' => $this->post('gender'),
                'address' => $this->post('address'),
                'emergency_contact_name' => $this->post('emergency_contact_name'),
                'emergency_contact_phone' => $this->post('emergency_contact_phone'),
                'insurance_provider' => $this->post('insurance_provider'),
                'insurance_policy_number' => $this->post('insurance_policy_number')
            ];
            
            // Validate required fields
            $requiredFields = ['first_name', 'last_name', 'date_of_birth', 'gender'];
            $errors = [];
            
            foreach ($requiredFields as $field) {
                if (empty($patientData[$field])) {
                    $errors[] = ucfirst(str_replace('_', ' ', $field)) . ' is required';
                }
            }
            
            // Validate email if provided
            if (!empty($patientData['email']) && !filter_var($patientData['email'], FILTER_VALIDATE_EMAIL)) {
                $errors[] = 'Invalid email format';
            }
            
            if (!empty($errors)) {
                flash('error', implode('<br>', $errors));
                $data['formData'] = $patientData;
                return $this->render('pages/patients/edit', $data);
            }
            
            // Update patient
            $patientModel->update($id, $patientData);
            
            // Update medical history
            $medicalHistoryData = [
                'allergies' => $this->post('allergies'),
                'current_medications' => $this->post('current_medications'),
                'previous_surgeries' => $this->post('past_surgeries'),
                'chronic_conditions' => $this->post('chronic_conditions'),
                'family_history' => $this->post('family_history'),
                'smoking_status' => $this->post('smoking_status'),
                'alcohol_consumption' => $this->post('alcohol_consumption')
            ];
            
            $patientModel->updateMedicalHistory($id, $medicalHistoryData);
            
            flash('success', 'Patient updated successfully');
            return $this->redirect(base_url('patients/' . $id));
        }
        
        $this->render('pages/patients/edit', $data);
    }
    
    /**
     * Delete a patient
     * 
     * @param string $id Patient ID
     */
    public function delete($id)
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            return $this->redirect(base_url('login'));
        }
        
        // Check if user has admin role
        if ($_SESSION['user_role'] !== 'admin') {
            flash('error', 'You do not have permission to delete patients');
            return $this->redirect(base_url('patients'));
        }
        
        // Validate CSRF token
        if (!csrf_check($_POST['csrf_token'] ?? '')) {
            flash('error', 'Invalid request');
            return $this->redirect(base_url('patients'));
        }
        
        // Get patient
        $patientModel = new Patient();
        $patient = $patientModel->findById($id);
        
        if (!$patient) {
            flash('error', 'Patient not found');
            return $this->redirect(base_url('patients'));
        }
        
        // Delete patient
        $patientModel->delete($id);
        
        flash('success', 'Patient deleted successfully');
        return $this->redirect(base_url('patients'));
    }
    
    /**
     * Show patient details (alias for view method)
     * 
     * @param string $id Patient ID
     */
    public function show($id)
    {
        return $this->view($id);
    }
}
