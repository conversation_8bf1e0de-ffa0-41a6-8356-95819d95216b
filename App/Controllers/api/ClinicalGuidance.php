<?php
namespace App\Controllers\Api;

use App\Core\Controller;
use App\Services\OpenAIService;

/**
 * API Clinical Guidance Controller
 * Handles API requests for clinical guidance
 */
class ClinicalGuidance extends Controller
{
    /**
     * Get clinical guidance
     */
    public function index()
    {
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!isset($data['question'])) {
            return $this->json(['error' => 'Question is required'], 400);
        }
        
        $question = $data['question'];
        $context = $data['context'] ?? [];
        
        $openAIService = new OpenAIService();
        $response = $openAIService->generateClinicalQAResponse($question, $context);
        
        return $this->json($response);
    }
}
