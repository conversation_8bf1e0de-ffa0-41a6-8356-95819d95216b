<?php
namespace App\Controllers\Api;

use App\Core\Controller;
use App\Models\Patient;

/**
 * API Patients Controller
 * Handles API requests for patient data
 */
class Patients extends Controller
{
    /**
     * Get all patients
     */
    public function index()
    {
        $patientModel = new Patient();
        $patients = $patientModel->getAll();
        
        return $this->json($patients);
    }
    
    /**
     * Get a specific patient
     */
    public function show()
    {
        $id = $this->params['id'] ?? null;
        
        if (!$id) {
            return $this->json(['error' => 'Patient ID is required'], 400);
        }
        
        $patientModel = new Patient();
        $patient = $patientModel->findById($id);
        
        if (!$patient) {
            return $this->json(['error' => 'Patient not found'], 404);
        }
        
        return $this->json($patient);
    }
    
    /**
     * Create a new patient
     */
    public function create()
    {
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            return $this->json(['error' => 'Invalid request data'], 400);
        }
        
        $patientModel = new Patient();
        $id = $patientModel->create($data);
        
        if (!$id) {
            return $this->json(['error' => 'Failed to create patient'], 500);
        }
        
        return $this->json(['id' => $id, 'message' => 'Patient created successfully'], 201);
    }
    
    /**
     * Update a patient
     */
    public function update()
    {
        $id = $this->params['id'] ?? null;
        
        if (!$id) {
            return $this->json(['error' => 'Patient ID is required'], 400);
        }
        
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            return $this->json(['error' => 'Invalid request data'], 400);
        }
        
        $patientModel = new Patient();
        $success = $patientModel->update($id, $data);
        
        if (!$success) {
            return $this->json(['error' => 'Failed to update patient'], 500);
        }
        
        return $this->json(['message' => 'Patient updated successfully']);
    }
    
    /**
     * Delete a patient
     */
    public function delete()
    {
        $id = $this->params['id'] ?? null;
        
        if (!$id) {
            return $this->json(['error' => 'Patient ID is required'], 400);
        }
        
        $patientModel = new Patient();
        $success = $patientModel->delete($id);
        
        if (!$success) {
            return $this->json(['error' => 'Failed to delete patient'], 500);
        }
        
        return $this->json(['message' => 'Patient deleted successfully']);
    }
}
