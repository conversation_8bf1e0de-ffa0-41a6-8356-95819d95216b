<?php
namespace App\Controllers\Api;

use App\Core\Controller;
use App\Models\Consultation;

/**
 * API Consultations Controller
 * Handles API requests for consultation data
 */
class Consultations extends Controller
{
    /**
     * Get all consultations
     */
    public function index()
    {
        $consultationModel = new Consultation();
        $consultations = $consultationModel->getAll();
        
        return $this->json($consultations);
    }
    
    /**
     * Get a specific consultation
     */
    public function show()
    {
        $id = $this->params['id'] ?? null;
        
        if (!$id) {
            return $this->json(['error' => 'Consultation ID is required'], 400);
        }
        
        $consultationModel = new Consultation();
        $consultation = $consultationModel->findById($id);
        
        if (!$consultation) {
            return $this->json(['error' => 'Consultation not found'], 404);
        }
        
        return $this->json($consultation);
    }
    
    /**
     * Create a new consultation
     */
    public function create()
    {
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            return $this->json(['error' => 'Invalid request data'], 400);
        }
        
        $consultationModel = new Consultation();
        $id = $consultationModel->create($data);
        
        if (!$id) {
            return $this->json(['error' => 'Failed to create consultation'], 500);
        }
        
        return $this->json(['id' => $id, 'message' => 'Consultation created successfully'], 201);
    }
    
    /**
     * Update a consultation
     */
    public function update()
    {
        $id = $this->params['id'] ?? null;
        
        if (!$id) {
            return $this->json(['error' => 'Consultation ID is required'], 400);
        }
        
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            return $this->json(['error' => 'Invalid request data'], 400);
        }
        
        $consultationModel = new Consultation();
        $success = $consultationModel->update($id, $data);
        
        if (!$success) {
            return $this->json(['error' => 'Failed to update consultation'], 500);
        }
        
        return $this->json(['message' => 'Consultation updated successfully']);
    }
}
