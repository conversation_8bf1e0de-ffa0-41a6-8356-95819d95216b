<?php

namespace App\Controllers;

use App\Core\Controller;
use App\Models\Appointment;
use App\Models\Patient;
use App\Models\User;

class Appointments extends Controller
{
    protected $appointmentModel;
    protected $patientModel;
    protected $userModel;
    
    public function __construct($params = [])
    {
        parent::__construct($params);
        $this->appointmentModel = new Appointment();
        $this->patientModel = new Patient();
        $this->userModel = new User();
    }
    
    /**
     * Display appointments list
     */
    public function index()
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            redirect('login');
        }
        
        // Get page number from query string
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $page = max(1, $page); // Ensure page is at least 1
        
        // Get filters from query string
        $filters = [
            'search' => $_GET['search'] ?? '',
            'date' => $_GET['date'] ?? '',
            'status' => $_GET['status'] ?? ''
        ];
        
        // Get appointments with pagination
        $result = $this->appointmentModel->getAllWithPagination($page, 10, $filters);
        
        $data = [
            'title' => 'Appointments',
            'appointments' => $result['appointments'],
            'pagination' => $result['pagination'],
            'filters' => $filters
        ];
        
        $this->render('pages/appointments/index', $data);
    }
    
    /**
     * Display appointment creation form
     */
    public function create()
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            redirect('login');
        }
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate CSRF token
            if (!csrf_check($_POST['csrf_token'])) {
                flash('error', 'Invalid security token');
                redirect('appointments/create');
            }
            
            // Validate form data
            $errors = [];
            
            if (empty($_POST['patient_id'])) {
                $errors[] = 'Patient is required';
            }
            
            if (empty($_POST['appointment_date'])) {
                $errors[] = 'Appointment date and time is required';
            }
            
            if (empty($_POST['reason_for_visit'])) {
                $errors[] = 'Reason for appointment is required';
            }
            
            // Check if time slot is available
            if (!empty($_POST['appointment_date'])) {
                $isAvailable = $this->appointmentModel->isTimeSlotAvailable(
                    null,
                    $_POST['appointment_date'],
                    $_POST['duration_minutes'] ?? 30
                );
                
                if (!$isAvailable) {
                    $errors[] = 'The selected time slot is not available';
                }
            }
            
            if (empty($errors)) {
                // Create appointment
                $appointmentData = [
                    'patient_id' => $_POST['patient_id'],
                    'appointment_date' => $_POST['appointment_date'],
                    'duration_minutes' => $_POST['duration_minutes'] ?? 30,
                    'reason_for_visit' => $_POST['reason_for_visit'],
                    'status' => $_POST['status'] ?? 'scheduled',
                    'notes' => $_POST['notes'] ?? null
                ];
                
                $appointmentId = $this->appointmentModel->create($appointmentData);
                
                if ($appointmentId) {
                    flash('success', 'Appointment created successfully');
                    redirect('appointments');
                } else {
                    flash('error', 'Failed to create appointment');
                }
            } else {
                // If there are errors, set flash message and keep form data
                flash('error', implode('<br>', $errors));
                $formData = $_POST;
            }
        } else {
            // Initialize empty form data
            $formData = [
                'status' => 'scheduled'
            ];
        }
        
        // Get patients for dropdown
        $patients = $this->patientModel->getAll();
        
        // Get doctors for dropdown
        $doctors = $this->userModel->getDoctors();
        
        // Check if patient ID was passed in the URL query string
        $preselectedPatientId = isset($_GET['patient_id']) ? $_GET['patient_id'] : null;
        
        $data = [
            'title' => 'Create Appointment',
            'patients' => $patients,
            'doctors' => $doctors,
            'formData' => $formData,
            'statuses' => ['scheduled', 'completed', 'cancelled', 'no_show'],
            'preselectedPatientId' => $preselectedPatientId
        ];
        
        $this->render('pages/appointments/create', $data);
    }
    
    /**
     * Display appointment edit form
     * 
     * @param string $id Appointment ID
     */
    public function edit($id)
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            redirect('login');
        }
        
        // Get appointment details
        $appointment = $this->appointmentModel->getById($id);
        
        if (!$appointment) {
            flash('error', 'Appointment not found');
            redirect('appointments');
        }
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate CSRF token
            if (!csrf_check($_POST['csrf_token'])) {
                flash('error', 'Invalid security token');
                redirect('appointments/edit/' . $id);
            }
            
            // Validate form data
            $errors = [];
            
            if (empty($_POST['patient_id'])) {
                $errors[] = 'Patient is required';
            }
            
            // Doctor validation removed as there is no doctor_id field
            
            if (empty($_POST['appointment_date'])) {
                $errors[] = 'Appointment date and time is required';
            }
            
            if (empty($_POST['reason_for_visit'])) {
                $errors[] = 'Reason for appointment is required';
            }
            
            // Check if time slot is available (excluding this appointment)
            if (!empty($_POST['appointment_date'])) {
                $isAvailable = $this->appointmentModel->isTimeSlotAvailable(
                    null,
                    $_POST['appointment_date'],
                    $_POST['duration_minutes'] ?? 30,
                    $id
                );
                
                if (!$isAvailable) {
                    $errors[] = 'The selected time slot is not available';
                }
            }
            
            if (empty($errors)) {
                // Update appointment
                $appointmentData = [
                    'patient_id' => $_POST['patient_id'],
                    'appointment_date' => $_POST['appointment_date'],
                    'duration_minutes' => $_POST['duration_minutes'] ?? 30,
                    'reason_for_visit' => $_POST['reason_for_visit'],
                    'status' => $_POST['status'],
                    'notes' => $_POST['notes'] ?? null
                ];
                
                $success = $this->appointmentModel->update($id, $appointmentData);
                
                if ($success) {
                    flash('success', 'Appointment updated successfully');
                    redirect('appointments');
                } else {
                    flash('error', 'Failed to update appointment');
                }
            } else {
                // If there are errors, set flash message and keep form data
                flash('error', implode('<br>', $errors));
                $formData = $_POST;
            }
        } else {
            // Prepare form data from appointment
            $formData = $appointment;
        }
        
        // Get patients for dropdown
        $patients = $this->patientModel->getAll();
        
        $data = [
            'title' => 'Edit Appointment',
            'appointment' => $appointment,
            'patients' => $patients,
            'formData' => $formData,
            'statuses' => ['scheduled', 'completed', 'cancelled', 'no_show']
        ];
        
        $this->render('pages/appointments/edit', $data);
    }
    
    /**
     * Update appointment status
     * 
     * @param string $id Appointment ID
     * @param string $status New status
     */
    public function updateStatus($id, $status)
    {
        // Debug information
        error_log("updateStatus called with ID: {$id}, Status: {$status}");
        error_log("Request method: {$_SERVER['REQUEST_METHOD']}");
        
        // Check if user is logged in
        if (!is_logged_in()) {
            $this->json(['success' => false, 'message' => 'Unauthorized'], 401);
            return;
        }
        
        // Validate status
        $validStatuses = ['scheduled', 'completed', 'cancelled', 'no_show'];
        if (!in_array($status, $validStatuses)) {
            $this->json(['success' => false, 'message' => 'Invalid status'], 400);
            return;
        }
        
        // Update appointment status
        $success = $this->appointmentModel->updateStatus($id, $status);
        
        if ($success) {
            $this->json(['success' => true, 'message' => 'Status updated successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to update status'], 500);
        }
    }
    
    /**
     * Display appointment details
     * 
     * @param string $id Appointment ID
     */
    public function show($id)
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            redirect('login');
        }
        
        // Get appointment details
        $appointment = $this->appointmentModel->getById($id);
        
        if (!$appointment) {
            flash('error', 'Appointment not found');
            redirect('appointments');
        }
        
        $data = [
            'title' => 'Appointment Details',
            'appointment' => $appointment
        ];
        
        $this->render('pages/appointments/show', $data);
    }
    
    /**
     * Delete appointment
     * 
     * @param string $id Appointment ID
     */
    public function delete($id)
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            redirect('login');
        }
        
        // Check if appointment exists
        $appointment = $this->appointmentModel->getById($id);
        
        if (!$appointment) {
            flash('error', 'Appointment not found');
            redirect('appointments');
        }
        
        // Delete appointment
        $success = $this->appointmentModel->delete($id);
        
        if ($success) {
            flash('success', 'Appointment deleted successfully');
        } else {
            flash('error', 'Failed to delete appointment');
        }
        
        redirect('appointments');
    }
    
    /**
     * Get available time slots for a specific date
     */
    public function getAvailableTimeSlots()
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            $this->json(['success' => false, 'message' => 'Unauthorized'], 401);
            return;
        }
        
        // Get date from query string
        $date = $_GET['date'] ?? date('Y-m-d');
        
        // Generate time slots from 9:00 to 17:00 with 30-minute intervals
        $timeSlots = [];
        $startHour = 9;
        $endHour = 17;
        $slotDuration = 30; // minutes
        
        $currentTime = new \DateTime($date);
        $currentTime->setTime($startHour, 0, 0);
        
        $endTime = new \DateTime($date);
        $endTime->setTime($endHour, 0, 0);
        
        while ($currentTime < $endTime) {
            $startTimeStr = $currentTime->format('H:i');
            
            // Create end time (30 minutes later)
            $endTimeObj = clone $currentTime;
            $endTimeObj->modify("+{$slotDuration} minutes");
            $endTimeStr = $endTimeObj->format('H:i');
            
            // Format the datetime value for the option
            $datetimeValue = $currentTime->format('Y-m-d H:i:s');
            
            // Check if time slot is available
            $isAvailable = $this->appointmentModel->isTimeSlotAvailable(
                null,
                $datetimeValue,
                $slotDuration
            );
            
            $timeSlots[] = [
                'start_time' => $startTimeStr,
                'end_time' => $endTimeStr,
                'datetime' => $datetimeValue,
                'available' => $isAvailable
            ];
            
            // Move to next slot
            $currentTime = $endTimeObj;
        }
        
        $this->json(['success' => true, 'timeSlots' => $timeSlots]);
    }
    
    /**
     * Display calendar view
     */
    public function calendar()
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            redirect('login');
        }
        
        // Get year and month from query string or use current date
        $year = isset($_GET['year']) ? (int)$_GET['year'] : date('Y');
        $month = isset($_GET['month']) ? (int)$_GET['month'] : date('n');
        
        // Create DateTime object for the selected month
        $currentDate = new \DateTime();
        $currentDate->setDate($year, $month, 1);
        
        // Get appointments for the month
        $startDate = $currentDate->format('Y-m-01');
        $endDate = $currentDate->format('Y-m-t');
        $appointments = $this->appointmentModel->getAppointmentsByDateRange($startDate, $endDate);
        
        // Group appointments by date
        $appointmentsByDate = [];
        foreach ($appointments as $appointment) {
            $date = date('Y-m-d', strtotime($appointment['appointment_date']));
            if (!isset($appointmentsByDate[$date])) {
                $appointmentsByDate[$date] = [];
            }
            $appointmentsByDate[$date][] = $appointment;
        }
        
        // Get patients for the new appointment form
        $patientModel = new \App\Models\Patient();
        $patients = $patientModel->getAll();
        
        $data = [
            'title' => 'Appointment Calendar',
            'currentDate' => $currentDate,
            'appointmentsByDate' => $appointmentsByDate,
            'patients' => $patients
        ];
        
        $this->render('pages/appointments/calendar', $data);
    }
    
    /**
     * Get appointments for calendar (AJAX)
     */
    public function getCalendarEvents()
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            $this->json(['success' => false, 'message' => 'Unauthorized'], 401);
            return;
        }
        
        // Get date range from query string
        $start = $_GET['start'] ?? date('Y-m-d');
        $end = $_GET['end'] ?? date('Y-m-d', strtotime('+30 days'));
        
        // Get appointments within date range
        $appointments = $this->appointmentModel->getAppointmentsByDateRange($start, $end);
        
        // Format appointments for FullCalendar
        $events = [];
        foreach ($appointments as $appointment) {
            $startDateTime = new \DateTime($appointment['appointment_date']);
            $endDateTime = clone $startDateTime;
            $endDateTime->modify("+{$appointment['duration_minutes']} minutes");
            
            $patientName = $appointment['first_name'] . ' ' . $appointment['last_name'];
            
            // Set color based on status
            $color = '#3788d8'; // Default blue
            switch ($appointment['status']) {
                case 'completed':
                    $color = '#28a745'; // Green
                    break;
                case 'cancelled':
                    $color = '#dc3545'; // Red
                    break;
                case 'no_show':
                    $color = '#ffc107'; // Yellow
                    break;
            }
            
            $events[] = [
                'id' => $appointment['id'],
                'title' => $patientName,
                'start' => $startDateTime->format('Y-m-d\TH:i:s'),
                'end' => $endDateTime->format('Y-m-d\TH:i:s'),
                'color' => $color,
                'extendedProps' => [
                    'status' => $appointment['status'],
                    'reason' => $appointment['reason_for_visit'] ?? '',
                    'patientId' => $appointment['patient_id']
                ]
            ];
        }
        
        $this->json($events);
    }
    
    /**
     * Get appointments for a specific date (AJAX)
     */
    public function getAppointmentsByDate()
    {
        // Check if user is logged in
        if (!is_logged_in()) {
            $this->json(['success' => false, 'message' => 'Unauthorized'], 401);
            return;
        }
        
        // Get date from query string
        $date = $_GET['date'] ?? date('Y-m-d');
        
        // Get appointments for the date
        $appointments = $this->appointmentModel->getAppointmentsByDate($date);
        
        // Format appointments for display
        $formattedAppointments = [];
        foreach ($appointments as $appointment) {
            $startDateTime = new \DateTime($appointment['appointment_date']);
            $patientName = $appointment['first_name'] . ' ' . $appointment['last_name'];
            
            $formattedAppointments[] = [
                'id' => $appointment['id'],
                'patient_name' => $patientName,
                'formatted_time' => $startDateTime->format('H:i'),
                'reason_for_visit' => $appointment['reason_for_visit'] ?? '',
                'status' => $appointment['status'],
                'duration_minutes' => $appointment['duration_minutes']
            ];
        }
        
        $this->json([
            'success' => true,
            'appointments' => $formattedAppointments
        ]);
    }
}
