<?php
namespace App\Core;

/**
 * Base Model class
 */
abstract class Model
{
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->db = Database::getInstance();
    }
    
    /**
     * Find a record by ID
     * 
     * @param string $id The ID to find
     * @return array|false The record or false if not found
     */
    public function find($id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = ?";
        return $this->db->fetchOne($sql, [$id]);
    }
    
    /**
     * Get all records
     * 
     * @param string $orderBy The column to order by
     * @param string $direction The direction to order (ASC or DESC)
     * @return array The records
     */
    public function all($orderBy = null, $direction = 'ASC')
    {
        $sql = "SELECT * FROM {$this->table}";
        
        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy} {$direction}";
        }
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * Find records by a specific field
     * 
     * @param string $field The field to search
     * @param mixed $value The value to search for
     * @return array The records
     */
    public function findBy($field, $value)
    {
        $sql = "SELECT * FROM {$this->table} WHERE {$field} = ?";
        return $this->db->fetchAll($sql, [$value]);
    }
    
    /**
     * Create a new record
     * 
     * @param array $data The data to insert
     * @return string The ID of the new record
     */
    public function create($data)
    {
        // Generate UUID if not provided
        if (!isset($data[$this->primaryKey]) && $this->primaryKey === 'id') {
            $data[$this->primaryKey] = $this->generateUuid();
        }
        
        $result = $this->db->insert($this->table, $data);
        
        // For UUID primary keys, return the generated UUID instead of lastInsertId
        if ($this->primaryKey === 'id' && isset($data[$this->primaryKey])) {
            return $data[$this->primaryKey];
        }
        
        return $result;
    }
    
    /**
     * Update a record
     * 
     * @param string $id The ID of the record to update
     * @param array $data The data to update
     * @return int The number of affected rows
     */
    public function update($id, $data)
    {
        return $this->db->update($this->table, $data, "{$this->primaryKey} = ?", [$id]);
    }
    
    /**
     * Delete a record
     * 
     * @param string $id The ID of the record to delete
     * @return int The number of affected rows
     */
    public function delete($id)
    {
        return $this->db->delete($this->table, "{$this->primaryKey} = ?", [$id]);
    }
    
    /**
     * Count records
     * 
     * @param string $where The WHERE clause
     * @param array $params The parameters for the WHERE clause
     * @return int The number of records
     */
    public function count($where = '', $params = [])
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table}";
        
        if ($where) {
            $sql .= " WHERE {$where}";
        }
        
        $result = $this->db->fetchOne($sql, $params);
        return (int) $result['count'];
    }
    
    /**
     * Generate a UUID v4
     * 
     * @return string The UUID
     */
    protected function generateUuid()
    {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
}
