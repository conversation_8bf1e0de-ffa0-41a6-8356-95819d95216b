<?php
namespace App\Core;

/**
 * Database class for handling database connections
 */
class Database
{
    private static $instance = null;
    private $connection;
    
    /**
     * Constructor - creates a new PDO connection
     */
    private function __construct()
    {
        try {
            $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';port=' . DB_PORT . ';charset=utf8mb4';
            $options = [
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
                \PDO::ATTR_EMULATE_PREPARES => false,
            ];
            
            $this->connection = new \PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (\PDOException $e) {
            throw new \Exception("Database connection failed: " . $e->getMessage());
        }
    }
    
    /**
     * Get the database instance (Singleton pattern)
     * 
     * @return Database
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        
        return self::$instance;
    }
    
    /**
     * Get the PDO connection
     * 
     * @return \PDO
     */
    public function getConnection()
    {
        return $this->connection;
    }
    
    /**
     * Execute a SQL query
     * 
     * @param string $sql The SQL query
     * @param array $params The parameters for the query
     * @return \PDOStatement
     */
    public function query($sql, $params = [])
    {
        $stmt = $this->connection->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    }
    
    /**
     * Get a single record
     * 
     * @param string $sql The SQL query
     * @param array $params The parameters for the query
     * @return array|false The record or false if not found
     */
    public function fetchOne($sql, $params = [])
    {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * Get multiple records
     * 
     * @param string $sql The SQL query
     * @param array $params The parameters for the query
     * @return array The records
     */
    public function fetchAll($sql, $params = [])
    {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * Insert a record and return the last insert ID
     * 
     * @param string $table The table name
     * @param array $data The data to insert
     * @return string The last insert ID
     */
    public function insert($table, $data)
    {
        $columns = implode(', ', array_keys($data));
        $placeholders = implode(', ', array_fill(0, count($data), '?'));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $this->query($sql, array_values($data));
        
        return $this->connection->lastInsertId();
    }
    
    /**
     * Update a record
     * 
     * @param string $table The table name
     * @param array $data The data to update
     * @param string $where The WHERE clause
     * @param array $params The parameters for the WHERE clause
     * @return int The number of affected rows
     */
    public function update($table, $data, $where, $params = [])
    {
        $set = [];
        foreach ($data as $column => $value) {
            $set[] = "{$column} = ?";
        }
        $set = implode(', ', $set);
        
        $sql = "UPDATE {$table} SET {$set} WHERE {$where}";
        $stmt = $this->query($sql, array_merge(array_values($data), $params));
        
        return $stmt->rowCount();
    }
    
    /**
     * Delete a record
     * 
     * @param string $table The table name
     * @param string $where The WHERE clause
     * @param array $params The parameters for the WHERE clause
     * @return int The number of affected rows
     */
    public function delete($table, $where, $params = [])
    {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        $stmt = $this->query($sql, $params);
        
        return $stmt->rowCount();
    }
    
    /**
     * Prepare a SQL statement
     * 
     * @param string $sql The SQL statement
     * @return \PDOStatement The prepared statement
     */
    public function prepare($sql)
    {
        return $this->connection->prepare($sql);
    }
}
