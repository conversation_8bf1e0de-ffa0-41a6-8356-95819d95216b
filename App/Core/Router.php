<?php
namespace App\Core;

/**
 * Router class for handling URL routing
 */
class Router
{
    private $routes = [];
    private $params = [];
    private $notFoundHandler = null;

    /**
     * Add a route to the routing table
     * 
     * @param string $route  The route URL
     * @param array  $params Parameters (controller, action, etc.)
     * @return void
     */
    public function add($route, $params = [])
    {
        // Convert the route to a regular expression
        $route = preg_replace('/\//', '\\/', $route);
        $route = preg_replace('/\{([a-z]+)\}/', '(?P<\1>[^\/]+)', $route);
        $route = '/^' . $route . '$/i';

        $this->routes[$route] = $params;
    }

    /**
     * Match the route to the routes in the routing table
     * 
     * @param string $url The route URL
     * @return boolean  true if a match found, false otherwise
     */
    public function match($url)
    {
        foreach ($this->routes as $route => $params) {
            if (preg_match($route, $url, $matches)) {
                foreach ($matches as $key => $match) {
                    if (is_string($key)) {
                        $params[$key] = $match;
                    }
                }
                $this->params = $params;
                return true;
            }
        }
        return false;
    }

    /**
     * Dispatch the route
     * 
     * @param string $url The route URL
     * @return void
     */
    public function dispatch($url = null)
    {
        $url = $url ?? $this->getUrl();
        $url = $this->removeQueryStringVariables($url);

        if ($this->match($url)) {
            $controller = $this->getControllerName();
            $controller = $this->convertToStudlyCaps($controller);
            $controller = "App\\Controllers\\$controller";

            if (class_exists($controller)) {
                $controllerObject = new $controller($this->params);

                $action = $this->params['action'] ?? 'index';
                $action = $this->convertToCamelCase($action);

                if (method_exists($controllerObject, $action)) {
                    // Extract action parameters from the URL parameters
                    $actionParams = [];
                    
                    // Get the reflection method to check parameters
                    $reflection = new \ReflectionMethod($controllerObject, $action);
                    $methodParams = $reflection->getParameters();
                    
                    // Match URL parameters to method parameters by name
                    foreach ($methodParams as $param) {
                        $paramName = $param->getName();
                        if (isset($this->params[$paramName])) {
                            $actionParams[] = $this->params[$paramName];
                        } elseif ($param->isOptional()) {
                            $actionParams[] = $param->getDefaultValue();
                        } else {
                            // Parameter required but not found in URL
                            throw new \Exception("Required parameter '$paramName' for action '$action' not provided in URL");
                        }
                    }
                    
                    // Call the action with the extracted parameters
                    call_user_func_array([$controllerObject, $action], $actionParams);
                } else {
                    throw new \Exception("Method $action in controller $controller not found");
                }
            } else {
                throw new \Exception("Controller class $controller not found");
            }
        } else {
            if ($this->notFoundHandler) {
                call_user_func($this->notFoundHandler);
            } else {
                header('HTTP/1.1 404 Not Found');
                echo '404 Page not found';
            }
        }
    }

    /**
     * Set a 404 not found handler
     * 
     * @param callable $handler The function to call when a route is not found
     * @return void
     */
    public function setNotFoundHandler($handler)
    {
        $this->notFoundHandler = $handler;
    }

    /**
     * Get the currently matched parameters
     * 
     * @return array
     */
    public function getParams()
    {
        return $this->params;
    }

    /**
     * Get the controller name from the parameters
     * 
     * @return string
     */
    protected function getControllerName()
    {
        return !empty($this->params['controller']) ? $this->params['controller'] : 'Home';
    }

    /**
     * Get the URL from the server
     * 
     * @return string The URL
     */
    protected function getUrl()
    {
        $url = $_SERVER['REQUEST_URI'] ?? '/';
        $url = parse_url($url, PHP_URL_PATH);
        return $url;
    }

    /**
     * Remove query string variables from the URL
     * 
     * @param string $url The URL
     * @return string The URL without query string variables
     */
    protected function removeQueryStringVariables($url)
    {
        if ($url !== '') {
            $parts = explode('?', $url, 2);
            $url = $parts[0];
        }
        return $url;
    }

    /**
     * Convert the string with hyphens to StudlyCaps,
     * e.g. post-authors => PostAuthors
     * 
     * @param string $string The string to convert
     * @return string
     */
    protected function convertToStudlyCaps($string)
    {
        return str_replace(' ', '', ucwords(str_replace('-', ' ', $string)));
    }

    /**
     * Convert the string with hyphens to camelCase,
     * e.g. add-new => addNew
     * 
     * @param string $string The string to convert
     * @return string
     */
    protected function convertToCamelCase($string)
    {
        return lcfirst($this->convertToStudlyCaps($string));
    }
}
