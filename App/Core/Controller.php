<?php
namespace App\Core;

/**
 * Base Controller class
 */
abstract class Controller
{
    protected $params = [];
    
    /**
     * Constructor
     * 
     * @param array $params Route parameters
     */
    public function __construct($params = [])
    {
        $this->params = $params;
    }
    
    /**
     * Render a view
     * 
     * @param string $view The view file
     * @param array $data Data to pass to the view
     * @return void
     */
    protected function render($view, $data = [])
    {
        // Extract data to make variables available in the view
        extract($data);
        
        // Start output buffering
        ob_start();
        
        // Include the view file
        $viewFile = APP_ROOT . "/App/views/{$view}.php";
        if (file_exists($viewFile)) {
            require_once $viewFile;
        } else {
            throw new \Exception("View {$view} not found");
        }
        
        // Get the content of the buffer
        $content = ob_get_clean();
        
        // Include the layout
        $layout = $data['layout'] ?? 'default';
        $layoutFile = APP_ROOT . "/App/views/layouts/{$layout}.php";
        
        if (file_exists($layoutFile)) {
            require_once $layoutFile;
        } else {
            echo $content;
        }
    }
    
    /**
     * Redirect to another URL
     * 
     * @param string $url The URL to redirect to
     * @return void
     */
    protected function redirect($url)
    {
        header('Location: ' . $url);
        exit;
    }
    
    /**
     * Return JSON response
     * 
     * @param mixed $data The data to return
     * @param int $statusCode HTTP status code
     * @return void
     */
    protected function json($data, $statusCode = 200)
    {
        header('Content-Type: application/json');
        http_response_code($statusCode);
        echo json_encode($data);
        exit;
    }
    
    /**
     * Get POST data
     * 
     * @param string $key The key to get
     * @param mixed $default Default value if key doesn't exist
     * @return mixed
     */
    protected function post($key = null, $default = null)
    {
        if ($key === null) {
            return $_POST;
        }
        
        return $_POST[$key] ?? $default;
    }
    
    /**
     * Get GET data
     * 
     * @param string $key The key to get
     * @param mixed $default Default value if key doesn't exist
     * @return mixed
     */
    protected function get($key = null, $default = null)
    {
        if ($key === null) {
            return $_GET;
        }
        
        return $_GET[$key] ?? $default;
    }
    
    /**
     * Check if the request is an AJAX request
     * 
     * @return bool
     */
    protected function isAjax()
    {
        return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
}
