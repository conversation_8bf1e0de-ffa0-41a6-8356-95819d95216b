<?php
/**
 * AI Doctor - Clinical Decision Support System
 * Helper functions
 */

/**
 * Get the base URL of the application
 * 
 * @return string The base URL
 */
function base_url($path = '')
{
    return APP_URL . '/' . ltrim($path, '/');
}

/**
 * Get the URL for an asset
 * 
 * @param string $path The path to the asset
 * @return string The asset URL
 */
function asset($path)
{
    return base_url('resources/' . ltrim($path, '/'));
}

/**
 * Escape HTML special characters
 * 
 * @param string $string The string to escape
 * @param string $encoding The character encoding to use
 * @return string The escaped string
 */
function e($string, $encoding = 'UTF-8')
{
    if ($string === null) {
        return '';
    }
    return htmlspecialchars((string)$string, ENT_QUOTES, $encoding);
}

/**
 * Generate a CSRF token
 * 
 * @return string The CSRF token
 */
function csrf_token()
{
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    
    return $_SESSION['csrf_token'];
}

/**
 * Check if the CSRF token is valid
 * 
 * @param string $token The token to check
 * @return bool True if valid, false otherwise
 */
function csrf_check($token)
{
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Generate a CSRF field for forms
 * 
 * @return string HTML input field with CSRF token
 */
function csrf_field()
{
    return '<input type="hidden" name="csrf_token" value="' . csrf_token() . '">';
}

/**
 * Flash a message to the session
 * 
 * @param string $type The type of message (success, error, info, warning)
 * @param string $message The message
 * @return void
 */
function flash($type, $message)
{
    $_SESSION['flash'][$type] = $message;
}

/**
 * Get flash messages and clear them from the session
 * 
 * @return array The flash messages
 */
function get_flashes()
{
    $flashes = $_SESSION['flash'] ?? [];
    unset($_SESSION['flash']);
    
    return $flashes;
}

/**
 * Check if the user is logged in
 * 
 * @return bool True if logged in, false otherwise
 */
function is_logged_in()
{
    return isset($_SESSION['user_id']);
}

/**
 * Get the current user ID
 * 
 * @return string|null The user ID or null if not logged in
 */
function current_user_id()
{
    return $_SESSION['user_id'] ?? null;
}

/**
 * Check if the current user is an admin
 * 
 * @return bool True if admin, false otherwise
 */
function isAdmin()
{
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

/**
 * Check if the current user is a doctor
 * 
 * @return bool True if doctor, false otherwise
 */
function isDoctor()
{
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'doctor';
}

/**
 * Format a date
 * 
 * @param string $date The date to format
 * @param string $format The format to use
 * @return string The formatted date
 */
function format_date($date, $format = 'd-m-Y')
{
    return date($format, strtotime($date));
}

/**
 * Format a date and time
 * 
 * @param string $datetime The datetime to format
 * @param string $format The format to use
 * @return string The formatted datetime
 */
function formatDateTime($datetime, $format = 'd-m-Y H:i')
{
    if (empty($datetime)) {
        return '';
    }
    return date($format, strtotime($datetime));
}

/**
 * Generate a UUID v4
 * 
 * @return string The UUID
 */
function generate_uuid()
{
    return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
        mt_rand(0, 0xffff), mt_rand(0, 0xffff),
        mt_rand(0, 0xffff),
        mt_rand(0, 0x0fff) | 0x4000,
        mt_rand(0, 0x3fff) | 0x8000,
        mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
    );
}

/**
 * Redirect to another URL
 * 
 * @param string $url The URL to redirect to
 * @return void
 */
function redirect($url)
{
    // Always use absolute URLs to prevent path duplication
    if (!preg_match('/^https?:\/\//', $url)) {
        $url = site_url($url);
    }
    header('Location: ' . $url);
    exit;
}

/**
 * Get the current URL
 * 
 * @return string The current URL
 */
function current_url()
{
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    return $protocol . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
}

/**
 * Check if the current URL matches a given path
 * 
 * @param string $path The path to check
 * @return bool True if matches, false otherwise
 */
function is_active($path)
{
    $current_path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    return $current_path === '/' . ltrim($path, '/');
}

/**
 * Truncate a string
 * 
 * @param string $string The string to truncate
 * @param int $length The maximum length
 * @param string $append The string to append if truncated
 * @return string The truncated string
 */
function str_truncate($string, $length = 100, $append = '...')
{
    if (strlen($string) <= $length) {
        return $string;
    }
    
    return substr($string, 0, $length) . $append;
}

/**
 * Get the URL for a site path
 * 
 * @param string $path The path
 * @return string The full URL
 */
function site_url($path = '')
{
    // Check if we're already in a controller path to prevent duplication
    $current_path = $_SERVER['REQUEST_URI'] ?? '';
    $current_segments = explode('/', trim($current_path, '/'));
    $path_segments = explode('/', trim($path, '/'));
    
    // If the first segment of the requested path is already in the current URL path,
    // make sure we're using an absolute path to prevent duplication
    if (!empty($current_segments) && !empty($path_segments) && 
        $current_segments[0] === $path_segments[0]) {
        // Use absolute path from the root
        return APP_URL . '/' . ltrim($path, '/');
    }
    
    return base_url($path);
}

/**
 * Calculate age from date of birth
 * 
 * @param string $date_of_birth Date of birth in any format accepted by strtotime()
 * @return int The calculated age
 */
function calculate_age($date_of_birth)
{
    $dob = new DateTime($date_of_birth);
    $now = new DateTime();
    $interval = $now->diff($dob);
    return $interval->y;
}

/**
 * Check if the current request is an AJAX request
 * 
 * @return bool True if it's an AJAX request, false otherwise
 */
function isAjaxRequest()
{
    return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
        strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}
