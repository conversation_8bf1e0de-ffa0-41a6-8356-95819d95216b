-- Travel Protocol Database Updates

-- Add new fields to travel_consultations table
ALTER TABLE travel_consultations 
ADD COLUMN age VARCHAR(10) AFTER departure_date,
ADD COLUMN gender ENUM('male', 'female') AFTER age,
ADD COLUMN allergies TEXT AFTER pregnancy_status,
ADD COLUMN doctor_id CHAR(36) AFTER vaccination_plan,
ADD COLUMN notes TEXT AFTER doctor_id,
ADD COLUMN status ENUM('in_progress', 'completed') DEFAULT 'in_progress' AFTER notes,
ADD COLUMN step INT DEFAULT 1 AFTER status;

-- Add foreign key for doctor_id
ALTER TABLE travel_consultations
ADD CONSTRAINT fk_travel_consultations_doctor
FOREI<PERSON><PERSON> KEY (doctor_id) REFERENCES users(id) ON DELETE SET NULL;

-- Create index for doctor_id
CREATE INDEX idx_travel_consultations_doctor ON travel_consultations(doctor_id);
