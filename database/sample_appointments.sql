-- Sample appointments for calendar testing
-- Insert appointments for August 2025 to match the screenshot

-- Insert sample appointments for August 2025 using actual patient IDs
INSERT INTO appointments (id, patient_id, appointment_date, duration_minutes, status, reason_for_visit, notes, created_at, updated_at) VALUES
-- August 12, 2025 appointments
(UUID(), (SELECT id FROM patients WHERE email = '<EMAIL>' LIMIT 1), '2025-08-12 09:00:00', 30, 'scheduled', 'Travel consultation for Thailand trip', 'Patient planning 2-week vacation', NOW(), NOW()),
(UUID(), (SELECT id FROM patients WHERE email = '<EMAIL>' LIMIT 1), '2025-08-12 14:30:00', 45, 'scheduled', 'Follow-up consultation', 'Check vaccination status', NOW(), NOW()),

-- August 13, 2025 appointments  
(UUID(), (SELECT id FROM patients WHERE email = '<EMAIL>' LIMIT 1), '2025-08-13 10:15:00', 30, 'scheduled', 'STD testing consultation', 'Routine screening', NOW(), NOW()),
(UUID(), (SELECT id FROM patients WHERE email = '<EMAIL>' LIMIT 1), '2025-08-13 15:00:00', 60, 'scheduled', 'General health checkup', 'Annual physical exam', NOW(), NOW()),

-- August 14, 2025 appointments
(UUID(), (SELECT id FROM patients WHERE email = '<EMAIL>' LIMIT 1), '2025-08-14 11:00:00', 30, 'scheduled', 'Travel medicine consultation', 'Business trip to India', NOW(), NOW()),

-- August 15, 2025 appointments
(UUID(), (SELECT id FROM patients WHERE email = '<EMAIL>' LIMIT 1), '2025-08-15 09:30:00', 45, 'scheduled', 'Follow-up appointment', 'Review test results', NOW(), NOW()),
(UUID(), (SELECT id FROM patients WHERE email = '<EMAIL>' LIMIT 1), '2025-08-15 13:45:00', 30, 'scheduled', 'Vaccination appointment', 'Hepatitis A/B vaccines', NOW(), NOW()),
(UUID(), (SELECT id FROM patients WHERE email = '<EMAIL>' LIMIT 1), '2025-08-15 16:00:00', 30, 'scheduled', 'Consultation', 'Pre-travel health advice', NOW(), NOW()),

-- August 19, 2025 appointments
(UUID(), (SELECT id FROM patients WHERE email = '<EMAIL>' LIMIT 1), '2025-08-19 10:00:00', 30, 'scheduled', 'Travel consultation', 'Africa safari trip planning', NOW(), NOW()),
(UUID(), (SELECT id FROM patients WHERE email = '<EMAIL>' LIMIT 1), '2025-08-19 14:00:00', 45, 'scheduled', 'General consultation', 'Health concerns discussion', NOW(), NOW()),

-- August 20, 2025 appointments
(UUID(), (SELECT id FROM patients WHERE email = '<EMAIL>' LIMIT 1), '2025-08-20 09:15:00', 30, 'scheduled', 'Follow-up visit', 'Post-vaccination check', NOW(), NOW()),

-- August 21, 2025 appointments
(UUID(), (SELECT id FROM patients WHERE email = '<EMAIL>' LIMIT 1), '2025-08-21 11:30:00', 60, 'scheduled', 'Comprehensive health assessment', 'Full medical evaluation', NOW(), NOW()),
(UUID(), (SELECT id FROM patients WHERE email = '<EMAIL>' LIMIT 1), '2025-08-21 15:30:00', 30, 'scheduled', 'Travel medicine', 'South America trip consultation', NOW(), NOW()),

-- August 22, 2025 appointments
(UUID(), (SELECT id FROM patients WHERE email = '<EMAIL>' LIMIT 1), '2025-08-22 10:45:00', 30, 'scheduled', 'STD testing', 'Quarterly screening', NOW(), NOW()),
(UUID(), (SELECT id FROM patients WHERE email = '<EMAIL>' LIMIT 1), '2025-08-22 13:00:00', 45, 'scheduled', 'Clinical consultation', 'Discuss symptoms', NOW(), NOW()),

-- Some completed and cancelled appointments for variety
(UUID(), (SELECT id FROM patients WHERE email = '<EMAIL>' LIMIT 1), '2025-08-08 09:00:00', 30, 'completed', 'Travel consultation', 'Completed successfully', NOW(), NOW()),
(UUID(), (SELECT id FROM patients WHERE email = '<EMAIL>' LIMIT 1), '2025-08-09 14:00:00', 45, 'completed', 'Health checkup', 'All tests normal', NOW(), NOW()),
(UUID(), (SELECT id FROM patients WHERE email = '<EMAIL>' LIMIT 1), '2025-08-10 11:00:00', 30, 'cancelled', 'Follow-up visit', 'Patient cancelled', NOW(), NOW());
