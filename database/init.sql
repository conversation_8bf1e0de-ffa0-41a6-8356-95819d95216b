-- AI Doctor Database Schema for MySQL
-- This script initializes the database with all necessary tables

-- Patients table
CREATE TABLE patients (
    id CHAR(36) PRIMARY KEY,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE,
    phone VARCHAR(20),
    date_of_birth DATE,
    gender ENUM('male', 'female'),
    address TEXT,
    emergency_contact_name VARCHAR(200),
    emergency_contact_phone VARCHAR(20),
    insurance_provider VARCHAR(200),
    insurance_policy_number VARCHAR(100),
    medical_record_number VARCHAR(50) UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Medical history table
CREATE TABLE medical_history (
    id CHAR(36) PRIMARY KEY,
    patient_id CHAR(36) NOT NULL,
    allergies TEXT,
    current_medications TEXT,
    chronic_conditions TEXT,
    previous_surgeries TEXT,
    family_history TEXT,
    smoking_status VARCHAR(50),
    alcohol_consumption VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE
);

-- Appointments table
CREATE TABLE appointments (
    id CHAR(36) PRIMARY KEY,
    patient_id CHAR(36) NOT NULL,
    appointment_date DATETIME NOT NULL,
    duration_minutes INTEGER DEFAULT 30,
    status ENUM('scheduled', 'completed', 'cancelled', 'no_show') DEFAULT 'scheduled',
    reason_for_visit TEXT,
    notes TEXT,
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE
);

-- Consultations table
CREATE TABLE consultations (
    id CHAR(36) PRIMARY KEY,
    patient_id CHAR(36) NOT NULL,
    appointment_id CHAR(36),
    consultation_type ENUM('travel_medicine', 'std_testing', 'general_qa') NOT NULL,
    status ENUM('in_progress', 'completed', 'cancelled') DEFAULT 'in_progress',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    ai_recommendations TEXT,
    doctor_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE,
    FOREIGN KEY (appointment_id) REFERENCES appointments(id) ON DELETE SET NULL
);

-- Travel medicine consultations
CREATE TABLE travel_consultations (
    id CHAR(36) PRIMARY KEY,
    consultation_id CHAR(36) NOT NULL,
    destination VARCHAR(200) NOT NULL,
    travel_type ENUM('business', 'tourism', 'backpacking', 'volunteering', 'visiting_family'),
    duration VARCHAR(100),
    departure_date DATE,
    pregnancy_status ENUM('pregnant', 'breastfeeding', 'planning_pregnancy', 'not_pregnant', 'not_applicable'),
    previous_vaccinations TEXT,
    risk_assessment TEXT,
    vaccination_plan TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (consultation_id) REFERENCES consultations(id) ON DELETE CASCADE
);

-- STD testing consultations
CREATE TABLE std_consultations (
    id CHAR(36) PRIMARY KEY,
    consultation_id CHAR(36) NOT NULL,
    symptoms TEXT,
    exposure_history TEXT,
    risk_factors TEXT,
    last_testing VARCHAR(200),
    recommended_tests TEXT,
    test_results TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (consultation_id) REFERENCES consultations(id) ON DELETE CASCADE
);

-- General Q&A consultations
CREATE TABLE qa_consultations (
    id CHAR(36) PRIMARY KEY,
    consultation_id CHAR(36) NOT NULL,
    question TEXT NOT NULL,
    ai_response TEXT,
    follow_up_questions TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (consultation_id) REFERENCES consultations(id) ON DELETE CASCADE
);

-- Vaccinations table
CREATE TABLE vaccinations (
    id CHAR(36) PRIMARY KEY,
    patient_id CHAR(36) NOT NULL,
    consultation_id CHAR(36),
    vaccine_name VARCHAR(200) NOT NULL,
    vaccine_type VARCHAR(100),
    administration_date DATE NOT NULL,
    expiry_date DATE,
    batch_number VARCHAR(100),
    administered_by VARCHAR(200),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE,
    FOREIGN KEY (consultation_id) REFERENCES consultations(id) ON DELETE SET NULL
);

-- Lab tests table
CREATE TABLE lab_tests (
    id CHAR(36) PRIMARY KEY,
    patient_id CHAR(36) NOT NULL,
    consultation_id CHAR(36),
    test_name VARCHAR(200) NOT NULL,
    test_type VARCHAR(100),
    order_date DATE NOT NULL,
    result_date DATE,
    results TEXT,
    reference_ranges TEXT,
    status VARCHAR(50) DEFAULT 'ordered',
    lab_facility VARCHAR(200),
    ordered_by VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE,
    FOREIGN KEY (consultation_id) REFERENCES consultations(id) ON DELETE SET NULL
);

-- Documents table for storing patient files
CREATE TABLE documents (
    id CHAR(36) PRIMARY KEY,
    patient_id CHAR(36) NOT NULL,
    consultation_id CHAR(36),
    document_name VARCHAR(255) NOT NULL,
    document_type VARCHAR(100),
    file_path VARCHAR(500),
    file_size INTEGER,
    mime_type VARCHAR(100),
    uploaded_by VARCHAR(200),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE,
    FOREIGN KEY (consultation_id) REFERENCES consultations(id) ON DELETE SET NULL
);

-- Audit log table
CREATE TABLE audit_log (
    id CHAR(36) PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL,
    record_id CHAR(36) NOT NULL,
    action VARCHAR(50) NOT NULL, -- INSERT, UPDATE, DELETE
    old_values JSON,
    new_values JSON,
    changed_by VARCHAR(200),
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Users table for authentication
CREATE TABLE users (
    id CHAR(36) PRIMARY KEY,
    username VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    role ENUM('admin', 'doctor', 'nurse', 'receptionist') NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_patients_email ON patients(email);
CREATE INDEX idx_patients_medical_record ON patients(medical_record_number);
CREATE INDEX idx_appointments_patient_date ON appointments(patient_id, appointment_date);
CREATE INDEX idx_consultations_patient ON consultations(patient_id);
CREATE INDEX idx_consultations_type ON consultations(consultation_type);
CREATE INDEX idx_travel_consultations_destination ON travel_consultations(destination);
CREATE INDEX idx_vaccinations_patient ON vaccinations(patient_id);
CREATE INDEX idx_lab_tests_patient ON lab_tests(patient_id);
CREATE INDEX idx_documents_patient ON documents(patient_id);

-- Insert sample data for testing
INSERT INTO patients (id, first_name, last_name, email, phone, date_of_birth, gender, medical_record_number) VALUES
(UUID(), 'John', 'Doe', '<EMAIL>', '+31612345678', '1985-06-15', 'male', 'MRN001'),
(UUID(), 'Jane', 'Smith', '<EMAIL>', '+31687654321', '1990-03-22', 'female', 'MRN002'),
(UUID(), 'Alice', 'Johnson', '<EMAIL>', '+31698765432', '1988-11-08', 'female', 'MRN003');

-- Insert sample users
INSERT INTO users (id, username, password, email, first_name, last_name, role) VALUES
(UUID(), 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'Admin', 'User', 'admin'),
(UUID(), 'doctor', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'Doctor', 'User', 'doctor');

-- Insert sample medical history
INSERT INTO medical_history (id, patient_id, allergies, current_medications, chronic_conditions) VALUES
(UUID(), (SELECT id FROM patients WHERE email = '<EMAIL>'), 'Penicillin allergy', 'None', 'Hypertension'),
(UUID(), (SELECT id FROM patients WHERE email = '<EMAIL>'), 'None known', 'Birth control pill', 'None'),
(UUID(), (SELECT id FROM patients WHERE email = '<EMAIL>'), 'Shellfish allergy', 'Vitamin D supplement', 'Asthma');

-- Insert sample appointments
INSERT INTO appointments (id, patient_id, appointment_date, reason_for_visit, status) VALUES
(UUID(), (SELECT id FROM patients WHERE email = '<EMAIL>'), '2024-12-15 10:00:00', 'Travel consultation for Thailand trip', 'scheduled'),
(UUID(), (SELECT id FROM patients WHERE email = '<EMAIL>'), '2024-12-16 14:30:00', 'STD testing consultation', 'scheduled'),
(UUID(), (SELECT id FROM patients WHERE email = '<EMAIL>'), '2024-12-17 09:15:00', 'General health questions', 'scheduled');
