/* Enhanced Form Styles for AI Doctor */

/* Input fields with improved focus states */
.form-input {
    display: block;
    width: 100%;
    border-radius: 0.375rem;
    border: 1px solid #d1d5db;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease-in-out;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

.form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
}

/* Custom select dropdowns */
.form-select {
    display: block;
    width: 100%;
    border-radius: 0.375rem;
    border: 1px solid #d1d5db;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding: 0.5rem 0.75rem;
    padding-right: 2.5rem;
    font-size: 0.875rem;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    transition: all 0.2s ease-in-out;
    appearance: none;
}

.form-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
}

/* Labels with improved styling */
.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.25rem;
}

/* Form groups with consistent spacing */
.form-group {
    margin-bottom: 1rem;
}

/* Progress steps */
.step-progress {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.step-circle {
    border-radius: 9999px;
    height: 2.5rem;
    width: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.2s ease-in-out;
}

.step-circle-active {
    background-color: #2563eb;
    transform: scale(1.1);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.2);
}

.step-circle-completed {
    background-color: #10b981;
}

.step-circle-inactive {
    background-color: #e5e7eb;
    color: #4b5563;
}

.step-title {
    font-size: 0.75rem;
    margin-top: 0.25rem;
    font-weight: 500;
}

.step-title-active {
    color: #2563eb;
}

.step-title-completed {
    color: #10b981;
}

.step-title-inactive {
    color: #6b7280;
}

.step-line {
    height: 0.25rem;
    flex-grow: 1;
    margin-left: 0.5rem;
    margin-right: 0.5rem;
    border-radius: 9999px;
    transition: all 0.2s ease-in-out;
}

.step-line-completed {
    background-color: #10b981;
}

.step-line-inactive {
    background-color: #e5e7eb;
}

/* Buttons with improved styling */
.btn {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

.btn-primary {
    background-color: #2563eb;
    color: white;
    border: none;
}

.btn-primary:hover {
    background-color: #1d4ed8;
}

.btn-secondary {
    background-color: white;
    border: 1px solid #d1d5db;
    color: #374151;
}

.btn-secondary:hover {
    background-color: #f9fafb;
}

/* Form sections */
.form-section {
    margin-bottom: 2rem;
}

.form-section-title {
    font-size: 1.125rem;
    font-weight: 500;
    color: #111827;
    margin-bottom: 1rem;
}

/* Form grid */
.form-grid {
    display: grid;
    grid-template-columns: repeat(1, minmax(0, 1fr));
    gap: 1.5rem 1rem;
}

@media (min-width: 640px) {
    .form-grid {
        grid-template-columns: repeat(6, minmax(0, 1fr));
    }
}

/* Checkbox and radio styling */
.form-checkbox, .form-radio {
    height: 1rem;
    width: 1rem;
    color: #2563eb;
    border: 1px solid #d1d5db;
}

.form-checkbox:focus, .form-radio:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
}

/* Help text */
.form-help {
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #6b7280;
}

/* Error states */
.form-input-error {
    border-color: #ef4444;
}

.form-input-error:focus {
    border-color: #ef4444;
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.25);
}

.form-error-message {
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc2626;
}

/* Success states */
.form-input-success {
    border-color: #10b981;
}

.form-input-success:focus {
    border-color: #10b981;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.25);
}

.form-success-message {
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #059669;
}

/* Card styling */
.form-card {
    background-color: white;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

@media (min-width: 640px) {
    .form-card {
        border-radius: 0.5rem;
    }
}

.form-card-header {
    padding: 1.25rem 1rem;
}

@media (min-width: 640px) {
    .form-card-header {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

.form-card-body {
    border-top: 1px solid #e5e7eb;
    padding: 1.25rem 1rem;
}

@media (min-width: 640px) {
    .form-card-body {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

.form-card-footer {
    border-top: 1px solid #e5e7eb;
    padding: 1rem;
}

@media (min-width: 640px) {
    .form-card-footer {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}
